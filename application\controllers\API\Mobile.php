<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsUsers $msusers
 * @property CI_Output $output
 * @property MobileSession $mobilesession
 * @property MobileNotification $mobilenotification
 * @property MsNotificationHandlerPackage $msnotificationhandlerpackage
 */
class Mobile extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MobileSession', 'mobilesession');
        $this->load->model('MobileNotification', 'mobilenotification');
        $this->load->model('MsNotificationHandlerPackage', 'msnotificationhandlerpackage');
    }

    public function auth_login()
    {
        $email = getPost('email');
        $password = getPost('password');

        if ($email == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Email tidak boleh kosong!'
            ));
        } else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Email tidak valid!'
            ));
        } else if ($password == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Password tidak boleh kosong!'
            ));
        }

        $user = $this->msusers->get(array(
            'email' => $email,
            'merchantid' => null,
            'role' => 'User',
        ));

        if ($user->num_rows() == 0) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Email atau password yang anda masukkan salah'
            ));
        }

        $user = $user->row();

        if (!password_verify($password, $user->password)) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Email atau password yang anda masukkan salah'
            ));
        }

        $data = array();
        $data['userid'] = $user->id;
        $data['token'] = stringEncryption('encrypt', json_encode(array(
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'createddate' => getCurrentDate(),
        )));
        $data['createddate'] = getCurrentDate();
        $data['createdby'] = $user->id;

        $mobilesession = $this->mobilesession->insert($data);

        if (!$mobilesession) {
            $this->output->set_status_header(500);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Login gagal'
            ));
        }

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Login berhasil',
            'data' => array(
                'token' => $data['token']
            )
        ));
    }

    private function validateToken($token)
    {
        if ($token == null) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $token = stringEncryption('decrypt', $token);

        if ($token == null) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $token = json_decode($token);

        if (json_last_error() != JSON_ERROR_NONE) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $validate = $this->mobilesession->total(array(
            'token' => getPost('token')
        ));

        if ($validate == 0) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $user = $this->msusers->get(array(
            'id' => $token->id,
        ));

        if ($user->num_rows() == 0) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $row = $user->row();

        return array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $row
        );
    }

    private function allowedPackage()
    {
        $allowed = array();
        foreach ($this->msnotificationhandlerpackage->result() as $key => $value) {
            if ($value->apps_id == null) continue;

            $allowed[] = $value->apps_id;
        }

        return $allowed;
    }

    public function notification_store()
    {
        $title = getPost('title');
        $text = getPost('text');
        $packagename = getPost('packagename');
        $token = getPost('token');

        if ($title == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Judul tidak boleh kosong!'
            ));
        } else if ($text == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Text tidak boleh kosong!'
            ));
        } else if ($packagename == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Package Name tidak boleh kosong!'
            ));
        } else {
            if (!in_array($packagename, $this->allowedPackage())) {
                $this->output->set_status_header(400);
                $this->output->set_content_type('application/json');

                return JSONResponse(array(
                    'status' => false,
                    'message' => 'Package Name tidak diperbolehkan!'
                ));
            }
        }

        $validate = $this->validateToken($token);

        if (!$validate['status']) {
            $this->output->set_status_header(401);
            $this->output->set_content_type('application/json');

            return JSONResponse($validate);
        }

        $insert = array();
        $insert['userid'] = $validate['data']->id;
        $insert['title'] = $title;
        $insert['description'] = $text;
        $insert['packagename'] = $packagename;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = $validate['data']->id;

        $notification = $this->mobilenotification->insert($insert);

        if (!$notification) {
            $this->output->set_status_header(500);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Gagal mengirim notifikasi'
            ));
        }

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Notifikasi berhasil dikirim'
        ));
    }

    public function auth_logout()
    {
        $token = getPost('token');

        $this->mobilesession->delete(array(
            'token' => $token
        ));

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Logout berhasil'
        ));
    }
}
