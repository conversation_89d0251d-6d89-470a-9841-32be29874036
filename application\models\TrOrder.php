<?php
defined('BASEPATH') or die('No direct script access allowed!');

class TrOrder extends MY_Model
{
    protected $table = 'trorder';
    protected $SearchDatatables = array(
        'a.clientcode'
    );

    public function QueryDatatables()
    {
        $this->db->select('a.*, b.productname, c.name AS buyername, b.vendor AS hostvendor')
            ->from($this->table . ' a')
            ->join('msproduct b', 'b.id = a.serviceid', 'LEFT')
            ->join('msusers c', 'c.id = a.userid', 'LEFT')
            ->order_by('a.id', 'DESC');

        return $this;
    }

    public function QueryDatatables_omsetprofit()
    {
        $this->db->select('DATE(a.createddate) as dateorder,SUM(a.price) as totalomset ,SUM(a.profit) as totalprofit')
            ->from($this->table . ' a')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->group_by('DATE(a.createddate)');

        return $this;
    }
}
