<?php
defined('BASEPATH') or exit('No direct script access allowed');

class DailyPanel
{
    private $_apikey;
    private $_pin;

    public function __construct($apikey, $pin)
    {
        $this->_apikey = $apikey;
        $this->_pin = $pin;
    }

    public function services()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://www.daily-panel.com/api/request");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "pin=$this->_pin&api_key=$this->_apikey&action=services");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function order($service, $target, $quantity, $other = null)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://www.daily-panel.com/api/request");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "pin=$this->_pin&api_key=$this->_apikey&action=order&service=$service&target=$target&quantity=$quantity&custom_comment=$other&custom_link=$other");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function status($id)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://www.daily-panel.com/api/request");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "pin=$this->_pin&api_key=$this->_apikey&action=status&order_id=$id");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function profile()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://www.daily-panel.com/api/request");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "pin=$this->_pin&api_key=$this->_apikey&action=profile");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }
}
