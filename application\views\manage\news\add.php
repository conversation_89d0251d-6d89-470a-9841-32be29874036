<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Tambah Berita</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Management</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Berita</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Tambah Berita</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <a href="<?= base_url('manage/news') ?>" class="btn btn-danger fw-bold">Kembali</a>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <form id="frmAddNews" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div id="apps_display" class="d-none">
                            <div class="mb-7">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Judul Berita</label>
                                <input type="text" class="form-control form-control-solid" name="judul_berita" placeholder="Judul Berita" />
                            </div>

                            <div class="mb-7">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Gambar</label>
                                <input class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" type="file" name="picture" id="picture" accept=".png, .jpg, .jpeg">

                                <div class="mt-2">
                                    <small>*Allowed types: <b>.jpg, .jpeg, .png</b></small>
                                    <br>
                                    <small>*Ukuran File maksimal: 2MB</small>
                                    <br>
                                    <small>*Gambar bersifat opsional</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Tipe Berita</label>
                            <select name="type" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                <option value="">- Pilih -</option>
                                <?php foreach ($newstype as $key => $value) : ?>
                                    <option value="<?= $value->id ?>"><?= $value->name ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <?php if (!isAdmin()) : ?>
                            <div class="form-check form-check-custom form-check-solid mb-7">
                                <input class="form-check-input" type="checkbox" name="apps" value="1" id="apps" />

                                <label class="form-check-label" for="apps">
                                    Tampilkan di Aplikasi
                                </label>
                            </div>
                        <?php endif; ?>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Konten</label>
                            <textarea id="input_content" placeholder="Masukkan Konten"></textarea>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    var editor;

    window.onload = function() {
        ClassicEditor
            .create(document.querySelector('#input_content'))
            .then(response => {
                editor = response;
            })
            .catch(responseError => {

            });

        $('#frmAddNews').submit(function(e) {
            e.preventDefault();

            let formData = new FormData(this);
            formData.append('content', editor.data.get());

            let elementsForm = $(this).find('button, textarea, input');
            elementsForm.attr('disabled');

            $.ajax({
                url: $(this).attr('action'),
                method: $(this).attr('method'),
                dataType: 'json',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    elementsForm.removeAttr('disabled');

                    if (response.RESULT == 'OK') {
                        return Swal.fire({
                            title: 'Berhasil',
                            text: response.MESSAGE,
                            icon: 'success'
                        }).then(function(result) {
                            return window.location.href = '<?= base_url('manage/news') ?>';

                        });
                    } else {
                        return Swal.fire({
                            title: 'Gagal',
                            text: response.MESSAGE,
                            icon: 'error'
                        });
                    }
                }
            }).fail(function() {
                elementsForm.removeAttr('disabled');

                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error'
                });
            });
        });

        $('#apps').change(function() {
            if ($(this).prop('checked') == true) {
                $('#apps_display').removeClass('d-none');
            } else {
                $('#apps_display').addClass('d-none');
            }
        });
    };
</script>