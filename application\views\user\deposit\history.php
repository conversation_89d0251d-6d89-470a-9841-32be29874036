<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Riwayat Topup</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Member</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Riwayat Topup</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <!-- Statistics Cards -->
            <div class="row g-5 mb-7">
                <div class="col-xl-4">
                    <div class="card card-flush h-xl-100" style="background: linear-gradient(135deg, #1BC5BD 0%, #0BB7AF 100%);">
                        <div class="card-body d-flex align-items-center">
                            <div class="symbol symbol-60px me-5">
                                <div class="symbol-label bg-white bg-opacity-20">
                                    <i class="bi bi-check-circle fs-2x text-white"></i>
                                </div>
                            </div>
                            <div class="d-flex flex-column flex-grow-1">
                                <span class="text-white fs-7 fw-semibold mb-1">Nominal Deposit Berhasil</span>
                                <span class="text-white fs-2 fw-bold" id="successful-amount">Rp 0</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-4">
                    <div class="card card-flush h-xl-100" style="background: linear-gradient(135deg, #FFA800 0%, #FF8F00 100%);">
                        <div class="card-body d-flex align-items-center">
                            <div class="symbol symbol-60px me-5">
                                <div class="symbol-label bg-white bg-opacity-20">
                                    <i class="bi bi-clock fs-2x text-white"></i>
                                </div>
                            </div>
                            <div class="d-flex flex-column flex-grow-1">
                                <span class="text-white fs-7 fw-semibold mb-1">Nominal Deposit Menunggu</span>
                                <span class="text-white fs-2 fw-bold" id="pending-amount">Rp 0</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-4">
                    <div class="card card-flush h-xl-100" style="background: linear-gradient(135deg, #F64E60 0%, #E1306C 100%);">
                        <div class="card-body d-flex align-items-center">
                            <div class="symbol symbol-60px me-5">
                                <div class="symbol-label bg-white bg-opacity-20">
                                    <i class="bi bi-x-circle fs-2x text-white"></i>
                                </div>
                            </div>
                            <div class="d-flex flex-column flex-grow-1">
                                <span class="text-white fs-7 fw-semibold mb-1">Nominal Deposit Gagal</span>
                                <span class="text-white fs-2 fw-bold" id="failed-amount">Rp 0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Card -->
            <div class="card mb-5">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="depositdate" class="col-form-label fw-semibold fs-6 pt-0">Tanggal Deposit</label>
                                <input type="text" name="depositdate" id="depositdate" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Tanggal" value="<?= date('m/01/Y') ?> - <?= date('m/t/Y') ?>">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="depositcode" class="col-form-label fw-semibold fs-6 pt-0">Kode Deposit</label>
                                <input type="text" name="depositcode" id="depositcode" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Kode Topup">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="paymenttype" class="col-form-label fw-semibold fs-6 pt-0">Metode Pembayaran</label>
                                <select name="paymenttype" id="paymenttype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                    <option value="">Pilih Metode Pembayaran</option>
                                    <option value="Otomatis">Otomatis</option>
                                    <option value="Manual">Manual</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="depositstatus" class="col-form-label fw-semibold fs-6 pt-0">Status Deposit</label>
                                <select name="depositstatus" id="depositstatus" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                    <option value="">Pilih Status</option>
                                    <option value="Pending">Menunggu</option>
                                    <option value="Success">Berhasil</option>
                                    <option value="Cancel">Gagal</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <button type="button" class="btn btn-dark w-100" onclick="filter()">Filter</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-5">
                <div class="card-body">
                    <table class="table table-striped table-row-bordered gy-5 datatables-history">
                        <thead>
                            <tr class="fw-semibold fs-6 text-muted">
                                <th>Tanggal</th>
                                <th>Kode Topup</th>
                                <th>Pembayaran</th>
                                <th>Jumlah Transfer</th>
                                <th>Saldo Yang Didapatkan</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>

                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('input[name="depositdate"]').daterangepicker();

        $('.datatables-history').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
                method: 'POST',
                data: {
                    date: $('#date').val()
                }
            }
        });

        // Load initial statistics
        loadStatistics();
    };

    function formatRupiah(amount) {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }

    function loadStatistics() {
        let depositdate = $('#depositdate').val();
        let depositcode = $('#depositcode').val();
        let paymenttype = $('#paymenttype').val();
        let depositstatus = $('#depositstatus').val();

        $.ajax({
            url: '<?= base_url('users/deposit/history/get_statistics') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                date: depositdate,
                code: depositcode,
                payment: paymenttype,
                status: depositstatus
            },
            success: function(response) {
                if (response.status) {
                    $('#successful-amount').text(formatRupiah(response.data.successful));
                    $('#pending-amount').text(formatRupiah(response.data.pending));
                    $('#failed-amount').text(formatRupiah(response.data.failed));
                }
            },
            error: function() {
                $('#successful-amount').text('Rp 0');
                $('#pending-amount').text('Rp 0');
                $('#failed-amount').text('Rp 0');
            }
        });
    }

    $('#filter').on('click', function() {
        let date = $('#date').val();
        let status = $('#status').val();

        $('.datatables-history').DataTable().destroy();
        $('.datatables-history').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
                method: 'POST',
                data: {
                    date: date,
                    status: status,
                    filter: true
                }
            }
        });
    });

    function confirmPayment(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Data akan dikonfirmasi, Saldo pengguna akan bertambah sesuai dengan nominal yang tertera',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/confirm') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function cancelPayment(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Permintaan deposit akan dibatalkan',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/cancel') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function wrongNominal(id) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/wrong/nominal') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        })
    }

    function filter() {
        let depositdate = $('#depositdate').val();
        let depositcode = $('#depositcode').val();
        let paymenttype = $('#paymenttype').val();
        let depositstatus = $('#depositstatus').val();

        // Update statistics
        loadStatistics();

        // Update datatable
        $('.datatables-history').DataTable().destroy();
        $('.datatables-history').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            ordering: false,
            ajax: {
                url: '<?= base_url('users/deposit/history/datatables') ?>',
                method: 'POST',
                data: {
                    date: depositdate,
                    code: depositcode,
                    payment: paymenttype,
                    status: depositstatus
                }
            }
        });
    }
</script>