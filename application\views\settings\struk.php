<?php
defined('BASEPATH') or exit('No direct script access allowed');

$decode = null;
if ($strukcontent != null) {
    $decode = json_decode($strukcontent);
}
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Konten Struk</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary"><PERSON><PERSON>a</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Akun</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Konten Struk</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row g-10 draggable-zone">
        <?php foreach (strukContent() as $key => $value) : ?>
            <div class="col-12 draggable">
                <div class="card card-bordered">
                    <div class="card-body d-flex justify-content-between">
                        <div class="form-check form-check-custom form-check-solid">
                            <input class="form-check-input" type="checkbox" value="1" id="<?= $key ?>" <?= $strukcontent == null ? 'checked' : (in_array($key, $decode) ? 'checked' : null) ?> />
                            <label class="form-check-label" for="<?= $key ?>">
                                <?= $value ?>
                            </label>
                        </div>

                        <a href="javascript:;" class="btn btn-icon btn-sm btn-hover-light-primary draggable-handle">
                            <!--begin::Svg Icon | path: icons/duotune/abstract/abs015.svg-->
                            <span class="svg-icon svg-icon-2x"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z" fill="currentColor" />
                                    <path opacity="0.3" d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14ZM22 20V18C22 17.4 21.6 17 21 17H3C2.4 17 2 17.4 2 18V20C2 20.6 2.4 21 3 21H21C21.6 21 22 20.6 22 20Z" fill="currentColor" />
                                </svg>
                            </span>
                            <!--end::Svg Icon-->
                        </a>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <div class="d-flex justify-content-end py-6">
        <button type="submit" class="btn btn-primary" onclick="saveStructure()">Simpan</button>
    </div>
</div>

<script>
    window.onload = function() {
        var containers = document.querySelectorAll(".draggable-zone");

        if (containers.length === 0) {
            return false;
        }

        var swappable = new Sortable.default(containers, {
            draggable: ".draggable",
            handle: ".draggable .draggable-handle",
            mirror: {
                appendTo: "body",
                constrainDimensions: true
            }
        });
    };

    function saveStructure() {
        var data = [];
        $('.draggable').each(function(index) {
            data.push({
                'id': $(this).find('input[type="checkbox"]').attr('id'),
                'checked': $(this).find('input[type="checkbox"]').is(':checked'),
                'order': index
            });
        });

        $.ajax({
            url: '<?= base_url(uri_string() . '/process') ?>',
            type: 'POST',
            dataType: 'json',
            data: {
                data: data
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil',
                        text: 'Struktur berhasil disimpan',
                    }).then((result) => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Gagal',
                        text: 'Struktur gagal disimpan',
                    });
                }
            }
        }).fail(function() {
            Swal.fire({
                icon: 'error',
                title: 'Gagal',
                text: 'Server sedang sibuk, silahkan coba lagi',
            });
        });
    }
</script>