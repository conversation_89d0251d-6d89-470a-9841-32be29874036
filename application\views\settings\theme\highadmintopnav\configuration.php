<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Konfigurasi Tema: HighAdmin (Top Navigation)</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmConfiguration" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <input type="hidden" name="id" value="<?= $id ?>">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">
                        Nama Singkatan Usaha

                        <a href="javascript:;" data-bs-toggle="popover" data-bs-placement="right" title="Nama Singkatan Usaha" data-bs-html="true" data-bs-content="<div>
<p class='m-0'>Teks ini ditampilkan pada saat ukuran layar mobile atau pada saat navbar ditutup.</p>
<p class='mb-2'>Contoh gambar:</p>
<img src='<?= base_url('assets/media/tutorial/highadmin-topnav/singkatan-usaha.png') ?>' class='w-100'>
</div>">
                            <i class="far fa-question-circle"></i>
                        </a>
                    </label>

                    <input type="text" name="companyabbr" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Singkatan Usaha (Depan)" value="<?= $config != null ? $config->companyabbr : null ?>" required>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">
                        Nama Usaha

                        <a href="javascript:;" data-bs-toggle="popover" data-bs-placement="right" title="Nama Usaha" data-bs-html="true" data-bs-content="<div>
<p class='m-0'>Teks ini ditampilkan pada saat halaman dashboard diakses menggunakan layar yang lebar atau pada saat navbar dibuka.</p>
<p class='mb-2'>Contoh gambar:</p>
<img src='<?= base_url('assets/media/tutorial/highadmin-topnav/nama-usaha.png') ?>' class='w-100'>
</div>">
                            <i class="far fa-question-circle"></i>
                        </a>
                    </label>

                    <input type="text" name="company" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Usaha (Depan)" value="<?= $config != null ? $config->company : null ?>" required>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    KTApp.init();

    $.AjaxRequest('#frmConfiguration', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success'
                }).then(function(result) {
                    return window.location.reload();

                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.RESULT,
                    icon: 'error'
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        }
    });
</script>