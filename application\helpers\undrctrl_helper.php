<?php
defined('BASEPATH') or die('No direct script access allowed!');

class UNDRCTRL
{
    private $_apikey;
    private $_public_key;

    public function __construct($apikey, $public_key = null)
    {
        $this->_apikey = $apikey;
        $this->_public_key = $public_key;
    }

    public function services()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://api-vendor.karpeldevtech.com/api/undrctrl/services");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "apikey=$this->_apikey&public_key=$this->_public_key");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function order($service, $link, $quantity, $runs = null, $interval = null)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://api-vendor.karpeldevtech.com/api/undrctrl/order");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "apikey=$this->_apikey&public_key=$this->_public_key&service=$service&link=$link&quantity=$quantity&runs=$runs&interval=$interval");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function status($order)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://api-vendor.karpeldevtech.com/api/undrctrl/status");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "apikey=$this->_apikey&public_key=$this->_public_key&order=$order");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function profile()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://api-vendor.karpeldevtech.com/api/undrctrl/profile");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "apikey=$this->_apikey&public_key=$this->_public_key");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }
}
