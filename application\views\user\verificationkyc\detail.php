<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!-- begin::Toolbar -->
<div class="toolbar mb-5 mb mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Detail Verifikasi KYC</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('users/verificationkyc') ?>" class="text-gray-600 text-hover-primary">Verifikasi KYC</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Detail</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!-- end::Toolbar -->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-header">
                    <h3 class="card-title">Informasi KYC</h3>
                    <div class="card-toolbar">
                        <a href="<?= base_url('users/verificationkyc') ?>" class="btn btn-sm btn-light-primary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row mb-5">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">Nama Lengkap</th>
                                    <td width="5%">:</td>
                                    <td><?= $kyc->name ?? '-' ?></td>
                                </tr>

                                <tr>
                                    <th>NIK</th>
                                    <td>:</td>
                                    <td><?= $kyc->nik ?? '-' ?></td>
                                </tr>

                                <tr>
                                    <th>Nama Ibu Kandung</th>
                                    <td>:</td>
                                    <td><?= $kyc->biologicalmother ?? '-' ?></td>
                                </tr>

                                <tr>
                                    <th>Status</th>
                                    <td>:</td>
                                    <td>
                                        <?php if ($kyc->status == 0): ?>
                                            <span class="badge badge-warning">Menunggu Verifikasi</span>
                                        <?php elseif ($kyc->status == 1): ?>
                                            <span class="badge badge-success">Disetujui</span>
                                        <?php elseif ($kyc->status == 2): ?>
                                            <span class="badge badge-danger">Ditolak</span>
                                        <?php else: ?>
                                            <span class="badge badge-secondary">Tidak Diketahui</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>

                                <tr>
                                    <th>Tanggal Pengajuan</th>
                                    <td>:</td>
                                    <td><?= date('d F Y H:i', strtotime($kyc->created_at ?? date('Y-m-d H:i:s'))) ?></td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">Username</th>
                                    <td width="5%">:</td>
                                    <td><?= $user->username ?? '-' ?></td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td>:</td>
                                    <td><?= $user->email ?? '-' ?></td>
                                </tr>
                                <tr>
                                    <th>No. Telepon</th>
                                    <td>:</td>
                                    <td><?= $user->phonenumber ?? '-' ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">Foto Wajah</h5>
                                </div>
                                <div class="card-body text-center">
                                    <?php if (!empty($kyc->facephoto)): ?>
                                        <img src="<?= base_url('live/demo/secure/kyc/image/' . $kyc->facephoto . '/facephoto?userid=' . stringEncryption('encrypt', getCurrentIdUser()) . '&sessions=' . stringEncryption('encrypt', json_encode(['id' => getCurrentIdUser(), 'date' => getCurrentDate()]))) ?>" alt="Foto Wajah" class="img-fluid rounded" style="max-height: 300px;">
                                    <?php else: ?>
                                        <div class="alert alert-warning">Foto tidak tersedia</div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">Foto KTP</h5>
                                </div>
                                <div class="card-body text-center">
                                    <?php if (!empty($kyc->identityphoto)): ?>
                                        <img src="<?= base_url('live/demo/secure/kyc/image/' . $kyc->identityphoto . '/identityphoto?userid=' . stringEncryption('encrypt', getCurrentIdUser()) . '&sessions=' . stringEncryption('encrypt', json_encode(['id' => getCurrentIdUser(), 'date' => getCurrentDate()]))) ?>" alt="Foto KTP" class="img-fluid rounded" style="max-height: 300px;">
                                    <?php else: ?>
                                        <div class="alert alert-warning">Foto tidak tersedia</div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">Foto Wajah dengan KTP</h5>
                                </div>
                                <div class="card-body text-center">
                                    <?php if (!empty($kyc->facenidentityphoto)): ?>
                                        <img src="<?= base_url('live/demo/secure/kyc/image/' . $kyc->facenidentityphoto . '/facenidentityphoto?userid=' . stringEncryption('encrypt', getCurrentIdUser()) . '&sessions=' . stringEncryption('encrypt', json_encode(['id' => getCurrentIdUser(), 'date' => getCurrentDate()]))) ?>" alt="Foto Wajah dengan KTP" class="img-fluid rounded" style="max-height: 300px;">
                                    <?php else: ?>
                                        <div class="alert alert-warning">Foto tidak tersedia</div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if ($kyc->status == 0): ?>
                        <div class="row mt-5">
                            <div class="col-md-12 text-center">
                                <button type="button" class="btn btn-danger me-3" onclick="rejectKYC('<?= $id_encrypted ?>')">
                                    <i class="fas fa-times-circle"></i> Tolak Verifikasi
                                </button>
                                <button type="button" class="btn btn-success" onclick="acceptKYC('<?= $id_encrypted ?>')">
                                    <i class="fas fa-check-circle"></i> Setujui Verifikasi
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function acceptKYC(id) {
        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin menyetujui verifikasi KYC ini?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Ya, Setujui',
            cancelButtonText: 'Batal',
            customClass: {
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-danger'
            },
            buttonsStyling: false
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url('users/verificationkyc/process_verificationkyc') ?>',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        id: id,
                        action: 'accept'
                    },
                    success: function(response) {
                        if (response.RESULT === 'OK') {
                            Swal.fire({
                                title: 'Berhasil',
                                text: 'Verifikasi KYC berhasil disetujui',
                                icon: 'success',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                },
                                buttonsStyling: false
                            }).then(() => {
                                window.location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE || 'Terjadi kesalahan',
                                icon: 'error',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                },
                                buttonsStyling: false
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Gagal',
                            text: 'Terjadi kesalahan pada server',
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            },
                            buttonsStyling: false
                        });
                    }
                });
            }
        });
    }

    function rejectKYC(id) {
        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin menolak verifikasi KYC ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Ya, Tolak',
            cancelButtonText: 'Batal',
            customClass: {
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-secondary'
            },
            buttonsStyling: false
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url('users/verificationkyc/process_verificationkyc') ?>',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        id: id,
                        action: 'reject'
                    },
                    success: function(response) {
                        if (response.RESULT === 'OK') {
                            Swal.fire({
                                title: 'Berhasil',
                                text: 'Verifikasi KYC berhasil ditolak',
                                icon: 'success',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                },
                                buttonsStyling: false
                            }).then(() => {
                                window.location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE || 'Terjadi kesalahan',
                                icon: 'error',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                },
                                buttonsStyling: false
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Gagal',
                            text: 'Terjadi kesalahan pada server',
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            },
                            buttonsStyling: false
                        });
                    }
                });
            }
        });
    }
</script>