<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Datatables $datatables
 * @property MsVendor $msvendor
 * @property MsVendorDetail $msvendordetail
 * @property MsProduct $msproduct
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 */
class Vendor extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsVendor', 'msvendor');
        $this->load->model('MsVendorDetail', 'msvendordetail');
        $this->load->model('MsProduct', 'msproduct');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Vendor';
        $data['content'] = 'manage/vendor/index';

        return $this->load->view('master', $data);
    }

    public function datatables_vendor()
    {
        try {
            if (isLogin() && (isAdmin() || (isUser() && getCurrentUser()->licenseid != null && getCurrentUser()->multivendor == 1))) {
                $data = array();

                $datatables = $this->datatables->make('MsVendor', 'QueryDatatables', 'SearchDatatables');

                $where = array();
                if (isUser()) {
                    $where['userid'] = getCurrentIdUser();
                } else {
                    $where['userid'] = null;
                }

                foreach ($datatables->getData($where) as $key => $value) {
                    $detail = array();
                    $detail[] = $value->name;
                    $detail[] = $value->currency;
                    $detail[] = $value->category;

                    $parameter = json_decode($value->parameter);

                    $li = '<ul>';
                    foreach ($parameter as $key => $v) {
                        $li .= '<li>' . $v . '</li>';
                    }
                    $li .= '</ul>';

                    $detail[] = $li;

                    if (isUser()) {
                        if ($value->default_config == null) {
                            $detail[] = "<span class=\"badge badge-light\">Belum dikonfigurasi</span>";
                        } else {
                            $detail[] = "<span class=\"badge badge-success\">Sudah dikonfigurasi</span>";
                        }

                        if ($value->isactive == 1) {
                            $detail[] = "<div class=\"form-check form-switch form-check-custom form-check-solid\">
                                <input class=\"form-check-input\" type=\"checkbox\" onchange=\"changeStatus('" . stringEncryption('encrypt', $value->id) . "')\" checked/>

                                <span class=\"form-check-label\">
                                    Aktif
                                </span>
                            </div>";
                        } else {
                            $detail[] = "<div class=\"form-check form-switch form-check-custom form-check-solid\">
                                <input class=\"form-check-input\" type=\"checkbox\" onchange=\"changeStatus('" . stringEncryption('encrypt', $value->id) . "')\"/>

                                <span class=\"form-check-label\">
                                    Tidak Aktif
                                </span>
                            </div>";
                        }
                    }

                    $detail[] = ($value->currency == 'IDR' ? 'Rp' : '$') . IDR($value->balance);

                    if (!isUser()) {
                        $detail[] = "<a href=\"" . base_url('manage/vendor/detail/' . $value->id) . "\" class=\"btn btn-icon btn-primary btn-sm mb-1\">
                        <i class=\"fa fa-eye\"></i>
                    </a>
                    
                    <a href=\"" . base_url('manage/vendor/edit/' . $value->id) . "\" class=\"btn btn-icon btn-warning btn-sm mb-1\">
                        <i class=\"fa fa-edit\"></i>
                    </a>

                    <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"deleteVendor('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-trash\"></i>
                    </a>";
                    } else {
                        $detail[] = "<a href=\"" . base_url('settings/apikey/detail/' . $value->id) . "\" class=\"btn btn-icon btn-primary btn-sm mb-1\">
                            <i class=\"fa fa-eye\"></i>
                        </a>

                        <button type=\"button\" class=\"btn btn-icon btn-warning btn-sm mb-1\" onclick=\"configurationVendor('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-cogs\"></i>
                        </button>
                        
                        <a href=\"" . base_url('settings/apikey/edit/' . $value->id) . "\" class=\"btn btn-icon btn-warning btn-sm mb-1\">
                            <i class=\"fa fa-edit\"></i>
                        </a>

                        <button type=\"button\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"deleteProduct('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-trash\"></i>
                            Hapus Produk
                        </button>

                        <a href=\"javascript:;\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"deleteVendor('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-trash\"></i>
                            Hapus Vendor
                        </a>";
                    }

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $e) {
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin() && !isUser()) {
            return redirect(base_url('dashboard'));
        } else if (isUser() && getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if (isUser() && getCurrentUser()->multivendor != 1) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tambah Vendor';
        $data['content'] = 'manage/vendor/add';

        return $this->load->view('master', $data);
    }

    public function process_add_vendor()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin() && !isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->multivendor != 1) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $nama = getPost('nama');
            $kategori = getPost('kategori');
            $parameter = getPost('parameter', array());
            $currency = getPost('currency');

            if ($nama == null) {
                throw new Exception('Nama tidak boleh kosong');
            } else if ($kategori == null) {
                throw new Exception('Kategori tidak boleh kosong');
            } else if (count($parameter) == 0) {
                throw new Exception('Parameter tidak boleh kosong');
            } else if ($currency == null) {
                throw new Exception('Currency tidak boleh kosong');
            } else if ($currency != 'IDR') {
                throw new Exception('Currency tidak valid');
            }

            $nama = str_replace(' ', '_', $nama);

            $insert = array();
            $insert['name'] = $nama;
            $insert['category'] = $kategori;
            $insert['parameter'] = json_encode($parameter);
            $insert['currency'] = $currency;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();
            $insert['updateddate'] = getCurrentDate();
            $insert['updatedby'] = getCurrentIdUser();

            if (isUser()) {
                $insert['userid'] = getCurrentIdUser();
            }

            $this->msvendor->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan vendor');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menambahkan vendor');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin() && !isUser()) {
            return redirect(base_url('dashboard'));
        } else if (isUser() && getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if (isUser() && getCurrentUser()->multivendor != 1) {
            return redirect(base_url('dashboard'));
        }

        $where = array(
            'id' => $id
        );
        if (isUser()) {
            $where['userid'] = getCurrentIdUser();
        } else {
            $where['userid'] = null;
        }

        $get = $this->msvendor->get($where);

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/vendor'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Ubah Vendor';
        $data['content'] = 'manage/vendor/edit';
        $data['vendor'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit_vendor($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin() && !isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->multivendor != 1) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $where = array(
                'id' => $id,
            );

            if (isUser()) {
                $where['userid'] = getCurrentIdUser();
            } else {
                $where['userid'] = null;
            }

            $get = $this->msvendor->total($where);

            if ($get == 0) {
                throw new Exception('Vendor tidak ditemukan');
            }

            $nama = getPost('nama');
            $kategori = getPost('kategori');
            $parameter = getPost('parameter', array());
            $currency = getPost('currency');

            if ($nama == null) {
                throw new Exception('Nama tidak boleh kosong');
            } else if ($kategori == null) {
                throw new Exception('Kategori tidak boleh kosong');
            } else if (count($parameter) == 0) {
                throw new Exception('Parameter tidak boleh kosong');
            } else if ($currency == null) {
                throw new Exception('Currency tidak boleh kosong');
            } else if ($currency != 'IDR') {
                throw new Exception('Currency tidak valid');
            }

            $nama = str_replace(' ', '_', $nama);

            $update = array();
            $update['name'] = $nama;
            $update['category'] = $kategori;
            $update['parameter'] = json_encode($parameter);
            $update['currency'] = $currency;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msvendor->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah Vendor');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil mengubah Vendor');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_vendor()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin() && !isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->multivendor != 1) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            if ($id == null) {
                throw new Exception('ID vendor tidak boleh kosong');
            }

            $where = array(
                'id' => $id,
            );

            if (isUser()) {
                $where['userid'] = getCurrentIdUser();
            } else {
                $where['userid'] = null;
            }

            $get = $this->msvendor->total($where);

            if ($get == 0) {
                throw new Exception('Vendor tidak ditemukan');
            }

            $this->msvendor->delete(array(
                'id' => $id
            ));

            $this->msvendordetail->delete(array(
                'vendorid' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus Vendor');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menghapus Vendor');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function detail($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin() && !isUser()) {
            return redirect(base_url('dashboard'));
        } else if (isUser() && getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if (isUser() && getCurrentUser()->multivendor != 1) {
            return redirect(base_url('dashboard'));
        }

        $where = array(
            'id' => $id
        );

        if (isUser()) {
            $where['userid'] = getCurrentIdUser();
        } else {
            $where['userid'] = null;
        }

        $get = $this->msvendor->total($where);

        if ($get == 0) {
            return redirect(base_url('manage/vendor'));
        }

        $data = array();
        $data['title'] = 'Detail Vendor';
        $data['content'] = 'manage/vendor/detail';

        return $this->load->view('master', $data);
    }

    public function add_detail($id)
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin() && !isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->multivendor != 1) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $where = array(
                'id' => $id
            );

            if (isUser()) {
                $where['userid'] = getCurrentIdUser();
            } else {
                $where['userid'] = null;
            }

            $get = $this->msvendor->get($where);

            if ($get->num_rows() == 0) {
                throw new Exception('Vendor tidak ditemukan');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/vendor/adddetail', array(
                    'vendor' => $get->row()
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_add_detail_vendor($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin() && !isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->multivendor != 1) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $where = array(
                'id' => $id
            );

            if (isUser()) {
                $where['userid'] = getCurrentIdUser();
            } else {
                $where['userid'] = null;
            }

            $get = $this->msvendor->total($where);

            if ($get == 0) {
                throw new Exception('Vendor tidak ditemukan');
            }

            $endpoint = getPost('endpoint');
            $requestmethod = getPost('requestmethod');
            $contenttype = getPost('contenttype');
            $api = getPost('api');
            $autoaddproduct = getPost('autoaddproduct');
            $parameter = getPost('parameter', array());
            $extends = getPost('extends', array());
            $custom = getPost('custom', array());
            $encryptiontype = getPost('encryptiontype', array());
            $formula = getPost('formula', array());
            $headers = getPost('headers', array());
            $headers_value = getPost('headers_value', array());
            $custom_headers = getPost('custom_headers', array());
            $response_indicator_index = getPost('response_indicator_index');
            $response_indicator_key = getPost('response_indicator_key');
            $response_indicator_value = getPost('response_indicator_valid_value');
            $response_indicator_datatype = getPost('response_indicator_datatype');
            $response_setting_index = getPost('response_setting_index');

            if ($endpoint == null) {
                throw new Exception('End Point tidak boleh kosong');
            } else if ($api == null) {
                throw new Exception('Tipe Api tidak boleh kosong');
            } else if ($requestmethod == null) {
                throw new Exception('Request Method tidak boleh kosong');
            } else if ($requestmethod != 'POST' && $requestmethod != 'GET') {
                throw new Exception('Request Method tidak valid');
            }

            $detail = array();

            foreach ($parameter as $key => $value) {
                if (empty($value)) continue;

                $params = array();
                $params['parameter'] = $value;
                $params['extends'] = $extends[$key];

                if (($extends[$key] ?? null) == 'Custom') {
                    $params['value'] = $custom[$key];
                } else if (($extends[$key] ?? null) == 'Signature') {
                    $params['value'] = array(
                        'encryptiontype' => $encryptiontype[$key],
                        'formula' => $formula[$key]
                    );
                } else {
                    $params['value'] = null;
                }

                $detail[] = $params;
            }

            $headers_detail = array();

            foreach ($headers as $key => $value) {
                if (empty($value)) continue;

                $header = array();
                $header['header'] = $value;
                $header['extends'] = $headers_value[$key];

                if (($headers_value[$key] ?? null) == 'Custom') {
                    $header['value'] = $custom_headers[$key];
                } else {
                    $header['value'] = null;
                }

                $headers_detail[] = $header;
            }

            $response_indicator = array();
            $response_indicator['index'] = $response_indicator_index;
            $response_indicator['key'] = $response_indicator_key;
            $response_indicator['value'] = $response_indicator_value;
            $response_indicator['datatype'] = $response_indicator_datatype;

            $response_setting = array();
            if ($api == 'Profile') {
                $response_balance = getPost('response_profile_balance');

                $response_setting['index'] = $response_setting_index;
                $response_setting['balance'] = $response_balance;
            } else if ($api == 'Service') {
                $response_service_code = getPost('response_service_code');
                $response_service_productname = getPost('response_service_productname');
                $response_service_description = getPost('response_service_description');
                $response_service_status = getPost('response_service_status');
                $response_service_statusdatatype = getPost('response_service_statusdatatype');
                $response_service_validstatus = getPost('response_service_validstatus');
                $response_service_price = getPost('response_service_price');
                $response_service_category = getPost('response_service_category');
                $response_service_brand = getPost('response_service_brand');
                $response_service_minorder = getPost('response_service_minorder');
                $response_service_maxorder = getPost('response_service_maxorder');
                $response_service_type = getPost('response_service_type');

                $response_setting['index'] = $response_setting_index;
                $response_setting['code'] = $response_service_code;
                $response_setting['productname'] = $response_service_productname;
                $response_setting['description'] = $response_service_description;
                $response_setting['status'] = $response_service_status;
                $response_setting['statusdatatype'] = $response_service_statusdatatype;
                $response_setting['validstatus'] = $response_service_validstatus;
                $response_setting['price'] = $response_service_price;
                $response_setting['category'] = $response_service_category;
                $response_setting['brand'] = $response_service_brand;
                $response_setting['minorder'] = $response_service_minorder;
                $response_setting['maxorder'] = $response_service_maxorder;
                $response_setting['type'] = $response_service_type;
            } else if ($api == 'Order') {
                $response_referenceid = getPost('response_order_referenceid');
                $response_price = getPost('response_order_price');
                $response_status = getPost('response_order_status');
                $response_note = getPost('response_order_note');
                $response_sn = getPost('response_order_sn');
                $response_status_errorrefund = getPost('response_status_errorrefund');

                $response_setting['index'] = $response_setting_index;
                $response_setting['referenceid'] = $response_referenceid;
                $response_setting['price'] = $response_price;
                $response_setting['status'] = $response_status;
                $response_setting['errorrefund'] = $response_status_errorrefund;
                $response_setting['note'] = $response_note;
                $response_setting['sn'] = $response_sn;
            } else if ($api == 'Status') {
                $response_referenceid = getPost('response_status_referenceid');
                $response_startcount = getPost('response_status_startcount');
                $response_remains = getPost('response_status_remains');
                $response_status_status = getPost('response_status_status');
                $response_status_errorrefund = getPost('response_status_errorrefund');
                $response_status_partialrefund = getPost('response_status_partialrefund');

                $response_setting['index'] = $response_setting_index;
                $response_setting['referenceid'] = $response_referenceid;
                $response_setting['startcount'] = $response_startcount;
                $response_setting['remains'] = $response_remains;
                $response_setting['status'] = $response_status_status;
                $response_setting['errorrefund'] = $response_status_errorrefund;
                $response_setting['partialrefund'] = $response_status_partialrefund;
            }

            $insert = array();
            $insert['vendorid'] = $id;
            $insert['endpoint'] = $endpoint;
            $insert['requestmethod'] = $requestmethod;
            $insert['contenttype'] = $contenttype;
            $insert['apitype'] = $api;

            if ($api == 'Service') {
                $insert['autoaddproduct'] = $autoaddproduct ? 1 : 0;
            }

            $insert['detail'] = json_encode($detail);
            $insert['headers'] = json_encode($headers_detail);
            $insert['response_indicator'] = json_encode($response_indicator);
            $insert['response_setting'] = json_encode($response_setting);
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();
            $insert['updateddate'] = getCurrentDate();
            $insert['updatedby'] = getCurrentIdUser();

            $this->msvendordetail->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan detail vendor');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menambahkan detail vendor');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit_detail($headerid)
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin() && !isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->multivendor != 1) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $where = array(
                'id' => $headerid
            );

            if (isUser()) {
                $where['userid'] = getCurrentIdUser();
            } else {
                $where['userid'] = null;
            }

            $get = $this->msvendor->get($where);

            if ($get->num_rows() == 0) {
                throw new Exception('Vendor tidak ditemukan');
            }

            $header = $get->row();

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $get = $this->msvendordetail->get(array(
                'id' => $id,
                'vendorid' => $headerid
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/vendor/editdetail', array(
                    'vendor' => $header,
                    'detail' => $row,
                    'indicator' => json_decode($row->response_indicator),
                    'setting' => json_decode($row->response_setting),
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_edit_detail_vendor($headerid)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin() && !isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->multivendor != 1) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $where = array(
                'id' => $headerid
            );

            if (isUser()) {
                $where['userid'] = getCurrentIdUser();
            } else {
                $where['userid'] = null;
            }

            $get = $this->msvendor->get($where);

            $id = getPost('id');

            $get = $this->msvendordetail->total(array(
                'id' => $id,
                'vendorid' => $headerid
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $endpoint = getPost('endpoint');
            $requestmethod = getPost('requestmethod');
            $contenttype = getPost('contenttype');
            $api = getPost('api');
            $autoaddproduct = getPost('autoaddproduct');
            $parameter = getPost('parameter', array());
            $extends = getPost('extends', array());
            $custom = getPost('custom', array());
            $encryptiontype = getPost('encryptiontype', array());
            $formula = getPost('formula', array());
            $headers = getPost('headers', array());
            $headers_value = getPost('headers_value', array());
            $custom_headers = getPost('custom_headers', array());
            $response_indicator_index = getPost('response_indicator_index');
            $response_indicator_key = getPost('response_indicator_key');
            $response_indicator_value = getPost('response_indicator_valid_value');
            $response_indicator_datatype = getPost('response_indicator_datatype');
            $response_setting_index = getPost('response_setting_index');

            if ($endpoint == null) {
                throw new Exception('End Point tidak boleh kosong');
            } else if ($api == null) {
                throw new Exception('Tipe Api tidak boleh kosong');
            } else if ($requestmethod == null) {
                throw new Exception('Request Method tidak boleh kosong');
            } else if ($requestmethod != 'POST' && $requestmethod != 'GET') {
                throw new Exception('Request Method tidak valid');
            }

            $detail = array();

            foreach ($parameter as $key => $value) {
                if (empty($value)) continue;

                $params = array();
                $params['parameter'] = $value;
                $params['extends'] = $extends[$key];

                if (($extends[$key] ?? null) == 'Custom') {
                    $params['value'] = $custom[$key];
                } else if (($extends[$key] ?? null) == 'Signature') {
                    $params['value'] = array(
                        'encryptiontype' => $encryptiontype[$key],
                        'formula' => $formula[$key]
                    );
                } else {
                    $params['value'] = null;
                }

                $detail[] = $params;
            }

            $headers_detail = array();
            foreach ($headers as $key => $value) {
                if (empty($value)) continue;

                $header = array();
                $header['header'] = $value;
                $header['extends'] = $headers_value[$key];

                if (($headers_value[$key] ?? null) == 'Custom') {
                    $header['value'] = $custom_headers[$key];
                } else {
                    $header['value'] = null;
                }

                $headers_detail[] = $header;
            }

            $response_indicator = array();
            $response_indicator['index'] = $response_indicator_index;
            $response_indicator['key'] = $response_indicator_key;
            $response_indicator['value'] = $response_indicator_value;
            $response_indicator['datatype'] = $response_indicator_datatype;

            $response_setting = array();
            if ($api == 'Profile') {
                $response_balance = getPost('response_profile_balance');

                $response_setting['index'] = $response_setting_index;
                $response_setting['balance'] = $response_balance;
            } else if ($api == 'Service') {
                $response_service_code = getPost('response_service_code');
                $response_service_productname = getPost('response_service_productname');
                $response_service_description = getPost('response_service_description');
                $response_service_status = getPost('response_service_status');
                $response_service_statusdatatype = getPost('response_service_statusdatatype');
                $response_service_validstatus = getPost('response_service_validstatus');
                $response_service_price = getPost('response_service_price');
                $response_service_category = getPost('response_service_category');
                $response_service_brand = getPost('response_service_brand');
                $response_service_minorder = getPost('response_service_minorder');
                $response_service_maxorder = getPost('response_service_maxorder');
                $response_service_type = getPost('response_service_type');

                $response_setting['index'] = $response_setting_index;
                $response_setting['code'] = $response_service_code;
                $response_setting['productname'] = $response_service_productname;
                $response_setting['description'] = $response_service_description;
                $response_setting['status'] = $response_service_status;
                $response_setting['statusdatatype'] = $response_service_statusdatatype;
                $response_setting['validstatus'] = $response_service_validstatus;
                $response_setting['price'] = $response_service_price;
                $response_setting['category'] = $response_service_category;
                $response_setting['brand'] = $response_service_brand;
                $response_setting['minorder'] = $response_service_minorder;
                $response_setting['maxorder'] = $response_service_maxorder;
                $response_setting['type'] = $response_service_type;
            } else if ($api == 'Order') {
                $response_referenceid = getPost('response_order_referenceid');
                $response_price = getPost('response_order_price');
                $response_status = getPost('response_order_status');
                $response_note = getPost('response_order_note');
                $response_sn = getPost('response_order_sn');
                $response_status_errorrefund = getPost('response_status_errorrefund');

                $response_setting['index'] = $response_setting_index;
                $response_setting['referenceid'] = $response_referenceid;
                $response_setting['price'] = $response_price;
                $response_setting['status'] = $response_status;
                $response_setting['errorrefund'] = $response_status_errorrefund;
                $response_setting['note'] = $response_note;
                $response_setting['sn'] = $response_sn;
            } else if ($api == 'Status') {
                $response_referenceid = getPost('response_status_referenceid');
                $response_startcount = getPost('response_status_startcount');
                $response_remains = getPost('response_status_remains');
                $response_status_status = getPost('response_status_status');
                $response_status_errorrefund = getPost('response_status_errorrefund');
                $response_status_partialrefund = getPost('response_status_partialrefund');

                $response_setting['index'] = $response_setting_index;
                $response_setting['referenceid'] = $response_referenceid;
                $response_setting['startcount'] = $response_startcount;
                $response_setting['remains'] = $response_remains;
                $response_setting['status'] = $response_status_status;
                $response_setting['errorrefund'] = $response_status_errorrefund;
                $response_setting['partialrefund'] = $response_status_partialrefund;
            }

            $update = array();
            $update['vendorid'] = $headerid;
            $update['requestmethod'] = $requestmethod;
            $update['contenttype'] = $contenttype;
            $update['endpoint'] = $endpoint;
            $update['apitype'] = $api;

            if ($api == 'Service') {
                $update['autoaddproduct'] = $autoaddproduct ? 1 : 0;
            } else {
                $update['autoaddproduct'] = null;
            }

            $update['detail'] = json_encode($detail);
            $update['headers'] = json_encode($headers_detail);
            $update['response_indicator'] = json_encode($response_indicator);
            $update['response_setting'] = json_encode($response_setting);
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msvendordetail->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah detail vendor');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil mengubah detail vendor');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_detail_vendor($id)
    {
        try {
            if (isLogin() && (isAdmin() || (isUser() && getCurrentUser()->licenseid != null && getCurrentUser()->multivendor == 1))) {
                $data = array();

                $datatables = $this->datatables->make('MsVendorDetail', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.vendorid' => $id,
                );

                if (isUser()) {
                    $where['b.userid'] = getCurrentIdUser();
                } else {
                    $where['b.userid'] = null;
                }

                foreach ($datatables->getData($where) as $key => $value) {
                    $detail = array();
                    $detail[] = $value->apitype;
                    $detail[] = $value->endpoint;
                    $detail[] = $value->contenttype ?? 'application/x-www-form-urlencoded';

                    $parameter = json_decode($value->detail);

                    $li = "";
                    foreach ($parameter as $key => $v) {
                        $li .= "<span>Params: " . $v->parameter . "</span><br>";
                        $li .= "<ul>";
                        $li .= "<li>Extends : " . $v->extends . "</li>";
                        if ($v->value != null) {
                            if (is_string($v->value) && strpos($v->value, '${split}') === false) {
                                $li .= "<li>Value : " . $v->value . "</li>";
                            } else if (is_object($v->value)) {
                                $signature = "<br>";
                                foreach ($v->value as $k => $val) {
                                    $signature .= "- " . $k . " : " . $val . "<br>";
                                }

                                $li .= "<li>Value : $signature</li>";
                            } else {
                                $split = "";
                                foreach (explode('${split}', $v->value) as $k => $val) {
                                    $split .= "- " . $val . "<br>";
                                }
                                $li .= "<li>Value : <br>$split</li>";
                            }
                        }
                        $li .= "</ul>";
                    }

                    $headers = json_decode($value->headers ?? '[]');
                    foreach ($headers as $key => $v) {
                        $li .= "<span>Headers: " . $v->header . "</span><br>";
                        $li .= "<ul>";
                        $li .= "<li>Extends : " . $v->extends . "</li>";
                        if ($v->value != null) {
                            $li .= "<li>Value : " . $v->value . "</li>";
                        }
                        $li .= "</ul>";
                    }

                    $detail[] = $li;

                    $detail[] = "<a href=\"javascript:;\") onclick=\"editDetail(" . $value->id . ")\" class=\"btn btn-icon btn-warning btn-sm mb-1\">
                    <i class=\"fa fa-edit\"></i>
                </a>

                <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"deleteDetailVendor('" . stringEncryption('encrypt', $value->id) . "')\">
                    <i class=\"fa fa-trash\"></i>
                </a>";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_detail_vendor()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin() && !isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->multivendor != 1) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            if ($id == null) {
                throw new Exception('ID Detail vendor tidak boleh kosong');
            }

            $where = array(
                'a.id' => $id,
            );

            if (isUser()) {
                $where['b.userid'] = getCurrentIdUser();
            } else {
                $where['b.userid'] = null;
            }

            $get = $this->msvendordetail->select('a.*')
                ->join('msvendor b', 'b.id = a.vendorid')
                ->total($where);

            if ($get == 0) {
                throw new Exception('Detail Vendor tidak ditemukan');
            }

            $this->msvendordetail->delete(array(
                'id' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus detail vendor');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menghapus detail vendor');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_product()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (isUser() && getCurrentUser()->multivendor != 1) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            if ($id == null) {
                throw new Exception('ID Produk tidak boleh kosong');
            }

            $this->msproduct->delete(array(
                'vendorid' => $id,
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus produk');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menghapus produk');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
