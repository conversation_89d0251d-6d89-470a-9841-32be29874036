<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-xl">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Transaksi Antri</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <div class="modal-body">
            <div class="table-responsive">
                <table class="table table-striped table-row-bordered gy-5 datatables-queuetransaction">
                    <thead>
                        <tr class="fw-semibold fs-6 text-muted">
                            <th>Kode Transaksi</th>
                            <th>Tanggal Transaksi</th>
                            <th>Produk</th>
                            <th>Harga</th>
                            <th>Vendor</th>
                            <th>Pembeli</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>

                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
        </div>
    </div>
</div>

<script>
    $('.datatables-queuetransaction').DataTable({
        responsive: true,
        processing: true,
        serverSide: true,
        stateSave: true,
        ordering: false,
        ajax: {
            url: '<?= base_url(uri_string() . '/datatables') ?>',
        }
    });

    function cancelTransaction(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Apakah anda yakin ingin membatalkan antrian transaksi ini?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/cancel') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }
</script>