<?php
defined('BASEPATH') or exit('No direct script access allowed');

class QueueUploadPlaystore_Prerelease extends MY_Model
{
    protected $table = 'queueuploadplaystore_prerelease';
    public $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.*')
            ->from($this->table . ' a')
            ->join('queueuploadplaystore b', 'b.id = a.queueuploadplaystoreid')
            ->order_by('a.createddate', 'DESC');

        return $this;
    }
}
