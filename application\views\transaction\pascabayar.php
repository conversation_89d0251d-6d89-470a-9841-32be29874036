<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Transaksi Pascabayar</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Transaksi</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Pascabayar</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="transactiondate" class="col-form-label fw-semibold fs-6 pt-0">Tanggal Transaksi</label>
                                <input type="text" name="transactiondate" id="transactiondate" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Tanggal" value="<?= date('m/01/Y') ?> - <?= date('m/t/Y') ?>">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="transactionstatus" class="col-form-label fw-semibold fs-6 pt-0">Status Transaksi</label>
                                <select name="transactionstatus" id="transactionstatus" class="form-control form-control-lg form-control-solid mb-lg-0">
                                    <option value="">Pilih Status</option>
                                    <?php foreach ($status as $key => $value): ?>
                                        <option value="<?= $value->status ?>"><?= strtoupper($value->status) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="transactionpaymentstatus" class="col-form-label fw-semibold fs-6 pt-0">Status Pembayaran</label>
                                <select name="transactionpaymentstatus" id="transactionpaymentstatus" class="form-control form-control-lg form-control-solid mb-lg-0">
                                    <option value="">Pilih Status</option>
                                    <?php foreach ($status_payment as $key => $value): ?>
                                        <option value="<?= $value->status_payment ?>"><?= strtoupper($value->status_payment) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="transactioncode" class="col-form-label fw-semibold fs-6 pt-0">Kode Transaksi</label>
                                <input type="text" name="transactioncode" id="transactioncode" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Kode Transaksi">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="platform" class="col-form-label fw-semibold fs-6 pt-0">Platform Pembelian</label>
                                <select name="platform" id="platform" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                    <option value="">Pilih Platform</option>
                                    <?php foreach ($platform as $key => $value): ?>
                                        <option value="<?= $value->orderplatform ?>"><?= strtoupper($value->orderplatform) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="productcategory" class="col-form-label fw-semibold fs-6 pt-0">Kategori Produk</label>
                                <select name="productcategory" id="productcategory" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                    <option value="">Pilih Kategori</option>
                                    <?php foreach ($category as $key => $value): ?>
                                        <option value="<?= $value->category ?>"><?= $value->category ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="productoperator" class="col-form-label fw-semibold fs-6 pt-0">Operator</label>
                                <select name="productoperator" id="productoperator" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                    <option value="">Pilih Operator</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="productid" class="col-form-label fw-semibold fs-6 pt-0">Produk</label>
                                <select name="productid" id="productid" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                    <option value="">Pilih Produk</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <button type="button" class="btn btn-dark fw-bold w-100" onclick="filterPascabayar()">Filter</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-5">
                <div class="card-body">
                    <table class="table table-striped table-row-bordered gy-5 datatables-pascabayar text-nowrap">
                        <thead>
                            <tr class="fw-semibold fs-6 text-muted">
                                <th>Tanggal Transaksi</th>
                                <th>Kode Transaksi</th>
                                <th>Produk</th>
                                <th>Tujuan</th>
                                <th>Harga</th>
                                <th>Pembeli</th>
                                <th>SN</th>
                                <th>Status</th>
                                <th>Status Pembayaran</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>

                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('input[name="transactiondate"]').daterangepicker();

        $('.datatables-pascabayar').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
                method: 'POST',
            }
        });

        $('#productcategory').change(function() {
            $('#productoperator').html('<option value="">Pilih Operator</option>');
            $('#productid').html('<option value="">Pilih Produk</option>');

            $.ajax({
                url: '<?= base_url('select/brand') ?>',
                method: 'POST',
                dataType: 'json',
                data: {
                    category: $(this).val()
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        for (let i = 0; i < response.DATA.length; i++) {
                            $('#productoperator').append('<option value="' + response.DATA[i] + '">' + response.DATA[i] + '</option>');
                        }
                    }
                }
            }).fail(function() {
                return Swal.fire('Gagal', 'Server sedang sibuk! Silahkan coba lagi nanti');
            });
        });

        $('#productoperator').change(function() {
            $('#productid').html('<option value="">Pilih Produk</option>');

            $.ajax({
                url: '<?= base_url('select/product') ?>',
                method: 'POST',
                dataType: 'json',
                data: {
                    category: $('#productcategory').val(),
                    brand: $(this).val()
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        for (let i = 0; i < response.DATA.length; i++) {
                            $('#productid').append('<option value="' + response.DATA[i]['value'] + '">' + response.DATA[i]['name'] + '</option>');
                        }
                    }
                }
            }).fail(function() {
                return Swal.fire('Gagal', 'Server sedang sibuk! Silahkan coba lagi nanti');
            });
        });

        socket.on('refreshtransaction', () => {
            $('.datatables-pascabayar').DataTable().ajax.reload();
        });
    };

    function filterPascabayar() {
        let transactiondate = $('input[name="transactiondate"]').val();
        let transactionstatus = $('#transactionstatus').val();
        let transactionpaymentstatus = $('#transactionpaymentstatus').val();
        let transactioncode = $('#transactioncode').val();
        let platform = $('#platform').val();
        let category = $('#productcategory').val();
        let brand = $('#productoperator').val();
        let product = $('#productid').val();

        $('.datatables-pascabayar').DataTable().destroy();
        $('.datatables-pascabayar').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url('transaction/pascabayar/datatables') ?>',
                method: 'POST',
                data: {
                    date: transactiondate,
                    status: transactionstatus,
                    paymentstatus: transactionpaymentstatus,
                    transactioncode: transactioncode,
                    platform: platform,
                    category: category,
                    brand: brand,
                    product: product,
                }
            }
        });
    }

    function refundTransaction(id) {
        return Swal.fire({
            title: 'Apakah anda yakin?',
            text: 'Transaksi akan diubah menjadi Status Gagal, Saldo User akan dikembalikan',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/refund') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(() => {
                                $('.datatables-pascabayar').DataTable().ajax.reload();
                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                });
            }
        });
    };

    function modalStruk(id) {
        $.ajax({
            url: '<?= base_url('transaction/print/') ?>' + id,
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                }
            }
        }).fail(function() {

        });
    }
    
    function modalUserDetail(userId) {
        // Pastikan userId tidak kosong
        if (!userId) {
            return Swal.fire({
                title: 'Gagal',
                text: 'ID pengguna tidak valid',
                icon: 'error'
            });
        }

        $.ajax({
            url: '<?= base_url("transaction/detail_user") ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                userId: userId
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    }
</script>