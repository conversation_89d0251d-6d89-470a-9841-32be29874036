<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Tambah Vendor</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Management</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Vendor</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Tambah Vendor</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <?php if (isUser()) : ?>
        <a href="<?= base_url('settings/apikey') ?>" class="btn btn-danger fw-bold">Kembali</a>
    <?php else : ?>
        <a href="<?= base_url('manage/vendor') ?>" class="btn btn-danger fw-bold">Kembali</a>
    <?php endif; ?>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <form id="frmAddVendor" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="mb-7 col-md-4">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Nama</label>
                                <input class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" type="text" name="nama" id="nama" placeholder="Nama" required>
                            </div>

                            <div class="mb-7 col-md-4">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Kategori</label>
                                <select name="kategori" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                    <option value="">- Pilih -</option>
                                    <option value="PPOB" <?= isUser() && getCurrentUser()->licenseid == 3 ? 'disabled' : null ?>>PPOB</option>
                                    <option value="SMM" <?= isUser() && getCurrentUser()->licenseid == 2 ? 'disabled' : null ?>>SMM</option>
                                </select>
                            </div>

                            <div class="mb-7 col-md-4">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Mata Uang</label>
                                <select name="currency" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" require>
                                    <option value="">- Pilih -</option>
                                    <option value="IDR">IDR</option>
                                </select>
                            </div>

                            <div class="col-md-12">
                                <div class="row align-items-center">
                                    <div class="mb-7 col-md-6">
                                        <label class="col-form-label fw-semibold fs-6 pt-0">Parameter</label>
                                        <input class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" type="text" name="parameter[]" id="parameter" placeholder="Parameter" required>
                                    </div>

                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-primary btn-sm" onclick="addParameter()">
                                            <i class="fa fa-plus pe-0"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div id="rowparameter" class="col-md-12">
                            </div>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $.AjaxRequest('#frmAddVendor', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function(result) {
                        <?php if (str_contains(uri_string(), 'apikey')) : ?>
                            return window.location.href = '<?= base_url('settings/apikey') ?>';
                        <?php else : ?>
                            return window.location.href = '<?= base_url('manage/vendor') ?>';
                        <?php endif; ?>
                    })
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error'
                });
            }
        });
    };

    function addParameter() {
        $('#rowparameter').append(`<div class="row align-items-center">
            <div class="mb-7 col-md-6">
                <input class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" type="text" name="parameter[]" placeholder="Parameter" required>
            </div>
            
            <div class="col-md-6 mb-7">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeParameter(this)">
                    <i class="fa fa-minus pe-0"></i>
                </button>
            </div>
        </div>`);
    }

    function removeParameter(these) {
        $(these).parent().parent().remove();
    }
</script>