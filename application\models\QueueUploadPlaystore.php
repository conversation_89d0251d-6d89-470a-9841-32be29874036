<?php
defined('BASEPATH') or exit('No direct script access allowed');

class QueueUploadPlaystore extends MY_Model
{
    protected $table = 'queueuploadplaystore';
    protected $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.*, b.name, b.email, b.companyname')
            ->from($this->table . ' a')
            ->join('msusers b', 'a.userid = b.id')
            ->order_by('a.createddate', 'DESC');

        return $this;
    }
}
