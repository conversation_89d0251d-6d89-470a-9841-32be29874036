<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-xl">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Fee Payments</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmFeePayments" action="<?= base_url(uri_string() . '/process') ?>" method="POST" enctype="multipart/form-data" autocomplete="off">
            <input type="hidden" name="enc" value="<?= stringEncryption('encrypt', $row->id) ?>">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <div class="row">
                    <?php if ($row->vendor == 'Midtrans') : ?>
                        <?php foreach (getMidtransPayments() as $key => $value) : ?>
                            <?php if (!in_array($key, $channel_payments)) continue; ?>

                            <?php $get = $this->db->get_where('feepaymentgateway', array('paymentgatewayid' => $row->id, 'paymentname' => $key, "(vendor IS NULL OR vendor = '$row->vendor') =" => true))->row(); ?>

                            <div class="col-md-12">
                                <div class="mb-7">
                                    <input type="hidden" name="paymentname[]" value="<?= $key ?>">
                                    <h5><?= $value ?></h5>
                                    <hr>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Nominal Fee</label>
                                            <input type="number" name="fee[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nominal Fee" step="0.01" value="<?= $get != null ? $get->fee : null ?>">
                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Tipe Fee</label>
                                            <select name="feetype[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                                <option value="">- Pilih -</option>
                                                <option value="Nominal" <?= $get != null && $get->feetype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                                                <option value="Persentase" <?= $get != null && $get->feetype == 'Persentase' ? 'selected' : null ?>>Persentase</option>
                                            </select>
                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Gambar Pembayaran</label>
                                            <input type="file" name="paymentimage[]" accept=".png,.jpg,.jpeg" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                            <small><?= $get != null && $get->paymentimage != null ? '*Kosongkan jika tidak ingin dibuah' : null ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php elseif ($row->vendor == 'Tripay') : ?>
                        <?php foreach (getTripayPayments() as $key => $value) : ?>
                            <?php if (!in_array($key, $channel_payments)) continue; ?>

                            <?php $get = $this->db->get_where('feepaymentgateway', array('paymentgatewayid' => $row->id, 'paymentname' => $key, "(vendor IS NULL OR vendor = '$row->vendor') =" => true))->row(); ?>

                            <div class="col-md-12">
                                <div class="mb-7">
                                    <input type="hidden" name="paymentname[]" value="<?= $key ?>">
                                    <h5><?= $value ?></h5>
                                    <hr>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Nominal Fee</label>
                                            <input type="number" name="fee[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nominal Fee" step="0.01" value="<?= $get != null ? $get->fee : null ?>">
                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Tipe Fee</label>
                                            <select name="feetype[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                                <option value="">- Pilih -</option>
                                                <option value="Nominal" <?= $get != null && $get->feetype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                                                <option value="Persentase" <?= $get != null && $get->feetype == 'Persentase' ? 'selected' : null ?>>Persentase</option>
                                            </select>
                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Gambar Pembayaran</label>
                                            <input type="file" name="paymentimage[]" accept=".png,.jpg,.jpeg" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                            <small><?= $get != null && $get->paymentimage != null ? '*Kosongkan jika tidak ingin dibuah' : null ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php elseif ($row->vendor == 'iPaymu') : ?>
                        <?php foreach (getIpaymuPayments() as $key => $value) : ?>
                            <?php if (!in_array($key, $channel_payments)) continue; ?>

                            <?php $get = $this->db->get_where('feepaymentgateway', array('paymentgatewayid' => $row->id, 'paymentname' => $key, "(vendor IS NULL OR vendor = '$row->vendor') =" => true))->row(); ?>

                            <div class="col-md-12">
                                <div class="mb-7">
                                    <input type="hidden" name="paymentname[]" value="<?= $key ?>">
                                    <h5><?= $value ?></h5>
                                    <hr>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Nominal Fee</label>
                                            <input type="number" name="fee[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nominal Fee" step="0.01" value="<?= $get != null ? $get->fee : null ?>">
                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Tipe Fee</label>
                                            <select name="feetype[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                                <option value="">- Pilih -</option>
                                                <option value="Nominal" <?= $get != null && $get->feetype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                                                <option value="Persentase" <?= $get != null && $get->feetype == 'Persentase' ? 'selected' : null ?>>Persentase</option>
                                            </select>
                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Gambar Pembayaran</label>
                                            <input type="file" name="paymentimage[]" accept=".png,.jpg,.jpeg" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                            <small><?= $get != null && $get->paymentimage != null ? '*Kosongkan jika tidak ingin dibuah' : null ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php elseif ($row->vendor == 'Duitku') : ?>
                        <?php foreach (json_decode($payment_method)->paymentFee as $key => $value) : ?>
                            <?php if (!in_array($value->paymentMethod, $channel_payments)) continue; ?>

                            <?php $get = $this->db->get_where('feepaymentgateway', array('paymentgatewayid' => $row->id, 'paymentname' => $value->paymentMethod, "(vendor IS NULL OR vendor = '$row->vendor') =" => true))->row(); ?>

                            <div class="col-md-12">
                                <div class="mb-7">
                                    <input type="hidden" name="paymentname[]" value="<?= $value->paymentMethod ?>">
                                    <h5><?= $value->paymentName ?></h5>
                                    <hr>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Nominal Fee</label>
                                            <input type="number" name="fee[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nominal Fee" step="0.01" value="<?= $get != null ? $get->fee : null ?>">
                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Tipe Fee</label>
                                            <select name="feetype[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                                <option value="">- Pilih -</option>
                                                <option value="Nominal" <?= $get != null && $get->feetype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                                                <option value="Persentase" <?= $get != null && $get->feetype == 'Persentase' ? 'selected' : null ?>>Persentase</option>
                                            </select>
                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Gambar Pembayaran</label>
                                            <input type="file" name="paymentimage[]" accept=".png,.jpg,.jpeg" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                            <small><?= $get != null && $get->paymentimage != null ? '*Kosongkan jika tidak ingin dibuah' : null ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php elseif ($row->vendor == 'Okeconnect') : ?>
                        <?php foreach (getOkeconnectPayments() as $key => $value) : ?>
                            <?php if (!in_array($key, $channel_payments)) continue; ?>

                            <?php $get = $this->db->get_where('feepaymentgateway', array('paymentgatewayid' => $row->id, 'paymentname' => $key, "(vendor IS NULL OR vendor = '$row->vendor') =" => true))->row(); ?>

                            <div class="col-md-12">
                                <div class="mb-7">
                                    <input type="hidden" name="paymentname[]" value="<?= $key ?>">
                                    <h5><?= $value ?></h5>
                                    <hr>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Nominal Fee</label>
                                            <input type="number" name="fee[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nominal Fee" step="0.01" value="<?= $get != null ? $get->fee : null ?>">
                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Tipe Fee</label>
                                            <select name="feetype[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                                <option value="">- Pilih -</option>
                                                <option value="Nominal" <?= $get != null && $get->feetype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                                                <option value="Persentase" <?= $get != null && $get->feetype == 'Persentase' ? 'selected' : null ?>>Persentase</option>
                                            </select>
                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Gambar Pembayaran</label>
                                            <input type="file" name="paymentimage[]" accept=".png,.jpg,.jpeg" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                            <small><?= $get != null && $get->paymentimage != null ? '*Kosongkan jika tidak ingin dibuah' : null ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php elseif ($row->vendor == 'PayDisini') : ?>
                        <?php foreach ($payment_channel as $key => $value) : ?>
                            <?php if (!in_array($value->id, $channel_payments)) continue; ?>

                            <?php $get = $this->db->get_where('feepaymentgateway', array('paymentgatewayid' => $row->id, 'paymentname' => $value->id, "(vendor IS NULL OR vendor = '$row->vendor') =" => true))->row(); ?>

                            <div class="col-md-12">
                                <div class="mb-7">
                                    <input type="hidden" name="paymentname[]" value="<?= $value->id ?>">
                                    <h5><?= $value->name ?></h5>
                                    <hr>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Nominal Fee</label>
                                            <input type="number" name="fee[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nominal Fee" step="0.01" value="<?= $get != null ? $get->fee : null ?>">
                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Tipe Fee</label>
                                            <select name="feetype[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                                <option value="">- Pilih -</option>
                                                <option value="Nominal" <?= $get != null && $get->feetype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                                                <option value="Persentase" <?= $get != null && $get->feetype == 'Persentase' ? 'selected' : null ?>>Persentase</option>
                                            </select>
                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Gambar Pembayaran</label>
                                            <input type="file" name="paymentimage[]" accept=".png,.jpg,.jpeg" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                            <small><?= $get != null && $get->paymentimage != null ? '*Kosongkan jika tidak ingin dibuah' : null ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    $.AjaxRequest('#frmFeePayments', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success',
                }).then(function(result) {
                    return window.location.reload();

                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.MESSAGE,
                    icon: 'error',
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        }
    });
</script>