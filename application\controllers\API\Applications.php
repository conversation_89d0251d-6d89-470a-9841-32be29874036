<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property CI_Upload $upload
 * @property CustomApp $customapp
 */
class Applications extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('CustomApp', 'customapp');
    }

    public function upload()
    {
        $customappid = getPost('customappid');
        $type = getPost('type');
        $version = getPost('version');

        if ($customappid == null) {
            return JSONResponse(array(
                'success' => false,
                'message' => 'Custom App ID is required'
            ));
        } else if (!in_array($type, array('aab', 'apk'))) {
            return JSONResponse(array(
                'success' => false,
                'message' => 'Invalid type'
            ));
        }

        $get = $this->customapp->total(array(
            'id' => $customappid
        ));

        if ($get == 0) {
            return JSONResponse(array(
                'success' => false,
                'message' => 'Custom App not found'
            ));
        }

        $config = array();
        $config['upload_path'] = './live/demo/uploads/';
        $config['allowed_types'] = 'aab|apk';
        $config['encrypt_name'] = true;

        $this->load->library('upload', $config);

        if ($this->upload->do_upload('app')) {
            $data = $this->upload->data();

            $execute = array();
            $execute['app_version'] = $version;

            if ($type == 'apk') {
                $execute['apk_link'] = $data['file_name'];
            } else {
                $execute['appbundle_link'] = $data['file_name'];
            }

            $this->customapp->update(array(
                'id' => $customappid
            ), $execute);

            return JSONResponse(array(
                'success' => true,
                'message' => 'File uploaded successfully'
            ));
        } else {
            return JSONResponse(array(
                'success' => false,
                'message' => $this->upload->display_errors('', '')
            ));
        }
    }
}
