<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Percakapan: <?= $ticket->title ?></h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Pengguna</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Tiket</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Percakapan: <?= $ticket->title ?></li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <a href="<?= base_url('users/ticket/history') ?>" class="btn btn-danger fw-bold">Kembali</a>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-content-fluid">
    <div class="card" id="kt_chat_messenger">
        <!--begin::Card header-->
        <div class="card-header" id="kt_chat_messenger_header">
            <!--begin::Title-->
            <div class="card-title">
                <!--begin::User-->
                <div class="d-flex justify-content-center flex-column me-3">
                    <a href="#" class="fs-4 fw-bold text-gray-900 text-hover-primary me-1 mb-2 lh-1"><?= $ticket->creator_name ?></a>

                    <!--begin::Info-->
                    <div class="mb-0 lh-1">
                        <span class="badge badge-success badge-circle w-10px h-10px me-1"></span>
                        <span class="fs-7 fw-semibold text-muted">User</span>
                    </div>
                    <!--end::Info-->
                </div>
                <!--end::User-->
            </div>
            <!--end::Title-->
        </div>
        <!--end::Card header-->

        <!--begin::Card body-->
        <div class="card-body" id="kt_chat_messenger_body">
            <!--begin::Messages-->
            <div id="kt_chat_messenger_content" class="scroll-y me-n5 pe-5 h-300px h-lg-auto" data-kt-element="messages" data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto" data-kt-scroll-dependencies="#kt_header, #kt_app_header, #kt_app_toolbar, #kt_toolbar, #kt_footer, #kt_app_footer, #kt_chat_messenger_header, #kt_chat_messenger_footer" data-kt-scroll-wrappers="#kt_content, #kt_app_content, #kt_chat_messenger_body" data-kt-scroll-offset="5px" style="max-height: 462px;">
                <?php foreach ($conversation as $key => $value) : ?>
                    <div class="d-flex <?= $value->userid == getCurrentIdUser() ? 'justify-content-end' : 'justify-content-start' ?> mb-10">
                        <!--begin::Wrapper-->
                        <div class="d-flex flex-column <?= $value->userid == getCurrentIdUser() ? 'align-items-end' : 'align-items-start' ?>">
                            <!--begin::User-->
                            <div class="d-flex align-items-center mb-2">
                                <?php if ($value->userid == getCurrentIdUser()) : ?>
                                    <!--begin::Details-->
                                    <div class="me-3">
                                        <span class="text-muted fs-7 mb-1"><?= DateFormat($value->createddate, 'd F Y H:i:s') ?></span>
                                        <a href="#" class="fs-5 fw-bold text-gray-900 text-hover-primary ms-1">Anda</a>
                                    </div>
                                    <!--end::Details-->
                                <?php endif; ?>

                                <!--begin::Avatar-->
                                <div class="symbol symbol-35px symbol-circle">
                                    <img src="<?= base_url() ?>assets/media/avatars/300-1.jpg">
                                </div>
                                <!--end::Avatar-->

                                <?php if ($value->userid != getCurrentIdUser()) : ?>
                                    <!--begin::Details-->
                                    <div class="ms-3">
                                        <a href="#" class="fs-5 fw-bold text-gray-900 text-hover-primary me-1"><?= $value->sender_name ?></a>
                                        <span class="text-muted fs-7 mb-1"><?= DateFormat($value->createddate, 'd F Y H:i:s') ?></span>
                                    </div>
                                    <!--end::Details-->
                                <?php endif; ?>
                            </div>
                            <!--end::User-->

                            <!--begin::Text-->
                            <div class="p-5 rounded <?= $value->userid == getCurrentIdUser() ? 'bg-light-primary' : 'bg-light-info' ?> text-dark fw-semibold mw-lg-400px <?= $value->userid == getCurrentIdUser() ? 'text-end' : 'text-start' ?>" data-kt-element="message-text">
                                <?php if (!empty($value->img_url)) : ?>
                                    <img src="<?= base_url('live/demo/uploads/tickets/' . $value->img_url) ?>"
                                        alt="Gambar"
                                        class="img-fluid rounded mb-2 d-block"
                                        style="max-width: 100%; max-height: 250px;">
                                <?php endif; ?>

                                <?php if (!empty($value->message)) : ?>
                                    <div><?= nl2br(htmlspecialchars($value->message)) ?></div>
                                <?php endif; ?>
                            </div>
                            <!--end::Text-->
                        </div>
                        <!--end::Wrapper-->
                    </div>
                <?php endforeach; ?>
            </div>
            <!--end::Messages-->
        </div>
        <!--end::Card body-->

        <!--begin::Card footer-->
        <div class="card-footer pt-4" id="kt_chat_messenger_footer">
            <form id="frmSendConversation" action="<?= base_url(uri_string() . '/send') ?>" method="POST" autocomplete="off" enctype="multipart/form-data">
                <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                <!--begin::Input-->
                <textarea name="message" class="form-control form-control-flush mb-3" rows="1" data-kt-element="input" placeholder="Type a message" <?= $ticket->status == 'Done' ? 'disabled' : null ?> required></textarea>
                <!--end::Input-->

                <!--begin:Toolbar-->
                <div class="d-flex justify-content-between flex-end">
                    <!--begin::Send-->
                    <button type="button" class="btn btn-light-info" data-bs-toggle="modal" data-bs-target="#modalUpload" <?= $ticket->status == 'Done' ? 'disabled' : null ?>>
                        <i class="fas fa-paperclip me-2"></i>Attach File
                    </button>

                    <button class="btn btn-primary" type="submit" data-kt-element="send" <?= $ticket->status == 'Done' ? 'disabled' : null ?>>
                        <i class="fas fa-paper-plane me-2"></i>Send
                    </button>
                    <!--end::Send-->
                </div>
                <!--end::Toolbar-->
            </form>
        </div>
        <!--end::Card footer-->
    </div>
</div>

<div class="modal fade" id="modalUpload" tabindex="-1" aria-labelledby="modalUploadLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form id="frmSendConversationWimage" action="<?= base_url(uri_string() . '/send') ?>" method="POST" autocomplete="off" enctype="multipart/form-data">
                <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                <div class="modal-header">
                    <h5 class="modal-title" id="modalUploadLabel">Kirim Pesan dengan Gambar</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <!-- Message Input -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">Pesan</label>
                        <textarea class="form-control" name="message" placeholder="Tulis pesan Anda..." rows="3" <?= $ticket->status == 'Done' ? 'disabled' : null ?> required></textarea>
                    </div>

                    <!-- File Upload Area -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">Lampiran File</label>
                        <div class="file-upload-wrapper">
                            <div class="file-upload-area" id="fileUploadArea" onclick="document.getElementById('screenshot').click()">
                                <div class="file-upload-content" id="fileUploadContent">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <h6 class="text-muted mb-2">Klik untuk memilih file atau drag & drop</h6>
                                    <p class="text-muted small mb-0">Mendukung: JPG, PNG, JPEG (Max: 5MB)</p>
                                </div>

                                <div class="file-upload-preview" id="fileUploadPreview" style="display: none;">
                                    <img id="previewImageModal" class="preview-image" src="" alt="Preview">
                                    <div class="file-info">
                                        <span id="fileName" class="file-name"></span>
                                        <button type="button" class="btn btn-sm btn-light-danger ms-2" onclick="removeFile()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <input type="file" class="d-none" name="screenshot" id="screenshot" accept="image/*" onchange="handleFileSelect(this)" <?= $ticket->status == 'Done' ? 'disabled' : null ?>>
                        </div>
                    </div>
                </div>

                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Batal
                    </button>

                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-paper-plane me-2"></i>Kirim Pesan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function goBottom() {
        var bottomChat = document.getElementById('kt_chat_messenger_content');
        bottomChat.scrollTop = bottomChat.scrollHeight;
    }

    window.onload = function() {
        $('textarea').on('keyup, keydown, keypress', function(e) {
            if (e.keyCode == 13) {
                $('#frmSendConversation').submit();
            }
        });

        $.AjaxRequest('#frmSendConversation', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#frmSendConversation').find('textarea').val(null).focus();

                    $.get('<?= base_url(uri_string()) ?>', function(data) {
                        let content = $(data).find('#kt_chat_messenger_content').html();
                        $('#kt_chat_messenger_content').html(content);
                    });

                    setTimeout(function() {
                        goBottom();
                    }, 250);
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            }
        });

        $.AjaxRequest('#frmSendConversationWimage', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    // Reset form: kosongkan textarea & file input
                    $('#frmSendConversationWimage').trigger('reset');
                    $('#frmSendConversationWimage').find('textarea').focus();

                    // Reset file upload interface
                    removeFile();

                    // Reload chat content
                    $.get('<?= base_url(uri_string()) ?>', function(data) {
                        let content = $(data).find('#kt_chat_messenger_content').html();
                        $('#kt_chat_messenger_content').html(content);
                    });

                    setTimeout(function() {
                        goBottom();
                    }, 250);

                    // Tutup modal kalau masih terbuka
                    $('#modalUpload').modal('hide');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            }
        });

        goBottom();

        socket.on('<?= stringEncryption('encrypt', $ticket->id) ?>', function() {
            $.get('<?= base_url(uri_string()) ?>', function(data) {
                let content = $(data).find('#kt_chat_messenger_content').html();
                $('#kt_chat_messenger_content').html(content);
            });

            setTimeout(function() {
                goBottom();
            }, 250);

            audio.play();
        });
    };

    function handleFileSelect(input) {
        const file = input.files[0];
        const uploadArea = document.getElementById('fileUploadArea');
        const uploadContent = document.getElementById('fileUploadContent');
        const uploadPreview = document.getElementById('fileUploadPreview');
        const previewImage = document.getElementById('previewImageModal');
        const fileName = document.getElementById('fileName');

        if (file) {
            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
            if (!allowedTypes.includes(file.type)) {
                Swal.fire({
                    title: 'File Tidak Valid',
                    text: 'Hanya file JPG, PNG, dan JPEG yang diperbolehkan.',
                    icon: 'error',
                });
                input.value = '';
                return;
            }

            // Validate file size (5MB)
            if (file.size > 5 * 1024 * 1024) {
                Swal.fire({
                    title: 'File Terlalu Besar',
                    text: 'Ukuran file maksimal 5MB.',
                    icon: 'error',
                });
                input.value = '';
                return;
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                fileName.textContent = file.name;
                uploadContent.style.display = 'none';
                uploadPreview.style.display = 'block';
                uploadArea.classList.add('file-selected');
            }
            reader.readAsDataURL(file);
        }
    }

    function removeFile() {
        const input = document.getElementById('screenshot');
        const uploadArea = document.getElementById('fileUploadArea');
        const uploadContent = document.getElementById('fileUploadContent');
        const uploadPreview = document.getElementById('fileUploadPreview');

        input.value = '';
        uploadContent.style.display = 'block';
        uploadPreview.style.display = 'none';
        uploadArea.classList.remove('file-selected');
    }

    // Drag and drop functionality
    document.addEventListener('DOMContentLoaded', function() {
        const uploadArea = document.getElementById('fileUploadArea');
        const fileInput = document.getElementById('screenshot');

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            uploadArea.classList.add('drag-over');
        }

        function unhighlight(e) {
            uploadArea.classList.remove('drag-over');
        }

        uploadArea.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect(fileInput);
            }
        }
    });
</script>

<style>
    .file-upload-wrapper {
        position: relative;
    }

    .file-upload-area {
        border: 2px dashed #e1e5e9;
        border-radius: 10px;
        padding: 40px 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: #f8f9fa;
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .file-upload-area:hover {
        border-color: #007bff;
        background-color: #f0f8ff;
    }

    .file-upload-area.drag-over {
        border-color: #007bff;
        background-color: #e3f2fd;
        transform: scale(1.02);
    }

    .file-upload-area.file-selected {
        border-color: #28a745;
        background-color: #f8fff9;
    }

    .file-upload-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .file-upload-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
    }

    .preview-image {
        max-width: 200px;
        max-height: 150px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 15px;
    }

    .file-info {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: white;
        padding: 10px 15px;
        border-radius: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .file-name {
        font-weight: 500;
        color: #495057;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Animation for file upload */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .file-upload-preview {
        animation: fadeInUp 0.3s ease;
    }
</style>