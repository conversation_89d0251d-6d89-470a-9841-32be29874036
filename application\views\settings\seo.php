<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">SEO</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Akun</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">SEO</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="card mb-5">
        <div class="card-header">
            <div class="card-title m-0">
                <h3 class="m-0 fw-bold">Konfigurasi SEO</h3>
            </div>
        </div>

        <form id="frmSEO" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="card-body">
                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Deskripsi Usaha</label>
                    <input type="text" name="description" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Deskripsi" value="<?= $seo != null && isset($seo->description) ? $seo->description : null ?>" required>
                </div>

                <div class="mb-7">
                    <label class="form-label">Keyword Tags</label>
                    <input type="text" name="keywords" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Keyword Tags" value="<?= $seo != null && isset($seo->keywords) ? $seo->keywords : null ?>" required id="keyword_tags" />
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Schema.org - <a href="https://technicalseo.com/tools/schema-markup-generator/" target="_blank">Generate Schema.org</a></label>
                    <textarea name="schemaorg" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Struktur Schema.org" rows="5"><?= $seo != null && isset($seo->schemaorg) ? json_encode($seo->schemaorg, JSON_PRETTY_PRINT) : null ?></textarea>
                    <small>*Masukkan schema.org dengan text json</small><br>
                    <small>*Diwajibkan memberikan key <b>@context</b> dengan value <b>http(s)://schema.org</b></small><br>
                    <small>*Diwajibkan memberikan key <b>@type</b></small><br><br>
                    <small>Contoh Schema</small>

                    <!-- contoh schema -->
                    <pre class="bg-secondary p-2">
<code class="language-json">{
    "@context": "https://schema.org",
    "@type": "ProfessionalService",
    "name": "Karpel Developer Technology",
    "image": "",
    "@id": "",
    "url": "https://www.karpeldevtech.com/",
    "telephone": "085885263097",
    "priceRange": "1000000",
    "address": {
    "@type": "PostalAddress",
    "streetAddress": "Jl. Raya Karang Widoro",
    "addressLocality": "Malang",
    "postalCode": "65151",
    "addressCountry": "ID"
    },
    "geo": {
    "@type": "GeoCoordinates",
    "latitude": -7.9662353,
    "longitude": 112.6098275
    },
    "openingHoursSpecification": {
    "@type": "OpeningHoursSpecification",
    "dayOfWeek": [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday"
    ],
    "opens": "09:00",
    "closes": "17:00"
    } 
}</code></pre>
                </div>
            </div>

            <div class="card-footer d-flex justify-content-end py-6 px-9">
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    window.onload = function() {
        let keyword_tags = document.querySelector('#keyword_tags');

        new Tagify(keyword_tags);

        $.AjaxRequest('#frmSEO', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire('Berhasil', response.MESSAGE, 'success').then(function() {
                        return window.location.reload();
                    });
                } else {
                    return Swal.fire('Gagal', response.MESSAGE, 'error');
                }
            },
            error: function() {
                return Swal.fire('Gagal', 'Server sedang sibuk! Silahkan coba lagi nanti', 'error');
            }
        });
    };
</script>