<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property AdditionalPages $additionalpages
 * @property CI_DB_mysqli_driver $db
 * @property Datatables $datatables
 */
class AddPages extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('AdditionalPages', 'additionalpages');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Halaman Tambahan';
        $data['content'] = 'manage/addpages/index';

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tambah Halaman Tambahan';
        $data['content'] = 'manage/addpages/add';

        return $this->load->view('master', $data);
    }

    public function process_add_pages()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $title = getPost('title');
            $content = getPost('content');

            if ($title == null) {
                throw new Exception('Judul wajib diisi');
            } else if ($content == null) {
                throw new Exception('Konten harus diisi');
            } else {
                $title = removeSymbol($title);

                $htmlpurifierconfig = HTMLPurifier_Config::createDefault();
                $htmlpurifierconfig->set('HTML.SafeIframe', true);
                $htmlpurifierconfig->set('URI.SafeIframeRegexp', '%^https://(www.youtube.com/embed/)%');

                $purifier = new HTMLPurifier($htmlpurifierconfig);

                $content = $purifier->purify($content);
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['title'] = $title;
            $insert['content'] = $content;

            $this->additionalpages->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Halaman berhasil ditambahkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->additionalpages->get(array(
            'id' => $id,
            'userid' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/addpages'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Ubah Halaman Tambahan';
        $data['content'] = 'manage/addpages/edit';
        $data['additionalpage'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit_pages($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $get = $this->additionalpages->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $title = getPost('title');
            $content = getPost('content');

            if ($title == null) {
                throw new Exception('Judul wajib diisi');
            } else if ($content == null) {
                throw new Exception('Konten harus diisi');
            } else {
                $title = removeSymbol($title);

                $htmlpurifierconfig = HTMLPurifier_Config::createDefault();
                $htmlpurifierconfig->set('HTML.SafeIframe', true);
                $htmlpurifierconfig->set('URI.SafeIframeRegexp', '%^https://(www.youtube.com/embed/)%');

                $purifier = new HTMLPurifier($htmlpurifierconfig);

                $content = $purifier->purify($content);
            }

            $update = array();
            $update['title'] = $title;
            $update['content'] = $content;

            $this->additionalpages->update(array('id' => $id), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Halaman berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_pages()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->additionalpages->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $this->additionalpages->delete(array('id' => $id));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus halaman');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Halaman berhasil dihapus');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_pages()
    {
        try {
            if (isLogin() && getCurrentUser()->licenseid != null && isUser()) {
                $data = array();

                $datatables = $this->datatables->make('AdditionalPages', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.userid' => getCurrentIdUser()
                );

                foreach ($datatables->getData($where) as $key => $value) {
                    $detail = array();
                    $detail[] = $value->title;
                    $detail[] = "<a href=\"" . base_url('manage/addpages/edit/' . $value->id) . "\" class=\"btn btn-icon btn-warning btn-sm\">
                        <i class=\"fa fa-edit\"></i>
                    </a>

                    <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm\" onclick=\"deleteAdditionalPages('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-trash\"></i>
                    </a>";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
