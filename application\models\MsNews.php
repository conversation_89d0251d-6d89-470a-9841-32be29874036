<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsNews extends MY_Model
{
    protected $table = 'msnews';
    public $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.*, b.name AS typename')
            ->from($this->table . ' a')
            ->join('msnewstype b', 'b.id = a.typeid')
            ->order_by('a.createddate', 'DESC');

        return $this->db;
    }
}
