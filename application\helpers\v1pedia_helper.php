<?php
defined('BASEPATH') or exit('No direct script access allowed');

class V1Pedia
{
    private $api_id;
    private $api_key;
    private $secret_key;

    public function __construct($api_id, $api_key, $secret_key)
    {
        $this->api_id = $api_id;
        $this->api_key = $api_key;
        $this->secret_key = $secret_key;
    }

    public function services()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://v1pedia.com/api/services");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_id=$this->api_id&api_key=$this->api_key&secret_key=$this->secret_key");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function order($service, $target, $quantity)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://v1pedia.com/api/order");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_id=$this->api_id&api_key=$this->api_key&secret_key=$this->secret_key&service=$service&target=$target&quantity=$quantity");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function status($id)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "	https://v1pedia.com/api/status");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_id=$this->api_id&api_key=$this->api_key&secret_key=$this->secret_key&id=$id");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function profile()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://v1pedia.com/api/profile");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_id=$this->api_id&api_key=$this->api_key&secret_key=$this->secret_key");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }
}
