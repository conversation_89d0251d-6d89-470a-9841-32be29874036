<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PrivacyPolicy extends MY_Controller
{
    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $main = null;
        $member = null;

        if (file_exists('./application/templates/privacypolicy_main.txt')) {
            $main = file_get_contents('./application/templates/privacypolicy_main.txt');
        }

        if (file_exists('./application/templates/privacypolicy_member.txt')) {
            $member = file_get_contents('./application/templates/privacypolicy_member.txt');
        }

        $data = array();
        $data['title'] = 'Kebijakan Privasi';
        $data['content'] = 'manage/privacypolicy/index';
        $data['main'] = $main;
        $data['member'] = $member;

        return $this->load->view('master', $data);
    }

    public function process_edit_privacypolicy()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Anda belum login');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $main = getPost('main');
            $member = getPost('member');

            if ($main == null) {
                throw new Exception('Kebijakan Privasi Server PPOB & SMM tidak boleh kosong');
            } else if ($member == null) {
                throw new Exception('Kebijakan Privasi Member Server PPOB & SMM tidak boleh kosong');
            } else {
                $htmlpurifierconfig = HTMLPurifier_Config::createDefault();
                $purifier = new HTMLPurifier($htmlpurifierconfig);

                $main = $purifier->purify($main);
                $member = $purifier->purify($member);
            }

            file_put_contents(APPPATH . 'templates/privacypolicy_main.txt', $main);
            file_put_contents(APPPATH . 'templates/privacypolicy_member.txt', $member);

            return JSONResponseDefault('OK', 'Berhasil mengubah Kebijakan Privasi');
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
