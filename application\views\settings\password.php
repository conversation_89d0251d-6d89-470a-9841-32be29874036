<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Password</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Akun</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Password</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Ubah Password</h3>
                    </div>
                </div>

                <form id="frmChangePassword" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Password Lama</label>
                            <input type="password" name="password" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Password Lama" required>
                        </div>

                        <div class="mb-7">
                            <!--begin::Main wrapper-->
                            <div class="fv-row" data-kt-password-meter="true">
                                <!--begin::Wrapper-->
                                <div class="mb-1">
                                    <!--begin::Label-->
                                    <label class="form-label fw-semibold fs-6 mb-2">
                                        Password Baru
                                    </label>
                                    <!--end::Label-->

                                    <!--begin::Input wrapper-->
                                    <div class="position-relative mb-3">
                                        <input class="form-control form-control-lg form-control-solid" type="password" placeholder="Masukkan Password Baru" name="newpassword" autocomplete="off" />

                                        <!--begin::Visibility toggle-->
                                        <span class="btn btn-sm btn-icon position-absolute translate-middle top-50 end-0 me-n2" data-kt-password-meter-control="visibility">
                                            <i class="bi bi-eye-slash fs-2"></i>

                                            <i class="bi bi-eye fs-2 d-none"></i>
                                        </span>
                                        <!--end::Visibility toggle-->
                                    </div>
                                    <!--end::Input wrapper-->

                                    <!--begin::Highlight meter-->
                                    <div class="d-flex align-items-center mb-3" data-kt-password-meter-control="highlight">
                                        <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                                        <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                                        <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                                        <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px"></div>
                                    </div>
                                    <!--end::Highlight meter-->
                                </div>
                                <!--end::Wrapper-->

                                <!--begin::Hint-->
                                <div class="text-muted">
                                    Gunakan 8 karakter atau lebih dengan campuran huruf, angka & simbol.
                                </div>
                                <!--end::Hint-->
                            </div>
                            <!--end::Main wrapper-->
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" id="change_password_submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    let KTChangePassword = function() {
        var form;
        var submitButton;
        var validator;
        var passwordMeter;

        var handleForm = function(e) {
            validator = FormValidation.formValidation(form, {
                fields: {
                    'newpassword': {
                        validators: {
                            notEmpty: {
                                message: 'Password baru wajib diisi'
                            },
                            callback: {
                                message: 'Harap masukkan kata sandi yang valid',
                                callback: function(input) {
                                    if (input.value.length > 0) {
                                        return validatePassword();
                                    }
                                }
                            }
                        }
                    }
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger({
                        event: {
                            password: false
                        }
                    }),
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: '.fv-row',
                        eleInvalidClass: '', // comment to enable invalid state icons
                        eleValidClass: '' // comment to enable valid state icons
                    })
                }
            });

            submitButton.addEventListener('click', function(e) {
                e.preventDefault();

                validator.revalidateField('newpassword');

                validator.validate().then(function(status) {
                    if (status == 'Valid') {
                        let formData = $('#frmChangePassword').serialize();
                        let element = $('#frmChangePassword').find('input, button');
                        element.attr('disabled', true);

                        $.ajax({
                            url: form.getAttribute('action'),
                            method: form.getAttribute('method'),
                            dataType: 'json',
                            data: formData,
                            success: function(response) {
                                element.removeAttr('disabled');

                                if (response.RESULT == 'OK') {
                                    return Swal.fire({
                                        title: 'Berhasil',
                                        text: response.MESSAGE,
                                        icon: 'success'
                                    }).then(function(result) {
                                        return window.location.reload();

                                    });
                                } else {
                                    return Swal.fire({
                                        title: 'Gagal',
                                        text: response.MESSAGE,
                                        icon: 'error'
                                    });
                                }
                            }
                        }).fail(function() {
                            element.removeAttr('disabled');

                            return Swal.fire({
                                title: 'Gagal',
                                text: 'Server sedang sibuk silahkan coba lagi nanti',
                                icon: 'error'
                            });
                        })
                    }
                });
            });

            form.querySelector('input[name="newpassword"]').addEventListener('input', function() {
                if (this.value.length > 0) {
                    validator.updateFieldStatus('newpassword', 'NotValidated');
                }
            });
        };

        var validatePassword = function() {
            return (passwordMeter.getScore() === 100);
        }

        return {
            init: function() {
                form = document.querySelector('#frmChangePassword');
                submitButton = document.querySelector('#change_password_submit');
                passwordMeter = KTPasswordMeter.getInstance(form.querySelector('[data-kt-password-meter="true"]'));

                handleForm();
            }
        };
    }();

    window.onload = function() {
        KTUtil.onDOMContentLoaded(function() {
            KTChangePassword.init();
        });
    };
</script>