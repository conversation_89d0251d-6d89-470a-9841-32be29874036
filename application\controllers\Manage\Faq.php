<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsFaq $msfaq
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver|CI_DB_query_builder $db
 */
class Faq extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsFaq', 'msfaq');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - FAQ';
        $data['content'] = 'manage/faq/index';

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - FAQ';
        $data['content'] = 'manage/faq/add';

        return $this->load->view('master', $data);
    }

    public function process_add_faq()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $title = getPost('title');
            $content = getPost('content');

            if ($title == null) {
                throw new Exception('Judul FAQ tidak boleh kosong!');
            } else if ($content == null) {
                throw new Exception('Konten FAQ tidak boleh kosong!');
            } else {
                $title = removeSymbol($title);

                $htmlpurifierconfig = HTMLPurifier_Config::createDefault();
                $purifier = new HTMLPurifier($htmlpurifierconfig);

                $content = $purifier->purify($content);
            }

            $data = array(
                'title' => $title,
                'content' => $content,
                'createddate' => getCurrentDate(),
                'createdby' => getCurrentIdUser(),
                'userid' => getCurrentIdUser(),
            );

            $this->msfaq->insert($data);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('FAQ gagal ditambahkan!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'FAQ berhasil ditambahkan!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_faq()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();

                $datatables = $this->datatables->make('MsFaq', 'QueryDatatables', 'SearchDatatables');

                foreach (
                    $datatables->getData(array(
                        'userid' => getCurrentIdUser()
                    )) as $key => $value
                ) {
                    $detail = array();
                    $detail[] = $value->title;
                    $detail[] = $value->content;
                    $detail[] = "<a href=\"" . base_url('manage/faq/edit/' . $value->id) . "\") class=\"btn btn-icon btn-warning btn-sm mb-1\">
                        <i class=\"fa fa-edit\"></i>
                    </a>

                    <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm  mb-1\" onclick=\"deleteFaq('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-trash\"></i>
                    </a>";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Anda tidak memiliki akses!');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->msfaq->get(array(
            'id = ' => $id,
            'userid' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/faq'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Faq';
        $data['content'] = 'manage/faq/edit';
        $data['faq'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit_faq($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $get = $this->msfaq->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('FAQ tidak ditemukan!');
            }

            $title = getPost('title');
            $content = getPost('content');

            if ($title == null) {
                throw new Exception('Judul FAQ tidak boleh kosong!');
            } else if ($content == null) {
                throw new Exception('Konten FAQ tidak boleh kosong!');
            } else {
                $title = removeSymbol($title);

                $htmlpurifierconfig = HTMLPurifier_Config::createDefault();
                $purifier = new HTMLPurifier($htmlpurifierconfig);

                $content = $purifier->purify($content);
            }

            $update = array();
            $update['title'] = $title;
            $update['content'] = $content;
            $update['userid'] = getCurrentIdUser();
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msfaq->update(array(
                'id = ' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('FAQ gagal diubah!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'FAQ berhasil diubah!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_faq()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = stringEncryption('decrypt', getPost('id'));

            $faq = $this->msfaq->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($faq == 0) {
                throw new Exception('FAQ tidak ditemukan!');
            }

            $this->msfaq->delete(array(
                'id' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('FAQ gagal dihapus!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'FAQ berhasil dihapus!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
