<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsOnvayaAcademy $msonvayaacademy
 * @property CI_DB_mysqli_driver|CI_DB_query_builder $db
 */
class Academy extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsOnvayaAcademy', 'msonvayaacademy');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        }

        // Get all academy videos
        $videos = $this->msonvayaacademy->order_by('createddate', 'DESC')->get();

        $data = array();
        $data['title'] = 'Onvaya Academy';
        $data['content'] = 'academy/index';
        $data['videos'] = $videos->result();

        return $this->load->view('master', $data);
    }

    public function detail($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        }

        if (empty($id) || !is_numeric($id)) {
            return redirect(base_url('academy'));
        }

        // Get specific video
        $video = $this->msonvayaacademy->get(array('id' => $id));

        if ($video->num_rows() == 0) {
            return redirect(base_url('academy'));
        }

        $data = array();
        $data['title'] = 'Detail Video - ' . $video->row()->title;
        $data['content'] = 'academy/detail';
        $data['video'] = $video->row();

        return $this->load->view('master', $data);
    }

    private function getYouTubeEmbedUrl($url)
    {
        // Extract video ID from various YouTube URL formats
        $video_id = '';

        if (preg_match('/youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/', $url, $matches)) {
            $video_id = $matches[1];
        } elseif (preg_match('/youtu\.be\/([a-zA-Z0-9_-]+)/', $url, $matches)) {
            $video_id = $matches[1];
        } elseif (preg_match('/youtube\.com\/embed\/([a-zA-Z0-9_-]+)/', $url, $matches)) {
            $video_id = $matches[1];
        }

        if (!empty($video_id)) {
            return "https://www.youtube.com/embed/" . $video_id;
        }

        return $url; // Return original URL if no match found
    }

    public function get_embed_url($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        if (empty($id) || !is_numeric($id)) {
            return JSONResponseDefault('FAILED', 'Invalid video ID');
        }

        $video = $this->msonvayaacademy->get(array('id' => $id));

        if ($video->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Video not found');
        }

        $embed_url = $this->getYouTubeEmbedUrl($video->row()->youtube_link);

        return JSONResponse(array(
            'RESULT' => 'OK',
            'embed_url' => $embed_url
        ));
    }
}
