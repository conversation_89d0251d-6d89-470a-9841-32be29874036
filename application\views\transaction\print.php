<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>

    <style>
        body {
            max-width: 768px;
            background: url(<?= base_url('assets/media/illustrations/waves.png') ?>);
            background-repeat: no-repeat;
            background-size: cover;
            font-family: 'Courier New', Courier, monospace;
            color: #333;
            margin: auto;
            height: 100vh;
            padding: 1rem;
        }

        .card {
            padding: 1rem;
            padding-left: 2rem;
            padding-right: 2rem;
            background-color: #fff;
            border: 1px solid #ccc;
            max-width: 500px;
            width: 100%;
            margin: 0 auto;
            border-radius: 5px;
        }

        .container {
            display: flex;
            width: 100%;
            height: 100%;
            align-items: center;
        }

        .logo {
            width: 150px;
            height: 150px;
            background-color: #eee;
            margin: auto;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 1px solid #ccc;
            text-align: center;
        }

        .address {
            margin-top: 1.5rem;
            font-size: 12px;
            text-align: center;
        }

        .dashed {
            border: 1px dashed #333;
        }

        .noted {
            padding: 1rem;
            text-align: center;
            font-size: 14px;
        }

        table {
            width: 100%;
        }

        table tr {
            /* display: inline-flex; */
            justify-content: space-between;
        }

        table tr td {
            font-size: 14px;
            line-break: anywhere;
        }

        table tr td:last-of-type {
            text-align: right;
            width: 65%;
        }

        .text-center {
            text-align: center;
        }

        /* mobile size */
        @media (max-width: 768px) {
            table tr {
                flex-direction: column;
                margin-bottom: 1rem;
            }

            table tr td:last-of-type {
                text-align: left;
                width: 100%;
            }
        }
    </style>
</head>

<body style="text-align: center; max-height: 100%;">
    <?php if ($transaction->companyicon == null) : ?>
        <div class="logo">
            <span><?= $transaction->companyname ?></span>
        </div>
    <?php else : ?>
        <div class="text-center">
            <div class="text-center">
                <?php

                $path = $transaction->companyicon;
                $type = pathinfo($path, PATHINFO_EXTENSION);
                $data = file_get_contents($path);
                $base64 = 'data:image/' . $type . ';base64,' . base64_encode($data);
                ?>
                <img src="<?= $base64 ?>" alt="" width="125px" height="125px" style="border-radius: 5px;">
            </div>
        </div>
    <?php endif; ?>

    <p class="address"><?= $transaction->companyaddress ?></p>

    <hr class="dashed">

    <table>
        <?php if ($transaction->strukcontent == null) : ?>
            <tr>
                <td>ID</td>
                <td><?= $transaction->clientcode ?></td>
            </tr>

            <tr>
                <td>Produk</td>
                <td><?= $transaction->productname ?? '- Produk telah dihapus -' ?></td>
            </tr>

            <tr>
                <td>Harga</td>
                <td>Rp <?= IDR($transaction->price) ?></td>
            </tr>

            <tr>
                <td>Tanggal Proses</td>
                <td><?= DateFormat($transaction->updateddate, 'd F Y H:i:s') ?></td>
            </tr>

            <tr>
                <td>Tujuan</td>
                <td><?= $transaction->target ?></td>
            </tr>

            <tr>
                <td>Status</td>
                <td><?= ucfirst($transaction->status) ?></td>
            </tr>
        <?php else : ?>
            <?php
            $decode = json_decode($transaction->strukcontent);
            $numformatcol = array('startcount', 'remain', 'qty');
            ?>

            <?php foreach ($decode as $key => $value) : ?>
                <?php
                if (!isset(strukContent()[$value]))
                    continue;

                if ($transaction->category_apikey == 'PPOB') {
                    $exception_col = array('startcount', 'remain', 'qty');

                    if (in_array($value, $exception_col))
                        continue;
                } else if ($transaction->category_apikey == 'SMM') {
                    $exception_col = array('sn');

                    if (in_array($value, $exception_col))
                        continue;
                }
                ?>

                <tr>
                    <td style="position:absolute; left:0pt; width:130pt; "><?= strukContent()[$value] ?></td>
                    <?php if ($value != 'aftercutsaldo' && !in_array($value, $numformatcol)) : ?>
                        <td style="margin-left:150pt; text-align:right; max-width:50px; overflow-wrap:break-word;">
                            <?php if ($value == 'currentsaldo') : ?>
                                Rp <?= IDR($transaction->$value) ?>
                            <?php elseif ($value == 'price') : ?>
                                Rp <?= IDR($price) ?? 0 ?>
                            <?php else : ?>
                                <?= $transaction->$value ?>
                            <?php endif; ?>
                        </td>
                    <?php elseif (in_array($value, $numformatcol)) : ?>
                        <td style="margin-left:150pt; text-align:right;"><?= IDR($transaction->$value) ?></td>
                    <?php else : ?>
                        <td style="margin-left:150pt; text-align:right;">Rp <?= IDR($transaction->currentsaldo - $transaction->price) ?></td>
                    <?php endif; ?>
                </tr>
            <?php endforeach; ?>
        <?php endif; ?>
    </table>

    <hr class="dashed">

    <div class="noted">
        SIMPAN STRUK INI, STRUK INI MERUPAKAN <br>
        BUKTI PEMBAYARAN SAH <br>
        www.<?= $transaction->domain ?>
    </div>
</body>

</html>