<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Filter Riwayat Topup</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <div class="modal-body">
            <div class="row">
                <div class="mb-7 col-md-6">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Tanggal</label>
                    <input type="date" name="date" id="date" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                </div>

                <div class="mb-7 col-md-6">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Kode Topup</label>
                    <input type="code" name="code" id="code" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Kode Topup">
                </div>

                <div class="mb-7 col-md-6">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Member</label>
                    <select name="users" id="users" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                        <option value="">Pilih Member</option>
                        <?php foreach ($users as $val) : ?>
                            <option value="<?= $val->userid ?>"><?= $val->name ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="mb-7 col-md-6">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Metode Pembayaran</label>
                    <select name="payment" id="payment" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                        <option value="">Pilih Metode Pembayaran</option>
                        <option value="Otomatis">Otomatis</option>
                        <option value="Manual">Manual</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
            <button type="button" id="filter" class="btn btn-primary">Filter</button>
        </div>
    </div>
</div>

<script>
    $('#filter').click(function() {
        let date = $('input[name=date]').val();
        let code = $('input[name="code"]').val();
        let users = $('#users').val();
        let payment = $('#payment').val();

        $('#ModalGlobal').modal('hide');
        $('.datatables-history').DataTable().destroy();
        $('.datatables-history').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            ordering: false,
            ajax: {
                url: '<?= base_url('admins/deposit/history/datatables') ?>',
                method: 'POST',
                data: {
                    date: date,
                    code: code,
                    users: users,
                    payment: payment,
                }
            }
        });
    });
</script>