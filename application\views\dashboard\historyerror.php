<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-xl">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Riwayat Error</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <div class="modal-body">
            <div class="table-responsive">
                <table class="table table-striped table-row-bordered gy-5 datatables-logerror">
                    <thead>
                        <tr class="fw-semibold fs-6 text-muted">
                            <th>Tipe</th>
                            <th>Respon</th>
                            <th>Tanggal</th>
                        </tr>
                    </thead>

                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
        </div>
    </div>
</div>

<script>
    $('.datatables-logerror').DataTable({
        responsive: true,
        processing: true,
        serverSide: true,
        stateSave: true,
        ordering: false,
        ajax: {
            url: '<?= base_url(uri_string() . '/datatables') ?>',
            method: 'POST',
            data: {
                id: <?= $id ?>
            }
        }
    });
</script>