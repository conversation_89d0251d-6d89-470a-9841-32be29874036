<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsUsers $msusers
 * @property HistoryBalance $historybalance
 * @property CI_DB_mysqli_driver $db
 * @property MsLicense $mslicense
 */
class Upgrade extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsUsers', 'msusers');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('MsLicense', 'mslicense');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Upgrade Akun';
        $data['content'] = 'upgrade/index';

        return $this->load->view('master', $data);
    }
}
