<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!--begin::Toolbar-->
<div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
    <!--begin::Toolbar container-->
    <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
        <!--begin::Page title-->
        <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
            <!--begin::Title-->
            <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Syarat dan <PERSON></h1>
            <!--end::Title-->
            <!--begin::Breadcrumb-->
            <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                <!--begin::Item-->
                <li class="breadcrumb-item text-muted">
                    <a href="<?= base_url('dashboard') ?>" class="text-muted text-hover-primary">Home</a>
                </li>
                <!--end::Item-->
                <!--begin::Item-->
                <li class="breadcrumb-item">
                    <span class="bullet bg-gray-400 w-5px h-2px"></span>
                </li>
                <!--end::Item-->
                <!--begin::Item-->
                <li class="breadcrumb-item text-muted">Manage</li>
                <!--end::Item-->
                <!--begin::Item-->
                <li class="breadcrumb-item">
                    <span class="bullet bg-gray-400 w-5px h-2px"></span>
                </li>
                <!--end::Item-->
                <!--begin::Item-->
                <li class="breadcrumb-item text-muted">Syarat dan Ketentuan</li>
                <!--end::Item-->
            </ul>
            <!--end::Breadcrumb-->
        </div>
        <!--end::Page title-->
    </div>
    <!--end::Toolbar container-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <form id="frmTermsOfService" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Syarat dan Ketentuan Server PPOB & SMM</label>
                            <textarea id="termsofservice_main"><?= $main ?></textarea>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Syarat dan Ketentuan Member Server PPOB & SMM</label>
                            <textarea id="termsofservice_member"><?= $member ?></textarea>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    var editorTermsOfService_main, editorTermsOfService_member;

    window.onload = function() {
        ClassicEditor
            .create(document.querySelector('#termsofservice_main'))
            .then(response => {
                editorTermsOfService_main = response;
            })
            .catch(responseError => {

            });

        ClassicEditor
            .create(document.querySelector('#termsofservice_member'))
            .then(response => {
                editorTermsOfService_member = response;
            })
            .catch(responseError => {

            });

        $('#frmTermsOfService').submit(function(e) {
            e.preventDefault();

            var formData = new FormData();
            formData.append('<?= $this->security->get_csrf_token_name() ?>', $('input[name="<?= $this->security->get_csrf_token_name() ?>"]').val());
            formData.append('termsofservice_main', editorTermsOfService_main.getData());
            formData.append('termsofservice_member', editorTermsOfService_member.getData());

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'JSON',
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.MESSAGE,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                location.reload();
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Gagal!',
                            text: response.MESSAGE,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan sistem',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        });
    };
</script>
