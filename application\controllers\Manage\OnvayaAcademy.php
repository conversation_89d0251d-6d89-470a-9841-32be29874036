<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsOnvayaAcademy $msonvayaacademy
 * @property CI_Upload $upload
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver|CI_DB_query_builder $db
 */
class OnvayaAcademy extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsOnvayaAcademy', 'msonvayaacademy');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Onvaya Academy';
        $data['content'] = 'manage/onvayaacademy/index';

        return $this->load->view('master', $data);
    }

    public function datatables_academy()
    {
        try {
            if (isLogin() && isAdmin()) {
                $data = array();

                $datatables = $this->datatables->make('MsOnvayaAcademy', 'QueryDatatables', 'SearchDatatables');

                $where = array();

                foreach ($datatables->getData($where) as $key => $value) {
                    $detail = array();
                    $detail[] = "<img src=\"" . base_url('uploads/' . $value->thumbnail) . "\" width=\"100\" class=\"rounded\">";
                    $detail[] = $value->title;
                    $detail[] = substr($value->description, 0, 100) . (strlen($value->description) > 100 ? '...' : '');
                    $detail[] = "<a href=\"" . $value->youtube_link . "\" target=\"_blank\" class=\"btn btn-sm btn-danger\">
                        <i class=\"fab fa-youtube\"></i> Watch
                    </a>";

                    $detail[] = "<a href=\"" . base_url('manage/onvayaacademy/edit/' . $value->id) . "\" class=\"btn btn-icon btn-warning btn-sm mb-1\">
                        <i class=\"fa fa-edit\"></i>
                    </a>

                    <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"deleteAcademy('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-trash\"></i>
                    </a>";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Video Academy';
        $data['content'] = 'manage/onvayaacademy/add';

        return $this->load->view('master', $data);
    }

    public function process_add_academy()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $title = getPost('title');
            $description = getPost('description');
            $youtube_link = getPost('youtube_link');

            if (empty($title)) {
                throw new Exception('Judul tidak boleh kosong');
            } else if (empty($description)) {
                throw new Exception('Deskripsi tidak boleh kosong');
            } else if (empty($youtube_link)) {
                throw new Exception('Link YouTube tidak boleh kosong');
            }

            // Validate YouTube URL
            if (!$this->isValidYouTubeUrl($youtube_link)) {
                throw new Exception('Link YouTube tidak valid');
            }

            // Handle thumbnail upload
            if (!isset($_FILES['thumbnail']['size']) || $_FILES['thumbnail']['size'] == 0) {
                throw new Exception('Thumbnail wajib diupload');
            }

            $config = array(
                'upload_path' => './uploads/',
                'allowed_types' => 'jpg|jpeg|png',
                'max_size' => 2048,
                'encrypt_name' => true,
            );

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('thumbnail')) {
                throw new Exception($this->upload->display_errors('', ''));
            }

            $upload = $this->upload->data();

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['title'] = removeSymbol($title);
            $insert['description'] = $description;
            $insert['youtube_link'] = $youtube_link;
            $insert['thumbnail'] = $upload['file_name'];
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();
            $insert['updateddate'] = getCurrentDate();
            $insert['updatedby'] = getCurrentIdUser();

            $this->msonvayaacademy->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan video academy');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menambahkan video academy');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->msonvayaacademy->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/onvayaacademy'));
        }

        $data = array();
        $data['title'] = 'Edit Video Academy';
        $data['content'] = 'manage/onvayaacademy/edit';
        $data['academy'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit_academy($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $get = $this->msonvayaacademy->get(array(
                'id' => $id
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Video academy tidak ditemukan');
            }

            $row = $get->row();

            $title = getPost('title');
            $description = getPost('description');
            $youtube_link = getPost('youtube_link');

            if (empty($title)) {
                throw new Exception('Judul tidak boleh kosong');
            } else if (empty($description)) {
                throw new Exception('Deskripsi tidak boleh kosong');
            } else if (empty($youtube_link)) {
                throw new Exception('Link YouTube tidak boleh kosong');
            }

            // Validate YouTube URL
            if (!$this->isValidYouTubeUrl($youtube_link)) {
                throw new Exception('Link YouTube tidak valid');
            }

            $update = array();
            $update['title'] = removeSymbol($title);
            $update['description'] = $description;
            $update['youtube_link'] = $youtube_link;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            // Handle thumbnail upload if new file is uploaded
            if (isset($_FILES['thumbnail']['size']) && $_FILES['thumbnail']['size'] > 0) {
                $config = array(
                    'upload_path' => './uploads/',
                    'allowed_types' => 'jpg|jpeg|png',
                    'max_size' => 2048,
                    'encrypt_name' => true,
                );

                $this->load->library('upload', $config);

                if (!$this->upload->do_upload('thumbnail')) {
                    throw new Exception($this->upload->display_errors('', ''));
                } else {
                    $upload = $this->upload->data();
                    $update['thumbnail'] = $upload['file_name'];

                    // Delete old thumbnail
                    if ($row->thumbnail != null && file_exists('./uploads/' . $row->thumbnail)) {
                        unlink('./uploads/' . $row->thumbnail);
                    }
                }
            }

            $this->msonvayaacademy->update(array('id' => $id), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah video academy');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil mengubah video academy');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_academy()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            if ($id == null) {
                throw new Exception('ID video academy tidak boleh kosong');
            }

            $get = $this->msonvayaacademy->get(array(
                'id' => $id
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Video academy tidak ditemukan');
            }

            $row = $get->row();

            // Delete thumbnail file
            if ($row->thumbnail != null && file_exists('./uploads/' . $row->thumbnail)) {
                unlink('./uploads/' . $row->thumbnail);
            }

            $this->msonvayaacademy->delete(array('id' => $id));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus video academy');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menghapus video academy');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    private function isValidYouTubeUrl($url)
    {
        $pattern = '/^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)[a-zA-Z0-9_-]{11}$/';
        return preg_match($pattern, $url);
    }
}
