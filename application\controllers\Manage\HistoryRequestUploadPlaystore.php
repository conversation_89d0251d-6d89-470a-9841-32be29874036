<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Datatables $datatables
 * @property MsUsers $msusers
 * @property HistoryBalance $historybalance
 * @property QueueUploadPlaystore $queueuploadplaystore
 * @property CI_DB_mysqli_driver $db
 * @property CI_Upload $upload
 */
class HistoryRequestUploadPlaystore extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsUsers', 'msusers');
        $this->load->model('QueueUploadPlaystore', 'queueuploadplaystore');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Pengajuan Playstore';
        $data['content'] = 'manage/historyrequestuploadplaystore/index';

        return $this->load->view('master', $data);
    }

    public function datatables_history_uploadplaystore()
    {
        try {
            if (isLogin() && isAdmin()) {
                $data = array();
                $datatable = $this->datatables->make('QueueUploadPlaystore', 'QueryDatatables', 'SearchDatatables');

                foreach ($datatable->getData() as $key => $value) {
                    $detail = array();

                    if ($value->status == 'Pending') {
                        $status = "<span class=\"badge badge-warning\">Menunggu</span>";
                    } else if ($value->status == 'Success') {
                        $status = "<span class=\"badge badge-success\">Berhasil</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger\">Gagal</span>";
                    }

                    $actions = "";

                    if ($value->status == 'Pending') {
                        $actions .= "<a href=\"javascript:;\" class=\"btn btn-success btn-sm mb-1\" onclick=\"confirmUploadPlaystore('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-check\"></i>
                            <span>Konfirmasi</span>
                        </a>

                        <button type=\"button\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"cancelUploadPlaystore('" . $value->id . "')\">
                            <i class=\"fa fa-times\"></i>
                            <span>Cancel</span>
                        </button>";
                    } else if ($value->status == 'Failed') {
                        $actions .= "<button type=\"button\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"modalReason('" . $value->id . "')\">
                            <i class=\"fa fa-book\"></i>
                            <span>Alasan</span>
                        </button>";
                    } else {
                        $actions = "N/A";
                    }

                    $getuser = $this->msusers->get(array(
                        'id' => $value->userid
                    ))->row();

                    $detail[] = $value->name;
                    $detail[] = $value->email;
                    $detail[] = $value->companyname;
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = $value->updateddate != null ? DateFormat($value->updateddate, 'd F Y H:i:s') : '-';
                    $detail[] = $value->appbundle_link != null ? "<a href=\"https://" . $getuser->domain . "/uploads/" . $value->appbundle_link . "\" target=\"_blank\">" . $value->appbundle_link . "</a>" : '-';
                    $detail[] = $status;
                    $detail[] = $actions;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_confirm_uploadplaystore()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->queueuploadplaystore->total(array(
                'id' => $id,
                'status' => 'Pending'
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $update = array();
            $update['status'] = 'Success';

            $this->queueuploadplaystore->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal melakukan konfirmasi');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil dikonfirmasi');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function cancel_uploadplaystore($id)
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $get = $this->queueuploadplaystore->total(array(
                'id' => $id,
                'status' => 'Pending'
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view(
                    'manage/historyrequestuploadplaystore/modalrejected',
                    array(),
                    true
                )
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_cancel_uploadplaystore($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $rejectedreason = getPost('rejectedreason');
            $rejectedimage = $_FILES['rejectedimage'];

            if ($rejectedreason == null) {
                throw new Exception('Alasan penolakan harus diisi');
            } else if ($rejectedimage['size'] == 0) {
                throw new Exception('Bukti penolakan harus diisi');
            }

            $get = $this->queueuploadplaystore->total(array(
                'id' => $id,
                'status' => 'Pending'
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $update = array();
            $update['status'] = 'Failed';
            $update['rejectedreason'] = $rejectedreason;

            $config = array(
                'upload_path' => './uploads/',
                'allowed_types' => 'jpg|jpeg|png',
                'encrypt_name' => true,
            );

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('rejectedimage')) {
                throw new Exception($this->upload->display_errors('', ''));
            } else {
                $upload = $this->upload->data();

                $update['rejectedimage'] = $upload['file_name'];
            }


            $this->queueuploadplaystore->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal membatalkan Pengajuan Playstore');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Pengajuan Playstore berhasil dibatalkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function modal_reason()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $get = $this->queueuploadplaystore->get(array(
                'id' => $id,
                'status' => 'Failed'
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view(
                    'manage/historyrequestuploadplaystore/modalreason',
                    array('reason' => $get->row()),
                    true
                )
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
