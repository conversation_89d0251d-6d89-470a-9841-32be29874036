<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Tambah Kategori Produk</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmAddCategory" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off" enctype="multipart/form-data">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Nama Kategori</label>
                    <input type="text" name="name" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Kategori" required>
                </div>

                <div class="mb-7" id="display_nonpascabayar">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Icon</label>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Pilih dari Galeri</label>
                            <select name="asset" class="form-control form-control-lg form-control-solid">
                                <option value="">- Pilih -</option>
                                <?php foreach ($icons as $key => $value) : ?>
                                    <option value="<?= $value->id ?>"><?= $value->assetname ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Atau Upload Icon Baru</label>
                            <input type="file" name="icon_file" class="form-control form-control-lg form-control-solid" accept=".jpg,.jpeg,.png">
                            <small class="text-muted">Format: JPG, JPEG, PNG. Maks: 2MB</small>
                        </div>
                    </div>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Kategori Layanan</label>
                    <select name="servicecategory" class="form-select form-select-solid" required>
                        <option value="">- Pilih -</option>
                        <option value="PPOB">PPOB</option>
                        <option value="SMM">SMM</option>
                    </select>
                </div>

                <div class="mb-7 d-none" id="display_ppob">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Kategori Layanan</label>
                    <select name="servicesubcategory" class="form-select form-select-solid">
                        <option value="">- Pilih -</option>
                        <option value="PRABAYAR">PRABAYAR</option>
                        <option value="PASCABAYAR">PASCABAYAR</option>
                    </select>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    $.AjaxRequest('#frmAddCategory', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success',
                }).then(function(result) {
                    return window.location.reload();
                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.MESSAGE,
                    icon: 'error',
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        }
    });

    $('select[name=servicecategory]').change(function() {
        if ($(this).val() == 'PPOB') {
            $('#display_ppob').removeClass('d-none').find('select').attr('required', true);

            if ($('select[name=servicesubcategory]').val() != 'PASCABAYAR') {
                $('#display_nonpascabayar').removeClass('d-none');
            } else {
                $('#display_nonpascabayar').addClass('d-none');
            }
        } else if ($(this).val() == 'SMM') {
            $('#display_ppob').addClass('d-none').find('select').removeAttr('required');
            $('#display_nonpascabayar').removeClass('d-none');
        } else {
            $('#display_ppob').addClass('d-none').find('select').removeAttr('required');
        }
    });

    $('select[name=servicesubcategory]').change(function() {
        if ($(this).val() == 'PASCABAYAR') {
            $('#display_nonpascabayar').addClass('d-none');
        } else if ($(this).val() == 'PRABAYAR') {
            $('#display_nonpascabayar').removeClass('d-none');
        }
    });
</script>