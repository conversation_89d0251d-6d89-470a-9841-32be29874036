<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Invoices $invoices
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver $db
 * @property MsUsers $users
 */
class Invoice extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Invoices', 'invoices');
        $this->load->model('MsUsers', 'users');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tagihan';
        $data['content'] = 'manage/invoices/index';

        return $this->load->view('master', $data);
    }

    public function datatables_invoices()
    {
        try {
            if (isLogin() && (isUser())) {
                $data = array();

                $datatables = $this->datatables->make('Invoices', 'QueryDatatables', 'SearchDatatables');

                foreach ($datatables->getData(array('userid' => getCurrentIdUser())) as $key => $value) {
                    if ($value->status == 'Pending') {
                        $status = "<span class=\"badge badge-warning\">Menunggu</span>";
                    } else if ($value->status == 'Success' || $value->status == 'Paid') {
                        $status = "<span class=\"badge badge-success\">Berhasil</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger\">Gagal</span>";
                    }

                    $detail = array();
                    $detail[] = $value->invoicecode;
                    $detail[] = DateFormat($value->createddate, 'd M Y H:i:s');
                    $detail[] = $value->description;
                    $detail[] = $value->note;
                    $detail[] = IDR($value->nominal);
                    $detail[] = $status;

                    if ($value->status == 'Pending') {
                        $detail[] = "<button type=\"button\" class=\"btn btn-primary btn-sm mb-1 me-1\" onclick=\"pay('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-credit-card\"></i>
                            <span>Bayar Pakai Saldo</span>
                        </button>";
                    } else {
                        $detail[] = "N/A";
                    }

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function pay()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda harus login terlebih dahulu');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = stringEncryption('decrypt', getPost('id'));

            $get = $this->invoices->get(array(
                'id' => $id,
                'userid' => getCurrentIdUser(),
                'status' => 'Pending'
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $balance = getCurrentBalance(getCurrentIdUser(), true);

            if ($balance < $row->nominal) {
                throw new Exception('Saldo anda tidak mencukupi');
            }

            $update = array();
            $update['status'] = 'Paid';
            $update['updateddate'] = date('Y-m-d H:i:s');

            $this->invoices->update(array('id' => $id), $update);

            $currentuser = getCurrentUser(getCurrentIdUser());

            if ($currentuser->expireddate > getCurrentDate()) {
                $expireddate = date('Y-m-d H:i:s', strtotime('+1 month', strtotime($currentuser->expireddate)));
            } else {
                $expireddate = date('Y-m-d H:i:s', strtotime('+1 month', strtotime(getCurrentDate())));
            }

            $updateUser = array();
            $updateUser['balance'] = $balance - $row->nominal;
            $updateUser['expireddate'] = $expireddate;
            $updateUser['updateddate'] = date('Y-m-d H:i:s');

            $this->users->update(array('id' => getCurrentIdUser()), $updateUser);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal membayar tagihan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil membayar tagihan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
