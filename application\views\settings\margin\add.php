<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Tambah Margin/Profit</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmAddMargin" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Kategori</label>
                    <select name="category" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                        <option value="">- Pilih -</option>
                        <?php if ($this->user->companycategory == 'PPOB & SMM' || $this->user->companycategory == 'PPOB') : ?>
                            <option value="Prabayar">Prabayar</option>
                            <option value="Pascabayar">Pascabayar</option>
                        <?php endif; ?>
                        <?php if ($this->user->companycategory == 'PPOB & SMM' || $this->user->companycategory == 'SMM') : ?>
                            <option value="Media Sosial">Media Sosial</option>
                        <?php endif; ?>
                    </select>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Harga Minimal</label>
                    <input type="number" name="minprice" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Harga Minimal" required>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Harga Maksimal</label>
                    <input type="number" name="maxprice" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Harga Maksimal" required>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Tipe Margin/Profit</label>
                    <select name="margintype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                        <option value="Nominal">Nominal</option>
                        <option value="Persentase" <?= getCurrentUser()->licenseid == null ? 'disabled' : null ?>>Persentase <?= getCurrentUser()->licenseid == null ? '(Premium Plan)' : null ?></option>
                    </select>

                    <div class="mt-2">
                        <small>*Jika memilih <b>Nominal</b> maka keuntungan anda akan diambil sesuai dengan apa yang anda inputkan pada <b>Jumlah Margin/Profit</b></small><br>
                        <small>*Jika memilih <b>Persentase</b> maka keuntungan anda akan diambil dari harga produk * persentase yang anda inputkan pada <b>Jumlah Margin/Profit</b></small>
                    </div>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Jumlah Margin/Profit</label>
                    <input type="number" name="profit" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah Margin/Profit" required>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    $.AjaxRequest('#frmAddMargin', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success',
                }).then(function(result) {
                    return window.location.reload();
                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.MESSAGE,
                    icon: 'error',
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        }
    });
</script>