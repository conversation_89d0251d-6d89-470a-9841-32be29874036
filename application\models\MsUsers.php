<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsUsers extends MY_Model
{
    protected $table = 'msusers';
    public $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.*, COALESCE(b.total, 0) as transactiontotal, COALESCE(c.total, 0) as transactiontotalppob, COALESCE(d.total, 0) as transactiontotalsmm, e.rolename')
            ->from($this->table . ' a')
            ->join("(select userid, count(*) as total from trorder group by userid) b", 'b.userid = a.id', 'LEFT')
            ->join("(select userid, count(*) as total from trorder where type = 'PPOB' group by userid) c", 'c.userid = a.id', 'LEFT')
            ->join("(select userid, count(*) as total from trorder where type = 'SMM' group by userid) d", 'd.userid = a.id', 'LEFT')
            ->join('msrole e', 'e.id = a.roleid', 'LEFT')
            ->order_by('b.total, c.total, d.total', 'DESC');

        return $this;
    }

    public function QueryDatatables_member()
    {
        $this->db->select('a.*, b.total, c.total as totalusers')
            ->from($this->table . ' a')
            ->join("(SELECT b.merchantid, count(*) AS total FROM trorder a JOIN msusers b ON b.id = a.userid WHERE MONTH(a.createddate) = MONTH(now()) AND YEAR(a.createddate) = YEAR(now()) GROUP BY b.merchantid) b", 'b.merchantid = a.id', 'LEFT')
            ->join("(select merchantid, count(*) as total from msusers group by merchantid) c", 'c.merchantid = a.id', 'LEFT')
            ->order_by('a.expireddate', 'DESC');

        return $this;
    }

    public function QueryDatatables_Monitoring_Member()
    {
        $this->db->select('a.*, b.total as totalordermonth , c.total as totalordertoday,d.total as totalorder')
            ->from($this->table . ' a')
            ->join("(SELECT b.merchantid, count(*) AS total FROM trorder a JOIN msusers b ON b.id = a.userid WHERE MONTH(a.createddate) = MONTH(now()) AND YEAR(a.createddate) = YEAR(now()) GROUP BY b.merchantid) b", 'b.merchantid = a.id', 'LEFT')
            ->join("(SELECT c.merchantid, count(*) AS total FROM trorder a JOIN msusers c ON c.id = a.userid WHERE DATE(a.createddate) = CURDATE() GROUP BY c.merchantid) c", 'c.merchantid = a.id', 'LEFT')
            ->join("(SELECT d.merchantid, count(*) AS total FROM trorder a JOIN msusers d ON d.id = a.userid GROUP BY d.merchantid) d", 'd.merchantid = a.id', 'LEFT')
            ->order_by('b.total', 'DESC');

        return $this;
    }
}
