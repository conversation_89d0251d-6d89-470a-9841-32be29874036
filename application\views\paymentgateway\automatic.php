<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Payment Gateway Otomatis</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Payment Gateway</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Otomatis</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <!--begin::Alert-->
            <div class="alert alert-success d-flex align-items-center p-5 mb-10">
                <!--begin::Svg Icon | path: icons/duotune/general/gen048.svg-->
                <span class="svg-icon svg-icon-2hx svg-icon-success me-4">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.3" d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor"></path>
                        <path d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z" fill="currentColor"></path>
                    </svg>
                </span>
                <!--end::Svg Icon-->

                <div class="d-flex flex-column">
                    <h4 class="mb-1 text-success">Data anda akan dienkripsi end-to-end</h4>
                    <span>Anda tidak perlu khawatir dengan keamanan data anda, Karena kami akan mengenkripsi data anda sebelum disimpan.</span>
                </div>
            </div>
            <!--end::Alert-->
        </div>

        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title">
                        <h3 class="fw-bold">Payment Gateway</h3>
                    </div>

                    <div class="align-self-center">
                        <?php if ($paymentgateway != null) : ?>
                            <button type="button" class="btn btn-primary btn-sm mb-1" onclick="feePayments()">
                                <span>Biaya Transaksi & Gambar Pembayaran</span>
                            </button>
                        <?php endif; ?>

                        <?php if ($paymentgateway != null && $paymentgateway->vendor == 'Midtrans') : ?>
                            <button type="button" class="btn btn-primary btn-sm mb-1" onclick="enabledPayments()">
                                <span>Channel Pembayaran</span>
                            </button>
                        <?php elseif ($paymentgateway != null && $paymentgateway->vendor == 'Tripay') : ?>
                            <button type="button" class="btn btn-primary btn-sm mb-1" onclick="channelPayments()">
                                <span>Channel Pembayaran</span>
                            </button>
                        <?php elseif ($paymentgateway != null && $paymentgateway->vendor == 'iPaymu') : ?>
                            <button type="button" class="btn btn-primary btn-sm mb-1" onclick="channelPaymentsIpaymu()">
                                <span>Channel Pembayaran</span>
                            </button>
                        <?php elseif ($paymentgateway != null && $paymentgateway->vendor == 'Duitku') : ?>
                            <button type="button" class="btn btn-primary btn-sm mb-1" onclick="channelPaymentsDuitku()">
                                <span>Channel Pembayaran</span>
                            </button>
                        <?php elseif ($paymentgateway != null && $paymentgateway->vendor == 'Okeconnect') : ?>
                            <button type="button" class="btn btn-primary btn-sm mb-1" onclick="channelPaymentsOkeconnect()">
                                <span>Channel Pembayaran</span>
                            </button>
                        <?php elseif ($paymentgateway != null && $paymentgateway->vendor == 'PayDisini') : ?>
                            <button type="button" class="btn btn-primary btn-sm mb-1" onclick="channelPaymentsPayDisini()">
                                <span>Channel Pembayaran</span>
                            </button>
                        <?php endif; ?>

                        <?php if ($paymentgateway != null) : ?>
                            <?php if ($paymentgateway->isdisabled != 1) : ?>
                                <button type="button" class="btn btn-danger btn-sm mb-1" onclick="turnOff('Payment Gateway')">
                                    <span>Turn Off Payment Gateway</span>
                                </button>
                            <?php else : ?>
                                <button type="button" class="btn btn-success btn-sm mb-1" onclick="turnOn('Payment Gateway')">
                                    <span>Turn On Payment Gateway</span>
                                </button>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <form id="frmPaymentGateway" action="<?= base_url(uri_string() . '/process/paymentgateway') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Vendor</label>
                            <select name="vendor" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                <option value="Midtrans" <?= $paymentgateway != null ? ($paymentgateway->vendor == 'Midtrans' ? 'selected' : null) : null ?>>Midtrans</option>
                                <option value="Tripay" <?= $paymentgateway != null ? ($paymentgateway->vendor == 'Tripay' ? 'selected' : null) : null ?>>Tripay</option>
                                <option value="iPaymu" <?= $paymentgateway != null ? ($paymentgateway->vendor == 'iPaymu' ? 'selected' : null) : null ?>>iPaymu</option>
                                <option value="Duitku" <?= $paymentgateway != null ? ($paymentgateway->vendor == 'Duitku' ? 'selected' : null) : null ?>>Duitku</option>
                                <option value="Okeconnect" <?= $paymentgateway != null ? ($paymentgateway->vendor == 'Okeconnect' ? 'selected' : null) : null ?>>Okeconnect</option>
                                <option value="PayDisini" <?= $paymentgateway != null ? ($paymentgateway->vendor == 'PayDisini' ? 'selected' : null) : null ?>>PayDisini</option>
                            </select>

                            <div class="mt-1 <?= $paymentgateway != null && $paymentgateway->vendor == 'Tripay' ? null : 'd-none' ?>" id="tripay_callback">
                                <small>*URL Callback: https://websitekamu.com/<b>callback/tripay</b></small>
                            </div>

                            <div class="mt-1 <?= $paymentgateway != null && $paymentgateway->vendor == 'iPaymu' ? null : 'd-none' ?>" id="ipaymu_callback">
                                <small>*URL Callback: https://websitekamu.com/<b>callback/ipaymu</b></small>
                            </div>

                            <div class="mt-1 <?= $paymentgateway != null && $paymentgateway->vendor == 'Duitku' ? null : 'd-none' ?>" id="duitku_callback">
                                <small>*URL Callback: https://websitekamu.com/<b>callback/duitku</b></small>
                            </div>

                            <div class="mt-1 <?= $paymentgateway != null && $paymentgateway->vendor == 'Okeconnect' ? null : 'd-none' ?>" id="okeconnect_callback">
                                <small>*URL Callback: https://websitekamu.com/<b>callback/okeconnect</b></small>
                            </div>

                            <div class="mt-1 <?= $paymentgateway != null && $paymentgateway->vendor == 'PayDisini' ? null : 'd-none' ?>" id="paydisini_callback">
                                <small>*URL Callback: https://websitekamu.com/<b>callback/paydisini</b></small>
                            </div>
                        </div>

                        <div class="mb-7 <?= $paymentgateway != null ? ($paymentgateway->vendor == 'Midtrans' ? null : 'd-none') : null ?>" id="serverkey_display">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Server Key</label>
                            <input type="text" name="serverkey" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Server Key" value="<?= $paymentgateway != null ? ($paymentgateway->vendor == 'Midtrans' ? json_decode(stringEncryption('decrypt', $paymentgateway->detail))->serverkey : null) : null ?>" <?= $paymentgateway != null ? ($paymentgateway->vendor == 'Midtrans' ? 'required' : null) : 'required' ?>>
                        </div>

                        <div class="mb-7 <?= $paymentgateway != null ? ($paymentgateway->vendor == 'Midtrans' ? null : 'd-none') : null ?>" id="clientkey_display">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Client Key</label>
                            <input type="text" name="clientkey" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Client Key" value="<?= $paymentgateway != null ? ($paymentgateway->vendor == 'Midtrans' ? json_decode(stringEncryption('decrypt', $paymentgateway->detail))->clientkey : null) : null ?>" <?= $paymentgateway != null ? ($paymentgateway->vendor == 'Midtrans' ? 'required' : null) : 'required' ?>>
                        </div>

                        <div class="mb-7 <?= $paymentgateway != null ? ($paymentgateway->vendor == 'Tripay' || $paymentgateway->vendor == 'Duitku' || $paymentgateway->vendor == 'Okeconnect' ? null : 'd-none') : 'd-none' ?>" id="merchantcode_display">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Kode Merchant</label>
                            <input type="text" name="merchantcode" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Kode Merchant" value="<?= $paymentgateway != null ? ($paymentgateway->vendor == 'Tripay' || $paymentgateway->vendor == 'Duitku' || $paymentgateway->vendor == 'Okeconnect' ? json_decode(stringEncryption('decrypt', $paymentgateway->detail))->merchantcode : null) : null ?>" <?= $paymentgateway != null ? ($paymentgateway->vendor == 'Tripay' || $paymentgateway->vendor == 'Duitku' || $paymentgateway->vendor == 'Okeconnect' ? 'required' : null) : null ?>>
                        </div>

                        <div class="mb-7 <?= $paymentgateway != null ? ($paymentgateway->vendor == 'iPaymu' ? null : 'd-none') : 'd-none' ?>" id="virtualaccount_display">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Virtual Account</label>
                            <input type="text" name="virtualaccount" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Virtual Account" value="<?= $paymentgateway != null ? ($paymentgateway->vendor == 'iPaymu' ? json_decode(stringEncryption('decrypt', $paymentgateway->detail))->virtualaccount : null) : null ?>" <?= $paymentgateway != null ? ($paymentgateway->vendor == 'iPaymu' ? 'required' : null) : null ?>>
                        </div>

                        <div class="mb-7 <?= $paymentgateway != null ? ($paymentgateway->vendor == 'Tripay' || $paymentgateway->vendor == 'iPaymu' || $paymentgateway->vendor == 'Duitku' || $paymentgateway->vendor == 'Okeconnect' || $paymentgateway->vendor == 'PayDisini' ? null : 'd-none') : 'd-none' ?>" id="apikey_display">
                            <label class="col-form-label fw-semibold fs-6 pt-0">API Key</label>
                            <input type="text" name="apikey" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan API Key" value="<?= $paymentgateway != null ? ($paymentgateway->vendor == 'Tripay' || $paymentgateway->vendor == 'iPaymu' || $paymentgateway->vendor == 'Duitku' || $paymentgateway->vendor == 'Okeconnect' || $paymentgateway->vendor == 'PayDisini' ? json_decode(stringEncryption('decrypt', $paymentgateway->detail))->apikey : null) : null ?>" <?= $paymentgateway != null ? ($paymentgateway->vendor == 'Tripay' || $paymentgateway->vendor == 'iPaymu' || $paymentgateway->vendor == 'Duitku' || $paymentgateway->vendor == 'Okeconnect' || $paymentgateway->vendor == 'PayDisini' ? 'required' : null) : null ?>>
                        </div>

                        <div class="mb-7 <?= $paymentgateway != null ? ($paymentgateway->vendor == 'Tripay' ? null : 'd-none') : 'd-none' ?>" id="privatekey_display">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Private Key</label>
                            <input type="text" name="privatekey" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Private Key" value="<?= $paymentgateway != null ? ($paymentgateway->vendor == 'Tripay' ? json_decode(stringEncryption('decrypt', $paymentgateway->detail))->privatekey : null) : null ?>" <?= $paymentgateway != null ? ($paymentgateway->vendor == 'Tripay' ? 'required' : null) : null ?>>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Minimal Deposit</label>
                                    <input type="number" name="minnominal" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Minimal Deposit" value="<?= $paymentgateway != null ? $paymentgateway->minnominal : null ?>">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Maksimal Deposit</label>
                                    <input type="number" name="maxnominal" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Minimal Deposit" value="<?= $paymentgateway != null ? $paymentgateway->maxnominal : null ?>">
                                </div>
                            </div>
                        </div>

                        <div class="form-check form-check-custom form-check-solid mb-7">
                            <input class="form-check-input" type="checkbox" name="isbonus" value="1" id="isbonus" <?= $paymentgateway != null ? ($paymentgateway->isbonus == 1 ? 'checked' : null) : null ?> />

                            <label class="form-check-label" for="isbonus">
                                Bonus Deposit
                            </label>
                        </div>

                        <div class="row <?= $paymentgateway != null ? ($paymentgateway->isbonus == 1 ? null : 'd-none') : 'd-none' ?>" id="bonus_deposit">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Tipe Bonus</label>
                                    <select id="bonustype" name="bonustype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                        <option value="Persentase" <?= $paymentgateway != null ? ($paymentgateway->bonustype == 'Persentase' ? 'selected' : null) : null ?>>Persentase</option>
                                        <option value="Nominal" <?= $paymentgateway != null ? ($paymentgateway->bonustype == 'Nominal' ? 'selected' : null) : null ?>>Nominal</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Jumlah Bonus</label>
                                    <input type="number" name="nominalbonus" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah Bonus" value="<?= $paymentgateway != null ? $paymentgateway->nominalbonus : null ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>

            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Notification Handler Services</h3>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-row-bordered gy-5 datatables-profit text-nowrap">
                            <thead>
                                <tr class="fw-semibold fs-6 text-muted">
                                    <th>Layanan Deposit</th>
                                    <th>Harga</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>

                            <tbody>
                                <?php foreach ($notificationhandler as $key => $value) : ?>
                                    <tr>
                                        <td><?= $value->packagename ?></td>
                                        <td>Rp <?= IDR($value->price) ?></td>
                                        <td>
                                            <?php if ($value->buyid == null) : ?>
                                                <button type="button" class="btn btn-primary btn-sm" onclick="buyNotificationHandler('<?= stringEncryption('encrypt', $value->id) ?>')">
                                                    <span>Buy</span>
                                                </button>
                                            <?php else : ?>
                                                <button type="button" class="btn btn-primary btn-sm" onclick="configNotificationHandler('<?= stringEncryption('encrypt', $value->buyid) ?>')">
                                                    <span>Konfigurasi</span>
                                                </button>
                                                <?php if ($value->isdisabled == 1) : ?>
                                                    <button type="button" class="btn btn-success btn-sm mb-1" onclick="enableNotificationHandler('<?= stringEncryption('encrypt', $value->buyid) ?>')">
                                                        Enable
                                                    </button>
                                                <?php else : ?>
                                                    <button type="button" class="btn btn-danger btn-sm mb-1" onclick="disableNotificationHandler('<?= stringEncryption('encrypt', $value->buyid) ?>')">
                                                        Disable
                                                    </button>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title">
                        <h3 class="fw-bold">Bank BCA</h3>
                    </div>

                    <div class="align-self-center">
                        <?php if ($bankbca != null) : ?>
                            <?php if ($bankbca->isdisabled != 1) : ?>
                                <?php if ($bankbca->bca_bot == 1) : ?>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="turnOffBot()">
                                        <span>Turn Off BCA BOT</span>
                                    </button>
                                <?php else : ?>
                                    <button type="button" class="btn btn-success btn-sm" onclick="turnOnBot()">
                                        <span>Turn On BCA BOT</span>
                                    </button>
                                <?php endif; ?>
                                <button type="button" class="btn btn-danger btn-sm" onclick="turnOff('Bank BCA')">
                                    <span>Turn Off Bank BCA</span>
                                </button>
                            <?php else : ?>
                                <button type="button" class="btn btn-success btn-sm" onclick="turnOn('Bank BCA')">
                                    <span>Turn On Bank BCA</span>
                                </button>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <form id="frmBCA" action="<?= base_url(uri_string() . '/process/bca') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <?php if ($bankbca != null && $bankbca->isdisabled != 1 && $bankbca->bca_bot != 1) : ?>
                            <!--begin::Alert-->
                            <div class="alert alert-info d-flex align-items-center p-5 mb-10">
                                <!--begin::Svg Icon | path: icons/duotune/general/gen048.svg-->
                                <span class="svg-icon svg-icon-info svg-icon-2hx me-4"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path opacity="0.3" d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor" />
                                        <path d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z" fill="currentColor" />
                                    </svg>
                                </span>
                                <!--end::Svg Icon-->

                                <div class="d-flex flex-column">
                                    <h4 class="mb-1 text-info">Pemberitahuan</h4>
                                    <span>Untuk mengamankan akun Internet Banking anda dari Terblokirnya akun akibat kesalahan Username & Password. Kami memberikan keamanan tambahan yaitu Bot BCA akan otomatis Offline apabila terjadi kegagalan Login Internet Banking lebih dari 2x. Maka dari itu anda diwajibkan menekan tombol <b>Turn On BCA BOT</b> untuk menjalankan Bot BCA agar bisa mengkonfirmasi deposit / transaksi yang dilakukan oleh member anda.</span>
                                </div>
                            </div>
                            <!--end::Alert-->
                        <?php endif; ?>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Nama Pemilik Rekening</label>
                            <input type="text" name="accountname" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Pemilik Rekening" value="<?= $bankbca != null ? (isset(json_decode(stringEncryption('decrypt', $bankbca->detail))->accountname) ? json_decode(stringEncryption('decrypt', $bankbca->detail))->accountname :  null) : null ?>" required>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Nomor Rekening</label>
                            <input type="number" name="accountnumber" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nomor Rekening" value="<?= $bankbca != null ? (isset(json_decode(stringEncryption('decrypt', $bankbca->detail))->accountnumber) ? json_decode(stringEncryption('decrypt', $bankbca->detail))->accountnumber :  null) : null ?>" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Username</label>
                                    <input type="text" name="username" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Username" value="<?= $bankbca != null ? (json_decode(stringEncryption('decrypt', $bankbca->detail))->username) : null ?>" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Password</label>
                                    <input type="password" name="password" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Password" value="<?= $bankbca != null ? (json_decode(stringEncryption('decrypt', $bankbca->detail))->password) : null ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Gambar Pembayaran</label>
                            <input type="file" accept=".jpg,.jpeg,.png" name="paymentimage" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" value="<?= $bankbca != null && $bankbca->paymentimage != null ? null  : 'required' ?>">
                            <small><?= $bankbca != null && $bankbca->paymentimage != null ? '*Kosongkan jika tidak ingin diubah' : null ?></small>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Minimal Deposit</label>
                                    <input type="number" name="minnominal" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Minimal Deposit" value="<?= $bankbca != null ? $bankbca->minnominal : null ?>">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Maksimal Deposit</label>
                                    <input type="number" name="maxnominal" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Minimal Deposit" value="<?= $bankbca != null ? $bankbca->maxnominal : null ?>">
                                </div>
                            </div>
                        </div>

                        <div class="form-check form-check-custom form-check-solid mb-7">
                            <input class="form-check-input" type="checkbox" name="isbonus" value="1" id="isbonus_bca" <?= $bankbca != null ? ($bankbca->isbonus == 1 ? 'checked' : null) : null ?> />

                            <label class="form-check-label" for="isbonus_bca">
                                Bonus Deposit
                            </label>
                        </div>

                        <div class="form-check form-check-custom form-check-solid mb-7">
                            <input class="form-check-input" type="checkbox" name="isfee" value="1" id="isfee_bca" <?= $bankbca != null ? ($bankbca->feetype != null ? 'checked' : null) : null ?> />

                            <label class="form-check-label" for="isfee_bca">
                                Biaya Transaksi
                            </label>
                        </div>

                        <div class="row <?= $bankbca != null ? ($bankbca->isbonus == 1 ? null : 'd-none') : 'd-none' ?>" id="bonus_deposit_bca">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Tipe Bonus</label>
                                    <select id="bonustype" name="bonustype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                        <option value="Persentase" <?= $bankbca != null ? ($bankbca->bonustype == 'Persentase' ? 'selected' : null) : null ?>>Persentase</option>
                                        <option value="Nominal" <?= $bankbca != null ? ($bankbca->bonustype == 'Nominal' ? 'selected' : null) : null ?>>Nominal</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Jumlah Bonus</label>
                                    <input type="number" name="nominalbonus" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah Bonus" value="<?= $bankbca != null ? $bankbca->nominalbonus : null ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row <?= $bankbca != null ? ($bankbca->feetype != null ? null : 'd-none') : 'd-none' ?>" id="biaya_fee_bca">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Tipe Biaya</label>
                                    <select id="feetype" name="feetype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                        <option value="Persentase" <?= $bankbca != null && $bankbca->feetype == 'Persentase' ? 'selected' : null ?>>Persentase</option>
                                        <option value="Nominal" <?= $bankbca != null && $bankbca->feetype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Jumlah Biaya</label>
                                    <input type="number" name="nominalfee" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah Biaya" value="<?= $bankbca != null && $bankbca->feetype != null ? $bankbca->nominalfee : null ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">E-Wallet Gopay</h3>
                    </div>

                    <div class="align-self-center">
                        <?php if ($gopay != null) : ?>
                            <?php if ($gopay->isdisabled != 1) : ?>
                                <button type="button" class="btn btn-danger btn-sm" onclick="turnOff('GOPAY')">
                                    <span>Turn Off E-Wallet Gopay</span>
                                </button>
                            <?php else : ?>
                                <button type="button" class="btn btn-success btn-sm" onclick="turnOn('GOPAY')">
                                    <span>Turn On E-Wallet Gopay</span>
                                </button>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <form id="frmGopay" action="<?= base_url(uri_string() . '/process/gopay') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Nomor Handphone</label>

                            <div class="input-group input-group-solid">
                                <input type="hidden" name="otptoken" id="gopay_otptoken">
                                <input type="number" name="phonenumber" id="gopay_phonenumber" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nomor Handphone" value="<?= $gopay != null ? json_decode(stringEncryption('decrypt', $gopay->detail))->phonenumber : null ?>" <?= $gopaypgstatus == true ? 'disabled' : null ?> required>


                                <span class="input-group-text">
                                    <?php if ($gopaypgstatus == false) : ?>
                                        <button type="button" class="btn btn-primary btn-sm" onclick="otpCodeGopay()">Kirim Kode OTP</button>
                                    <?php endif; ?>
                                </span>
                            </div>
                        </div>

                        <?php if ($gopaypgstatus == false) : ?>
                            <div class="mb-7">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Kode OTP</label>

                                <div class="input-group input-group-solid">
                                    <input type="number" name="otpcode" id="gopay_otpcode" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Kode OTP">

                                    <span class="input-group-text">
                                        <button type="button" class="btn btn-primary btn-sm" id="gopay_login" onclick="loginGopay()" disabled>Login</button>
                                    </span>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Gopay Status</label>

                            <div>
                                <span class="badge <?= $gopaypgstatus ? 'badge-success' : 'badge-danger' ?>"><?= $gopaypgstatus ? 'Connected: ' . $gopayname : 'Disconnected' ?></span>
                            </div>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Gambar Pembayaran</label>
                            <input type="file" accept=".jpg,.jpeg,.png" name="paymentimage" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" value="<?= $gopay != null && $gopay->paymentimage != null ? 'required' : null ?>">
                            <small><?= $gopay != null && $gopay->paymentimage != null ? '*Kosongkan jika tidak ingin diubah' : null ?></small>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Minimal Deposit</label>
                                    <input type="number" name="minnominal" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Minimal Deposit" value="<?= $gopay != null ? $gopay->minnominal : null ?>">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Maksimal Deposit</label>
                                    <input type="number" name="maxnominal" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Minimal Deposit" value="<?= $gopay != null ? $gopay->maxnominal : null ?>">
                                </div>
                            </div>
                        </div>

                        <div class="form-check form-check-custom form-check-solid mb-7">
                            <input class="form-check-input" type="checkbox" name="isbonus" value="1" id="isbonus_gopay" <?= $gopay != null ? ($gopay->isbonus == 1 ? 'checked' : null) : null ?> />

                            <label class="form-check-label" for="isbonus_gopay">
                                Bonus Deposit
                            </label>
                        </div>

                        <div class="form-check form-check-custom form-check-solid mb-7">
                            <input class="form-check-input" type="checkbox" name="isfee" value="1" id="isfee_gopay" <?= $gopay != null ? ($gopay->feetype != null ? 'checked' : null) : null ?> />

                            <label class="form-check-label" for="isfee_gopay">
                                Biaya Transaksi
                            </label>
                        </div>

                        <div class="row <?= $gopay != null ? ($gopay->isbonus == 1 ? null : 'd-none') : 'd-none' ?>" id="bonus_deposit_gopay">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Tipe Bonus</label>
                                    <select id="bonustype" name="bonustype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                        <option value="Persentase" <?= $gopay != null ? ($gopay->bonustype == 'Persentase' ? 'selected' : null) : null ?>>Persentase</option>
                                        <option value="Nominal" <?= $gopay != null ? ($gopay->bonustype == 'Nominal' ? 'selected' : null) : null ?>>Nominal</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Jumlah Bonus</label>
                                    <input type="number" name="nominalbonus" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah Bonus" value="<?= $gopay != null ? $gopay->nominalbonus : null ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row <?= $gopay != null ? ($gopay->feetype != null ? null : 'd-none') : 'd-none' ?>" id="biaya_fee_gopay">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Tipe Biaya</label>
                                    <select id="feetype" name="feetype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                        <option value="Persentase" <?= $gopay != null && $gopay->feetype == 'Persentase' ? 'selected' : null ?>>Persentase</option>
                                        <option value="Nominal" <?= $gopay != null && $gopay->feetype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Jumlah Biaya</label>
                                    <input type="number" name="nominalfee" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah Biaya" value="<?= $gopay != null && $gopay->feetype != null ? $gopay->nominalfee : null ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">E-Wallet OVO</h3>
                    </div>

                    <div class="align-self-center">
                        <?php if ($ovo != null) : ?>
                            <?php if ($ovo->isdisabled != 1) : ?>
                                <button type="button" class="btn btn-danger btn-sm" onclick="turnOff('OVO')">
                                    <span>Turn Off E-Wallet OVO</span>
                                </button>
                            <?php else : ?>
                                <button type="button" class="btn btn-success btn-sm" onclick="turnOn('OVO')">
                                    <span>Turn On E-Wallet OVO</span>
                                </button>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <form id="frmOVO" action="<?= base_url(uri_string() . '/process/ovo') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Nomor Handphone</label>

                            <div class="input-group input-group-solid">
                                <input type="hidden" name="otptoken" id="ovo_otptoken">
                                <input type="hidden" name="otprefid" id="ovo_otprefid">
                                <input type="text" name="phonenumber" id="ovo_phonenumber" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nomor Handphone" value="<?= $ovo != null ? json_decode(stringEncryption('decrypt', $ovo->detail))->phonenumber : null ?>" <?= $ovostatus == true ? 'disabled' : null ?> required>

                                <span class="input-group-text">
                                    <?php if ($ovostatus == false) : ?>
                                        <button type="button" class="btn btn-primary btn-sm" onclick="otpCodeOVO()">Kirim Kode OTP</button>
                                    <?php endif; ?>
                                </span>
                            </div>
                        </div>

                        <?php if ($ovostatus == false) : ?>
                            <div class="mb-7">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Kode OTP</label>

                                <div class="input-group input-group-solid">
                                    <input type="text" name="otpcode" id="ovo_otpcode" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Kode OTP">

                                    <span class="input-group-text">
                                        <button type="button" class="btn btn-primary btn-sm" id="ovo_login" onclick="loginOVO()" disabled>Login</button>
                                    </span>
                                </div>
                            </div>

                            <div class="mb-7" id="display_ovo_securitycode" style="display: none;">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Security Code (PIN)</label>

                                <div class="input-group input-group-solid">
                                    <input type="number" name="pin" id="ovo_securitycode" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Security Code (PIN)">

                                    <span class="input-group-text">
                                        <button type="button" class="btn btn-primary btn-sm" id="ovo_verify" onclick="verifyOVO()">Verify</button>
                                    </span>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">OVO Status</label>

                            <div>
                                <span class="badge <?= $ovostatus ? 'badge-success' : 'badge-danger' ?>"><?= $ovostatus ? 'Connected: ' . $ovoemail : 'Disconnected' ?></span>
                            </div>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Gambar Pembayaran</label>
                            <input type="file" accept=".jpg,.jpeg,.png" name="paymentimage" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" value="<?= $ovo != null && $ovo->paymentimage != null ? 'required' : null ?>">
                            <small><?= $ovo != null && $ovo->paymentimage != null ? '*Kosongkan jika tidak ingin diubah' : null ?></small>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Minimal Deposit</label>
                                    <input type="number" name="minnominal" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Minimal Deposit" value="<?= $ovo != null ? $ovo->minnominal : null ?>">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Maksimal Deposit</label>
                                    <input type="number" name="maxnominal" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Minimal Deposit" value="<?= $ovo != null ? $ovo->maxnominal : null ?>">
                                </div>
                            </div>
                        </div>

                        <div class="form-check form-check-custom form-check-solid mb-7">
                            <input class="form-check-input" type="checkbox" name="isbonus" value="1" id="isbonus_ovo" <?= $ovo != null ? ($ovo->isbonus == 1 ? 'checked' : null) : null ?> />

                            <label class="form-check-label" for="isbonus_ovo">
                                Bonus Deposit
                            </label>
                        </div>

                        <div class="form-check form-check-custom form-check-solid mb-7">
                            <input class="form-check-input" type="checkbox" name="isfee" value="1" id="isfee_ovo" <?= $ovo != null ? ($ovo->feetype != null ? 'checked' : null) : null ?> />

                            <label class="form-check-label" for="isfee_ovo">
                                Biaya Transaksi
                            </label>
                        </div>

                        <div class="row <?= $ovo != null ? ($ovo->isbonus == 1 ? null : 'd-none') : 'd-none' ?>" id="bonus_deposit_ovo">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Tipe Bonus</label>
                                    <select id="bonustype" name="bonustype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                        <option value="Persentase" <?= $ovo != null ? ($ovo->bonustype == 'Persentase' ? 'selected' : null) : null ?>>Persentase</option>
                                        <option value="Nominal" <?= $ovo != null ? ($ovo->bonustype == 'Nominal' ? 'selected' : null) : null ?>>Nominal</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Jumlah Bonus</label>
                                    <input type="number" name="nominalbonus" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah Bonus" value="<?= $ovo != null ? $ovo->nominalbonus : null ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row <?= $ovo != null ? ($ovo->feetype != null ? null : 'd-none') : 'd-none' ?>" id="biaya_fee_ovo">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Tipe Biaya</label>
                                    <select id="feetype" name="feetype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                        <option value="Persentase" <?= $ovo != null && $ovo->feetype == 'Persentase' ? 'selected' : null ?>>Persentase</option>
                                        <option value="Nominal" <?= $ovo != null && $ovo->feetype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Jumlah Biaya</label>
                                    <input type="number" name="nominalfee" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah Biaya" value="<?= $ovo != null && $ovo->feetype != null ? $ovo->nominalfee : null ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $.AjaxRequest('#frmPaymentGateway', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success',
                    }).then(function(result) {
                        return window.location.reload();

                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            }
        });

        $.AjaxRequest('#frmBCA', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success',
                    }).then(function(result) {
                        return window.location.reload();

                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            }
        });

        $.AjaxRequest('#frmGopay', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success',
                    }).then(function(result) {
                        return window.location.reload();

                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            }
        });

        $.AjaxRequest('#frmOVO', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success',
                    }).then(function(result) {
                        return window.location.reload();

                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            }
        });

        $('select[name=vendor]').change(function() {
            if ($(this).val() == 'Midtrans') {
                $('#merchantcode_display, #apikey_display, #privatekey_display, #virtualaccount_display').addClass('d-none').find('input').val(null).removeAttr('required');
                $('#serverkey_display, #clientkey_display').removeClass('d-none').find('input').val(null).attr('required', true);
                $('#tripay_callback, #ipaymu_callback, #duitku_callback, #okeconnect_callback,#paydisini_callback').addClass('d-none');
            } else if ($(this).val() == 'Tripay') {
                $('#serverkey_display, #clientkey_display,#virtualaccount_display').addClass('d-none').find('input').val(null).removeAttr('required');
                $('#merchantcode_display, #apikey_display, #privatekey_display').removeClass('d-none').find('input').val(null).attr('required', true);
                $('#tripay_callback').removeClass('d-none');
                $('#ipaymu_callback, #duitku_callback, #okeconnect_callback,#paydisini_callback').addClass('d-none');
            } else if ($(this).val() == 'iPaymu') {
                $('#serverkey_display, #clientkey_display, #merchantcode_display, #privatekey_display').addClass('d-none').find('input').val(null).removeAttr('required');
                $('#virtualaccount_display, #apikey_display').removeClass('d-none').find('input').val(null).attr('required', true);
                $('#tripay_callback, #duitku_callback, #okeconnect_callback,#paydisini_callback').addClass('d-none');
                $('#ipaymu_callback').removeClass('d-none');
            } else if ($(this).val() == 'Duitku') {
                $('#serverkey_display, #clientkey_display, #virtualaccount_display, #privatekey_display').addClass('d-none').find('input').val(null).removeAttr('required');
                $('#merchantcode_display, #apikey_display').removeClass('d-none').find('input').val(null).attr('required', true);
                $('#tripay_callback, #ipaymu_callback, #okeconnect_callback,#paydisini_callback').addClass('d-none');
                $('#duitku_callback').removeClass('d-none');
            } else if ($(this).val() == 'Okeconnect') {
                $('#serverkey_display, #clientkey_display, #virtualaccount_display, #privatekey_display').addClass('d-none').find('input').val(null).removeAttr('required');
                $('#merchantcode_display, #apikey_display').removeClass('d-none').find('input').val(null).attr('required', true);
                $('#tripay_callback, #ipaymu_callback, #duitku_callback,#paydisini_callback').addClass('d-none');
                $('#okeconnect_callback').removeClass('d-none');
            } else if ($(this).val() == 'PayDisini') {
                $('#merchantcode_display,#serverkey_display, #clientkey_display, #virtualaccount_display, #privatekey_display').addClass('d-none').find('input').val(null).removeAttr('required');
                $('#apikey_display').removeClass('d-none').find('input').val(null).attr('required', true);
                $('#tripay_callback, #ipaymu_callback, #duitku_callback,#okeconnect_callback').addClass('d-none');
                $('#paydisini_callback').removeClass('d-none');
            }
        });

        $('#isbonus').change(function() {
            if ($(this).prop('checked')) {
                $('#bonus_deposit').removeClass('d-none').find('input').attr('required', true).find('select').attr('required', true);
            } else {
                $('#bonus_deposit').addClass('d-none').find('input').removeAttr('required').find('select').removeAttr('required');
            }
        });

        $('#isbonus_bca').change(function() {
            if ($(this).prop('checked')) {
                $('#bonus_deposit_bca').removeClass('d-none').find('input').attr('required', true).find('select').attr('required', true);
            } else {
                $('#bonus_deposit_bca').addClass('d-none').find('input').removeAttr('required').find('select').removeAttr('required');
            }
        });

        $('#isbonus_gopay').change(function() {
            if ($(this).prop('checked')) {
                $('#bonus_deposit_gopay').removeClass('d-none').find('input').attr('required', true).find('select').attr('required', true);
            } else {
                $('#bonus_deposit_gopay').addClass('d-none').find('input').removeAttr('required').find('select').removeAttr('required');
            }
        });

        $('#isbonus_ovo').change(function() {
            if ($(this).prop('checked')) {
                $('#bonus_deposit_ovo').removeClass('d-none').find('input').attr('required', true).find('select').attr('required', true);
            } else {
                $('#bonus_deposit_ovo').addClass('d-none').find('input').removeAttr('required').find('select').removeAttr('required');
            }
        });

        $('#isfee_bca').change(function() {
            if ($(this).prop('checked')) {
                $('#biaya_fee_bca').removeClass('d-none').find('input').attr('required', true).find('select').attr('required', true);
            } else {
                $('#biaya_fee_bca').addClass('d-none').find('input').removeAttr('required').find('select').removeAttr('required');
            }
        });

        $('#isfee_gopay').change(function() {
            if ($(this).prop('checked')) {
                $('#biaya_fee_gopay').removeClass('d-none').find('input').attr('required', true).find('select').attr('required', true);
            } else {
                $('#biaya_fee_gopay').addClass('d-none').find('input').removeAttr('required').find('select').removeAttr('required');
            }
        });

        $('#isfee_ovo').change(function() {
            if ($(this).prop('checked')) {
                $('#biaya_fee_ovo').removeClass('d-none').find('input').attr('required', true).find('select').attr('required', true);
            } else {
                $('#biaya_fee_ovo').addClass('d-none').find('input').removeAttr('required').find('select').removeAttr('required');
            }
        });
    };

    function enabledPayments() {
        $.ajax({
            url: '<?= base_url(uri_string() . '/midtrans/enabledpayments') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        })
    }

    function channelPaymentsDuitku() {
        $.ajax({
            url: '<?= base_url(uri_string() . '/duitku/channelpayments') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        })
    }

    function channelPaymentsOkeconnect() {
        $.ajax({
            url: '<?= base_url(uri_string() . '/okeconnect/channelpayments') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        })
    }

    function channelPaymentsPayDisini() {
        $.ajax({
            url: '<?= base_url(uri_string() . '/paydisini/channelpayments') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        })
    }

    function channelPayments() {
        $.ajax({
            url: '<?= base_url(uri_string() . '/tripay/channelpayments') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        })
    }

    function channelPaymentsIpaymu() {
        $.ajax({
            url: '<?= base_url(uri_string() . '/ipaymu/channelpayments') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        })
    }

    function feePayments() {
        $.ajax({
            url: '<?= base_url(uri_string() . '/feepayments') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        });
    }

    function otpCodeOVO() {
        let phonenumber = $('#ovo_phonenumber').val();

        if (phonenumber.length == 0) {
            return Swal.fire({
                title: 'Gagal',
                text: 'Nomor handphone wajib diisi',
                icon: 'error',
            });
        } else {
            $.ajax({
                url: '<?= base_url(uri_string() . '/otp/ovo') ?>',
                method: 'POST',
                dataType: 'json',
                data: {
                    phonenumber: phonenumber
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        $('#ovo_otprefid').val(response.OTPREFID);
                        $('#ovo_login').removeAttr('disabled');

                        return Swal.fire({
                            title: 'Berhasil',
                            text: response.MESSAGE,
                            icon: 'success',
                        });
                    } else {
                        return Swal.fire({
                            title: 'Gagal',
                            text: response.MESSAGE,
                            icon: 'error',
                        });
                    }
                }
            }).fail(function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            });
        }
    }

    function otpCodeGopay() {
        let phonenumber = $('#gopay_phonenumber').val();

        if (phonenumber.length == 0) {
            return Swal.fire({
                title: 'Gagal',
                text: 'Nomor handphone wajib diisi',
                icon: 'error',
            });
        } else {
            $.ajax({
                url: '<?= base_url(uri_string() . '/otp/gopay') ?>',
                method: 'POST',
                dataType: 'json',
                data: {
                    phonenumber: phonenumber
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        $('#gopay_otptoken').val(response.OTPTOKEN);
                        $('#gopay_login').removeAttr('disabled');

                        return Swal.fire({
                            title: 'Berhasil',
                            text: response.MESSAGE,
                            icon: 'success',
                        });
                    } else {
                        return Swal.fire({
                            title: 'Gagal',
                            text: response.MESSAGE,
                            icon: 'error',
                        });
                    }
                }
            }).fail(function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            });
        }
    }

    function loginOVO() {
        let otpcode = $('#ovo_otpcode').val();
        let otprefid = $('#ovo_otprefid').val();

        if (otpcode.length == 0) {
            return Swal.fire({
                title: 'Gagal',
                text: 'Kode OTP wajib diisi',
                icon: 'error'
            });
        } else if (otprefid.length == 0) {
            return Swal.fire({
                title: 'Gagal',
                text: 'Invalid OTP Reference ID',
                icon: 'error'
            });
        } else {
            $.ajax({
                url: '<?= base_url(uri_string() . '/login/ovo') ?>',
                method: 'POST',
                dataType: 'json',
                data: {
                    otpcode: otpcode,
                    otprefid: otprefid
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        $('#display_ovo_securitycode').css('display', 'block');
                        $('#ovo_otptoken').val(response.OTPTOKEN);
                        $('#ovo_otprefid').val(response.OTPREFID);

                        return Swal.fire({
                            title: 'Berhasil',
                            text: response.MESSAGE,
                            icon: 'success',
                        });
                    } else {
                        return Swal.fire({
                            title: 'Gagal',
                            text: response.MESSAGE,
                            icon: 'error',
                        });
                    }
                }
            }).fail(function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            });
        }
    }

    function verifyOVO() {
        let phonenumber = $('#ovo_phonenumber').val();
        let otprefid = $('#ovo_otprefid').val();
        let otptoken = $('#ovo_otptoken').val();
        let securitycode = $('#ovo_securitycode').val();

        if (phonenumber.length == 0) {
            return Swal.fire({
                title: 'Gagal',
                text: 'Nomor handphone wajib diisi',
                icon: 'error'
            });
        } else if (otprefid.length == 0) {
            return Swal.fire({
                title: 'Gagal',
                text: 'Invalid OTP Reference ID',
                icon: 'error'
            });
        } else if (otptoken.length == 0) {
            return Swal.fire({
                title: 'Gagal',
                text: 'Invalid OTP Token',
                icon: 'error'
            });
        } else if (securitycode.length == 0) {
            return Swal.fire({
                title: 'Gagal',
                text: 'Security Code wajib diisi',
                icon: 'error'
            });
        } else {
            $.ajax({
                url: '<?= base_url(uri_string() . '/verify/ovo') ?>',
                method: 'POST',
                dataType: 'json',
                data: {
                    phonenumber: phonenumber,
                    otprefid: otprefid,
                    otptoken: otptoken,
                    securitycode: securitycode
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        return Swal.fire({
                            title: 'Berhasil',
                            text: response.MESSAGE,
                            icon: 'success',
                        }).then(function() {
                            return window.location.reload();
                        });
                    } else {
                        return Swal.fire({
                            title: 'Gagal',
                            text: response.MESSAGE,
                            icon: 'error',
                        });
                    }
                }
            }).fail(function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            });
        }
    }

    function loginGopay() {
        let otpcode = $('#gopay_otpcode').val();
        let otptoken = $('#gopay_otptoken').val();

        if (otpcode.length == 0) {
            return Swal.fire({
                title: 'Gagal',
                text: 'Kode OTP wajib diisi',
                icon: 'error'
            });
        } else if (otptoken.length == 0) {
            return Swal.fire({
                title: 'Gagal',
                text: 'Invalid OTP Token',
                icon: 'error'
            });
        } else {
            $.ajax({
                url: '<?= base_url(uri_string() . '/login/gopay') ?>',
                method: 'POST',
                dataType: 'json',
                data: {
                    otpcode: otpcode,
                    otptoken: otptoken
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        return Swal.fire({
                            title: 'Berhasil',
                            text: response.MESSAGE,
                            icon: 'success',
                        }).then((result) => {
                            return window.location.reload();
                        });
                    } else {
                        return Swal.fire({
                            title: 'Gagal',
                            text: response.MESSAGE,
                            icon: 'error',
                        });
                    }
                }
            }).fail(function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            });
        }
    }

    function turnOff(method) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Apakah anda yakin ingin menonaktifkan pembayaran ini?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/disable') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        method: method
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function turnOn(method) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Apakah anda yakin ingin mengaktifkan pembayaran ini?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/enable') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        method: method
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function turnOffBot() {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Apakah anda yakin ingin menonaktifkan BCA BOT?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/disablebcabot') ?>',
                    method: 'POST',
                    dataType: 'json',
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function turnOnBot(method) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Apakah anda yakin ingin mengaktifkan BCA BOT?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/enablebcabot') ?>',
                    method: 'POST',
                    dataType: 'json',
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function buyNotificationHandler(key) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/buy') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                key: key
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        })
    }

    function configNotificationHandler(key) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/configuration') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                key: key,
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        })
    }

    function enableNotificationHandler(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Apakah anda yakin ingin mengaktifkan pembayaran ini?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/notificationhandler/enable') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        key: id,
                        isdisabled: 0
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function disableNotificationHandler(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Apakah anda yakin ingin menonaktifkan pembayaran ini?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/notificationhandler/disable') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        key: id,
                        isdisabled: 1
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }
</script>