<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Upgrade Akun: <?= $license->name ?></h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <div class="modal-body">
            <table class="table table-striped table-row-bordered gy-5">
                <tr>
                    <th>Nama</th>
                    <td>Paket <?= $license->name ?></td>
                </tr>

                <tr>
                    <th>Untuk</th>
                    <td><?= $user->email ?></td>
                </tr>

                <tr>
                    <th>Saldo</th>
                    <td>Rp <?= IDR($user->balance) ?></td>
                </tr>

                <tr>
                    <th>Harga Paket</th>
                    <td>Rp <?= IDR($license->price) ?></td>
                </tr>
            </table>

            <?php if (getCurrentBalance() < $license->price) : ?>
                <!--begin::Alert-->
                <div class="alert alert-danger d-flex align-items-center p-5">
                    <!--begin::Icon-->
                    <span class="svg-icon svg-icon-2hx svg-icon-danger me-3">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="5" fill="currentColor" />
                            <rect x="7" y="15.3137" width="12" height="2" rx="1" transform="rotate(-45 7 15.3137)" fill="currentColor" />
                            <rect x="8.41422" y="7" width="12" height="2" rx="1" transform="rotate(45 8.41422 7)" fill="currentColor" />
                        </svg>
                    </span>
                    <!--end::Icon-->

                    <!--begin::Wrapper-->
                    <div class="d-flex flex-column">
                        <!--begin::Content-->
                        <span>Saldo anda tidak mencukupi untuk membeli paket ini, Silahkan <b><a href="<?= base_url('deposit/topup') ?>">Topup Saldo</a></b> anda terlebih dahulu.</span>
                        <!--end::Content-->
                    </div>
                    <!--end::Wrapper-->
                </div>
                <!--end::Alert-->
            <?php endif; ?>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
            <?php if (getCurrentBalance() >= $license->price) : ?>
                <button type="button" class="btn btn-primary" onclick="buy()">Beli</button>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
    function buy() {
        return Swal.fire({
            title: 'Apakah anda yakin?',
            text: 'Apakah anda yakin ingin membeli paket ini?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/buy') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        licenseid: <?= $license->id ?>,
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }
</script>