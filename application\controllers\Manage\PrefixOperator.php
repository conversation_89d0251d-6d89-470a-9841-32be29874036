<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsPrefixOperator $msprefixoperator
 * @property MsProduct $MsProduct
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver|CI_DB_query_builder $db
 */
class PrefixOperator extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsPrefixOperator', 'msprefixoperator');
        $this->load->model('MsProduct', 'msproduct');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Prefix Operator';
        $data['content'] = 'manage/prefixoperator/index';

        return $this->load->view('master', $data);
    }

    public function datatables_prefixoperator()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();
                $where = array(
                    'a.userid' => getCurrentIdUser(),
                );

                if (getCurrentUser()->multivendor != 1) {
                    $vendor_ppob = getCurrentVendor('PPOB', getCurrentIdUser());
                    $vendor_smm = getCurrentVendor('SMM', getCurrentIdUser());

                    if ($vendor_ppob != null && $vendor_smm == null) {
                        $where['a.vendor'] = $vendor_ppob;
                    } else if ($vendor_ppob == null && $vendor_smm != null) {
                        $where['a.vendor'] = $vendor_smm;
                    } else if ($vendor_ppob != null && $vendor_smm != null) {
                        $where["(a.vendor = '$vendor_ppob' OR a.vendor = '$vendor_smm') ="] = true;
                    }
                } else {
                    $where["a.vendorid !="] = null;
                    $where['a.vendorenabled'] = 1;
                }

                $datatables = $this->datatables->make('MsProduct', 'QueryDatatablesPrefixOperator', 'SearchDatatables');

                foreach (
                    $datatables->getData($where) as $key => $value
                ) {
                    $detail = array();
                    $detail[] = $value->brandname;
                    $detail[] = $value->prefix ?? '-';
                    $detail[] = "<a href=\"javascript:;\" class=\"btn btn-icon btn-primary btn-sm\" onclick=\"detailPrefix('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-eye\"></i>
                    </a>";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function detail_prefixoperator()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Access denied');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Access denied');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('ID tidak boleh kosong');
            }

            $id = stringEncryption('decrypt', $id);
            $where = array(
                'a.id' => $id,
                'a.userid' => getCurrentIdUser()
            );

            if (getCurrentUser()->multivendor != 1) {
                $vendor_ppob = getCurrentVendor('PPOB', getCurrentIdUser());
                $vendor_smm = getCurrentVendor('SMM', getCurrentIdUser());

                if ($vendor_ppob != null && $vendor_smm == null) {
                    $where['a.vendor'] = $vendor_ppob;
                } else if ($vendor_ppob == null && $vendor_smm != null) {
                    $where['a.vendor'] = $vendor_smm;
                } else if ($vendor_ppob != null && $vendor_smm != null) {
                    $where["(a.vendor = '$vendor_ppob' OR a.vendor = '$vendor_smm') ="] = true;
                }
                $where['a.vendorid'] = null;
            } else {
                $where["a.vendorid !="] = null;
                $where['a.vendorenabled'] = 1;
            }

            $get = $this->msproduct->select('a.*, b.prefix')
                ->join('msprefixoperator b', 'b.operatorname = a.brand AND b.userid = a.userid', 'LEFT')
                ->get($where);

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/prefixoperator/detail', array(
                    'product' => $get->row(),
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_detail_prefixoperator()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $name = getPost('name');
            $prefix = getPost('prefix');

            if ($id == null) {
                throw new Exception('ID tidak boleh kosong');
            }

            $id = stringEncryption('decrypt', $id);
            $where = array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            );

            if (getCurrentUser()->multivendor != 1) {
                $vendor_ppob = getCurrentVendor('PPOB', getCurrentIdUser());
                $vendor_smm = getCurrentVendor('SMM', getCurrentIdUser());

                if ($vendor_ppob != null && $vendor_smm == null) {
                    $where['vendor'] = $vendor_ppob;
                } else if ($vendor_ppob == null && $vendor_smm != null) {
                    $where['vendor'] = $vendor_smm;
                } else if ($vendor_ppob != null && $vendor_smm != null) {
                    $where["(vendor = '$vendor_ppob' OR vendor = '$vendor_smm') ="] = true;
                }
                $where['vendorid'] = null;
            } else {
                $where["vendorid !="] = null;
                $where['vendorenabled'] = 1;
            }

            // Get product data
            $get = $this->msproduct->get($where);

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            // Check if prefix already exists for this brand
            $check_prefix = $this->msprefixoperator->get(array(
                'operatorname' => $name,
                'userid' => getCurrentIdUser()
            ));

            if ($check_prefix->num_rows() > 0) {
                $update = array(
                    'prefix' => $prefix,
                    'updateddate' => getCurrentDate(),
                    'updatedby' => getCurrentIdUser()
                );

                $where = array(
                    'operatorname' => $name,
                    'userid' => getCurrentIdUser()
                );

                $this->msprefixoperator->update($where, $update);
            } else {
                $insert = array(
                    'operatorname' => $name,
                    'prefix' => $prefix,
                    'userid' => getCurrentIdUser(),
                    'createddate' => getCurrentDate(),
                    'createdby' => getCurrentIdUser(),
                );

                $this->msprefixoperator->insert($insert);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menyimpan data');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
