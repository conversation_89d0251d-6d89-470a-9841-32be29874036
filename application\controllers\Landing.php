<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsUsers $msusers
 * @property MsProduct $msproduct
 * @property TrOrder $trorder
 */
class Landing extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        if (readConfig('./ai9iSFg3aEdUMmFxb1hZOGJVK2pvZz09.cfg', 'maintenance') == 'true') {
            if (uri_string() != 'maintenance') {
                return redirect(base_url('maintenance'));
            }
        }

        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('TrOrder', 'trorder');
    }

    public function index()
    {
        $data = array();
        $data['title'] = 'Server PPOB & SMM - Buat Bisnis PPOB & SMM Secara Instan';
        $data['users'] = $this->msusers->total(array(
            'merchantid' => null,
            'role' => 'User',
        ));
        $data['products'] = $this->msproduct->total();
        $data['transactions'] = $this->trorder->total();
        $data['content'] = 'landing/index';

        return $this->load->view('landing/master', $data);
    }

    public function contact()
    {
        $data = array();
        $data['title'] = 'Server PPOB & SMM - Hubungi Kami';
        $data['content'] = 'landing/contact';

        return $this->load->view('landing/master', $data);
    }

    public function privacypolicy()
    {
        $privacypolicy = null;
        if (file_exists('./application/templates/privacypolicy_main.txt')) {
            $privacypolicy = file_get_contents('./application/templates/privacypolicy_main.txt');
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Kebijakan Privasi';
        $data['content'] = 'landing/privacypolicy';
        $data['privacypolicy'] = $privacypolicy;

        return $this->load->view('landing/master', $data);
    }

    public function termsofservice()
    {
        $termsofservice = null;
        if (file_exists('./application/templates/termsofservice_main.txt')) {
            $termsofservice = file_get_contents('./application/templates/termsofservice_main.txt');
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Syarat dan Ketentuan';
        $data['content'] = 'landing/termsofservice';
        $data['termsofservice'] = $termsofservice;

        return $this->load->view('landing/master', $data);
    }

    public function maintenance()
    {
        if (readConfig('./ai9iSFg3aEdUMmFxb1hZOGJVK2pvZz09.cfg', 'maintenance') != 'true') {
            return redirect(base_url());
        }

        return $this->load->view('maintenance');
    }
}
