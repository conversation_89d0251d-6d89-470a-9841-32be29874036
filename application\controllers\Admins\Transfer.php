<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsUsers $msusers
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver $db
 * @property HistoryTransferSaldo $historytransfersaldo
 * @property HistoryBalance $historybalance
 */
class Transfer extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsUsers', 'msusers');
        $this->load->model('HistoryTransferSaldo', 'historytransfersaldo');
        $this->load->model('HistoryBalance', 'historybalance');
    }

    public function saldo()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Transfer Saldo';
        $data['content'] = 'admin/transfer/saldo';

        return $this->load->view('master', $data);
    }

    public function datatables_transfer_saldo()
    {
        try {
            if (isLogin() && isAdmin()) {
                $data = array();

                $datatable = $this->datatables->make('HistoryTransferSaldo', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.createdby' => getCurrentIdUser()
                );

                foreach ($datatable->getData($where) as $key => $value) {
                    $detail = array();
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = $value->email;
                    $detail[] = $value->name;
                    $detail[] = IDR($value->nominal);

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_transfer_saldo()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda harus login terlebih dahulu');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $email = getPost('email');
            $nominal = getPost('nominal');

            if ($email == null) {
                throw new Exception('Email wajib diisi');
            } else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Email yang anda masukkan tidak valid');
            } else if ($nominal == null) {
                throw new Exception('Nominal wajib diisi');
            } else if (!is_numeric($nominal)) {
                throw new Exception('Nominal harus berupa angka');
            } else if ($nominal <= 0) {
                throw new Exception('Nominal harus lebih dari 0');
            }

            $get = $this->msusers->get(array(
                'email' => $email,
                'merchantid' => null,
                'isdeleted' => null
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Email yang anda masukkan tidak terdaftar');
            }

            $row = $get->row();

            $before = getCurrentBalance($row->id, true);

            $update = array();
            $update['balance'] = $before + $nominal;

            $this->msusers->update(array(
                'id' => $row->id
            ), $update);

            $insert = array();
            $insert['userid'] = $row->id;
            $insert['nominal'] = $nominal;

            $this->historytransfersaldo->insert($insert);

            $inserthistorybalance = array();
            $inserthistorybalance['userid'] = $row->id;
            $inserthistorybalance['type'] = 'IN';
            $inserthistorybalance['nominal'] = $nominal;
            $inserthistorybalance['currentbalance'] = $before;
            $inserthistorybalance['createdby'] = getCurrentIdUser();
            $inserthistorybalance['createddate'] = getCurrentDate();

            $this->historybalance->insert($inserthistorybalance);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal melakukan transfer saldo');
            }

            $this->db->trans_commit();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('admin/transfer/result', array(
                    'before' => $before,
                    'after' => $update['balance'],
                    'user' => $row
                ), true)
            ));
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
