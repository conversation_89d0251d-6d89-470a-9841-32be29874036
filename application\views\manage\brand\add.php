<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Tambah Brand Produk</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmAddBrand" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Nama Brand</label>
                    <input type="text" name="name" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Brand" required>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Kategori Produk</label>
                    <?php if ($currentcategory == null) : ?>
                        <select name="productcategory" class="form-select form-select-solid" required>
                            <option value="">- Pilih -</option>
                            <?php foreach ($category as $key => $value) : ?>
                                <option value="<?= $value->category ?>"><?= $value->category ?></option>
                            <?php endforeach; ?>
                        </select>
                    <?php else : ?>
                        <input type="text" name="productcategory" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Kategori" value="<?= $currentcategory ?>" readonly>
                    <?php endif; ?>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    $.AjaxRequest('#frmAddBrand', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success',
                }).then(function(result) {
                    return window.location.reload();
                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.MESSAGE,
                    icon: 'error',
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        }
    });
</script>