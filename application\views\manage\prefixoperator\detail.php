<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>

<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Detail Prefix Operator</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form action="<?= base_url(uri_string() . '/process') ?>" id="frmprocessprefix" method="POST" autocomplete="off">
            <input type="hidden" name="id" value="<?= stringEncryption('encrypt', $product->id) ?>">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Nama Operator</label>
                    <input type="text" name="name" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Operator" value="<?= $product->brand ?>" readonly>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Prefix</label>
                    <input type="text" name="prefix" id="kt_tagify_prefix" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Prefix (pisahkan dengan koma)" value="<?= $product->prefix ?? null ?>" required>
                    <div class="form-text">Masukkan prefix operator, pisahkan dengan koma untuk multiple prefix. Contoh: 0811, 0812, 0813</div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    // Initialize Tagify
    var input = document.querySelector('#kt_tagify_prefix');

    if (input) {
        var isInitializing = true; // Flag to prevent validation during initialization

        var tagify = new Tagify(input, {
            delimiters: ",| ", // Allow comma and space as delimiters
            pattern: /^[0-9]+$/, // Only allow numbers
            placeholder: "Masukkan prefix (contoh: 0811, 0812, 0813)",
            maxTags: 20, // Maximum 20 prefix
            dropdown: {
                enabled: 0, // Disable dropdown suggestions
            },
            validate: function(tagData) {
                // Skip validation during initialization
                if (isInitializing) {
                    return true;
                }

                // Validate that prefix is numeric and has appropriate length
                var value = tagData.value;
                if (!/^[0-9]+$/.test(value)) {
                    return false; // Only numbers allowed
                }
                if (value.length < 3 || value.length > 6) {
                    return false; // Prefix should be 3-6 digits
                }
                return true;
            },
            transformTag: function(tagData) {
                // Ensure prefix starts with 0 if it's a mobile number
                var value = tagData.value;
                if (value.length >= 3 && !value.startsWith('0')) {
                    tagData.value = '0' + value;
                }
            },
            callbacks: {

            }
        });

        // Set initialization flag to false after a short delay
        setTimeout(function() {
            isInitializing = false;
        }, 100);

        // Update hidden input value when tags change
        tagify.on('change', function(e) {
            var tags = [];
            if (tagify.value && Array.isArray(tagify.value)) {
                tags = tagify.value.map(function(tag) {
                    return typeof tag === 'object' ? tag.value : tag;
                });
            }
            input.value = tags.join(',');
        });

        // Ensure proper data format before form submission
        var form = document.querySelector('#frmprocessprefix');
        if (form) {
            form.addEventListener('submit', function(e) {
                var tags = [];
                if (tagify.value && Array.isArray(tagify.value)) {
                    tags = tagify.value.map(function(tag) {
                        return typeof tag === 'object' ? tag.value : tag;
                    });
                }
                input.value = tags.join(',');
            });
        }
    }

    $.AjaxRequest('#frmprocessprefix', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success',
                }).then(function(result) {
                    return window.location.reload();
                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.MESSAGE,
                    icon: 'error',
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        }
    });
</script>