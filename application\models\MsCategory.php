<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Ms<PERSON>ategory extends MY_Model
{
    protected $table = 'mscategory';

    public function getCategory($userid, $servicetype = null, $vendor = null)
    {
        $category_vendor = "";
        if ($servicetype != null && $vendor != null) {
            $category_vendor = "AND category_apikey = '$servicetype' AND vendor = '$vendor' AND vendorid IS NULL ";
        } else if ($servicetype != null) {
            $category_vendor = "AND category_apikey = '$servicetype' ";
        } else if ($vendor != null) {
            $category_vendor = "AND vendor = '$vendor' AND vendorid IS NULL ";
        }

        if ($vendor == null) {
            $category_vendor .= "AND vendorid IS NOT NULL";
        }

        $category_where = "";
        if ($servicetype != null) {
            $category_where = "AND servicetype = '$servicetype'";
        }

        return $this->db->query("SELECT * FROM ( SELECT category FROM msproduct WHERE userid = $userid $category_vendor AND (vendorenabled IS NULL OR vendorenabled = 1) GROUP BY category UNION SELECT NAME AS category FROM mscategory WHERE userid = $userid $category_where ) a WHERE a.category IS NOT NULL GROUP BY a.category ORDER BY a.category ASC");
    }
}
