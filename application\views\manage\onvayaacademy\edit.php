<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Edit Video Academy</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Management</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('manage/onvayaacademy') ?>" class="text-gray-600 text-hover-primary">Onvaya Academy</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Edit Video</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-header">
                    <h3 class="card-title">Form Edit Video Academy</h3>
                </div>

                <form id="frmEditAcademy" action="<?= base_url(uri_string() . '/process') ?>" method="POST" enctype="multipart/form-data" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-5">
                                    <label class="form-label required">Judul Video</label>
                                    <input type="text" name="title" class="form-control form-control-lg form-control-solid" placeholder="Masukkan judul video" value="<?= htmlspecialchars($academy->title) ?>" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-5">
                                    <label class="form-label required">Link YouTube</label>
                                    <input type="url" name="youtube_link" class="form-control form-control-lg form-control-solid" placeholder="https://www.youtube.com/watch?v=..." value="<?= htmlspecialchars($academy->youtube_link) ?>" required>
                                    <div class="form-text">Masukkan link YouTube yang valid</div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-5">
                            <label class="form-label required">Deskripsi</label>
                            <textarea name="description" class="form-control form-control-lg form-control-solid" rows="4" placeholder="Masukkan deskripsi video" required><?= htmlspecialchars($academy->description) ?></textarea>
                        </div>

                        <div class="form-group mb-5">
                            <label class="form-label">Thumbnail</label>
                            <input type="file" name="thumbnail" class="form-control form-control-lg form-control-solid" accept="image/*" onchange="previewThumbnail(this)">
                            <div class="form-text">Format: JPG, JPEG, PNG. Maksimal 2MB. Kosongkan jika tidak ingin mengubah thumbnail.</div>
                            
                            <div class="mt-3">
                                <label class="form-label">Thumbnail Saat Ini:</label>
                                <div>
                                    <img src="<?= base_url('uploads/' . $academy->thumbnail) ?>" class="img-fluid rounded" style="max-height: 200px;">
                                </div>
                            </div>
                            
                            <div class="mt-3" id="thumbnail-preview" style="display: none;">
                                <label class="form-label">Preview Thumbnail Baru:</label>
                                <div>
                                    <img id="preview-image" src="" class="img-fluid rounded" style="max-height: 200px;">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-end">
                            <a href="<?= base_url('manage/onvayaacademy') ?>" class="btn btn-light me-3">Kembali</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function previewThumbnail(input) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            
            reader.onload = function(e) {
                $('#preview-image').attr('src', e.target.result);
                $('#thumbnail-preview').show();
            }
            
            reader.readAsDataURL(input.files[0]);
        } else {
            $('#thumbnail-preview').hide();
        }
    }

    window.onload = function() {
        $.AjaxRequest('#frmEditAcademy', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function(result) {
                        return window.location.href = '<?= base_url('manage/onvayaacademy') ?>';
                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error'
                });
            }
        });
    };
</script>
