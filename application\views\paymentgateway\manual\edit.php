<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Ubah Metode Pembayaran</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmEditPayment" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <input type="hidden" name="id" value="<?= stringEncryption('encrypt', $paymentmethod->id) ?>">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Tipe Pembayaran</label>
                            <select id="paymenttype" name="paymenttype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                <option value="Normal" <?= $paymentmethod->image == null ? 'selected' : null ?>>Normal</option>
                                <option value="Scan" <?= $paymentmethod->image != null ? 'selected' : null ?>>Scan</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Nama Pembayaran</label>
                            <input type="text" name="paymentmethod" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Pembayaran" value="<?= $paymentmethod->paymentmethod ?>" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-7 <?= $paymentmethod->image == null ? 'd-none' : null ?>" id="display_image">
                            <div>
                                <label class="col-form-label fw-semibold fs-6 pt-0">Gambar Scan</label>
                            </div>

                            <!--begin::Image input-->
                            <div class="image-input image-input-empty image-input-outline" data-kt-image-input="true" style="background-image: url(<?= base_url() ?>assets/media/svg/avatars/blank.svg)">
                                <!--begin::Image preview wrapper-->
                                <div class="image-input-wrapper w-125px h-125px" style="background-image: url(<?= base_url('uploads/' . $paymentmethod->image) ?>)"></div>
                                <!--end::Image preview wrapper-->

                                <!--begin::Edit button-->
                                <label class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Change avatar">
                                    <i class="bi bi-pencil-fill fs-7"></i>

                                    <!--begin::Inputs-->
                                    <input type="file" name="image" accept=".png, .jpg, .jpeg" />
                                    <input type="hidden" name="avatar_remove" />
                                    <!--end::Inputs-->
                                </label>
                                <!--end::Edit button-->

                                <!--begin::Cancel button-->
                                <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Cancel avatar">
                                    <i class="bi bi-x fs-2"></i>
                                </span>
                                <!--end::Cancel button-->

                                <!--begin::Remove button-->
                                <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="remove" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Remove avatar">
                                    <i class="bi bi-x fs-2"></i>
                                </span>
                                <!--end::Remove button-->
                            </div>
                            <!--end::Image input-->
                        </div>
                    </div>
                    <div class="col-md-<?= $paymentmethod->image == null ? '12' : '6' ?>" id="display_paymentimage">
                        <div class="mb-7">
                            <div>
                                <label class="col-form-label fw-semibold fs-6 pt-0">Logo Pembayaran</label>
                            </div>

                            <!--begin::Image input-->
                            <div class="image-input image-input-empty image-input-outline" data-kt-image-input="true" style="background-image: url(<?= base_url() ?>assets/media/svg/avatars/blank.svg)">
                                <!--begin::Image preview wrapper-->
                                <div class="image-input-wrapper w-125px h-125px" style="background-image: url(<?= base_url('uploads/' . $paymentmethod->paymentimage) ?>)"></div>
                                <!--end::Image preview wrapper-->

                                <!--begin::Edit button-->
                                <label class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Change avatar">
                                    <i class="bi bi-pencil-fill fs-7"></i>

                                    <!--begin::Inputs-->
                                    <input type="file" name="paymentimage" accept=".png, .jpg, .jpeg" />
                                    <input type="hidden" name="avatar_remove" />
                                    <!--end::Inputs-->
                                </label>
                                <!--end::Edit button-->

                                <!--begin::Cancel button-->
                                <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Cancel avatar">
                                    <i class="bi bi-x fs-2"></i>
                                </span>
                                <!--end::Cancel button-->

                                <!--begin::Remove button-->
                                <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="remove" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Remove avatar">
                                    <i class="bi bi-x fs-2"></i>
                                </span>
                                <!--end::Remove button-->
                            </div>
                            <!--end::Image input-->
                        </div>
                    </div>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Atas Nama</label>
                    <input type="text" name="accountname" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Atas Nama" value="<?= $paymentmethod->accountname ?>" required>
                </div>

                <div class="mb-7 <?= $paymentmethod->image == null ? null : 'd-none' ?>" id="display_accountnumber">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Nomor Rekening</label>
                    <input type="number" name="accountnumber" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nomor Rekening" value="<?= $paymentmethod->accountnumber ?>" <?= $paymentmethod->image == null ? 'required' : null ?>>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Minimal Topup</label>
                            <input type="number" name="minnominal" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Minimal Topup" value="<?= $paymentmethod->minnominal ?>" min="9" required>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Maksimal Topup</label>
                            <input type="number" name="maxnominal" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Maksimal Topup" value="<?= $paymentmethod->maxnominal ?>" min="0" required>
                            <small>*Isi '0' jika tidak ada batas maksimal topup</small>
                        </div>
                    </div>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Deskripsi</label>
                    <textarea id="input_description" placeholder="Masukkan Deskripsi"><?= $paymentmethod->description ?></textarea>
                </div>

                <div class="form-check form-check-custom form-check-solid mb-3">
                    <input class="form-check-input" type="checkbox" name="isunique" value="1" id="isunique" <?= $paymentmethod->isunique == 1 ? 'checked' : null ?> />

                    <label class="form-check-label" for="isunique">
                        Nominal Unik
                    </label>
                </div>

                <div class="form-check form-check-custom form-check-solid mb-3 <?= $paymentmethod->isunique == 1 ? null : 'd-none' ?>">
                    <input class="form-check-input" type="checkbox" name="uniqueadmin" value="1" id="uniqueadmin" <?= $paymentmethod->uniqueadmin == 1 ? 'checked' : null ?> />

                    <label class="form-check-label" for="uniqueadmin">
                        Jadikan Nominal Unik sbg. Biaya Admin
                    </label>
                </div>

                <div class="form-check form-check-custom form-check-solid mb-3">
                    <input class="form-check-input" type="checkbox" name="isbonus" value="1" id="isbonus" <?= $paymentmethod->isbonus == 1 ? 'checked' : null ?> />

                    <label class="form-check-label" for="isbonus">
                        Bonus Deposit
                    </label>
                </div>

                <div class="form-check form-check-custom form-check-solid mb-7">
                    <input class="form-check-input" type="checkbox" name="isfee" value="1" id="isfee" <?= $paymentmethod->feetype != null ? 'checked' : null ?> />

                    <label class="form-check-label" for="isfee">
                        Biaya Transaksi
                    </label>
                </div>

                <div class="row <?= $paymentmethod->isbonus != 1 ? 'd-none' : null ?>" id="bonus_deposit">
                    <div class="col-md-6">
                        <div class="mb-7">
                            <label for="" class="col-form-label fw-semibold fs-6 pt-0">Tipe Bonus</label>
                            <select id="bonustype" name="bonustype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" <?= $paymentmethod->isbonus == 1 ? 'required' : null ?>>
                                <option value="Persentase" <?= $paymentmethod->bonustype == 'Persentase' ? 'selected' : null ?>>Persentase</option>
                                <option value="Nominal" <?= $paymentmethod->bonustype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-7">
                            <label for="" class="col-form-label fw-semibold fs-6 pt-0">Jumlah Bonus</label>
                            <input type="number" name="nominalbonus" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah Bonus" value="<?= $paymentmethod->nominalbonus ?>" <?= $paymentmethod->isbonus == 1 ? 'required' : null ?>>
                        </div>
                    </div>
                </div>

                <div class="row <?= $paymentmethod->feetype == null ? 'd-none' : null ?>" id="biaya_fee">
                    <div class="col-md-6">
                        <div class="mb-7">
                            <label for="" class="col-form-label fw-semibold fs-6 pt-0">Tipe Biaya</label>
                            <select id="feetype" name="feetype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                <option value="Persentase">Persentase</option>
                                <option value="Nominal">Nominal</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-7">
                            <label for="" class="col-form-label fw-semibold fs-6 pt-0">Jumlah Biaya</label>
                            <input type="number" name="nominalfee" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah Biaya">
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    KTImageInput.createInstances();

    var editor;

    ClassicEditor
        .create(document.querySelector('#input_description'))
        .then(response => {
            editor = response;
        })
        .catch(responseError => {

        });

    $('#frmEditPayment').submit(function(e) {
        e.preventDefault();

        let formData = new FormData(this);
        formData.append('description', editor.data.get());

        let elementsForm = $(this).find('button, textarea, input,select');
        elementsForm.attr('disabled');

        $.ajax({
            url: $(this).attr('action'),
            method: $(this).attr('method'),
            dataType: 'json',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                elementsForm.removeAttr('disabled');

                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function(result) {
                        return window.location.reload();

                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            elementsForm.removeAttr('disabled');

            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    });

    $('#paymenttype').change(function() {
        if ($(this).val() == 'Scan') {
            $('#display_image').removeClass('d-none');
            $('#display_accountnumber').addClass('d-none').find('input').removeAttr('required');
            $('#display_paymentimage').removeClass('col-md-12').addClass('col-md-6');
        } else {
            $('#display_image').addClass('d-none');
            $('#display_accountnumber').removeClass('d-none').find('input').attr('required', true);
            $('#display_paymentimage').removeClass('col-md-6').addClass('col-md-12');
        }
    });

    $('#isbonus').change(function() {
        if ($(this).prop('checked')) {
            $('#bonus_deposit').removeClass('d-none').find('input').attr('required', true).find('select').attr('required', true);
        } else {
            $('#bonus_deposit').addClass('d-none').find('input').removeAttr('required').find('select').removeAttr('required');
        }
    });

    $('#isfee').change(function() {
        if ($(this).prop('checked')) {
            $('#biaya_fee').removeClass('d-none').find('input').attr('required', true).find('select').attr('required', true);
        } else {
            $('#biaya_fee').addClass('d-none').find('input').removeAttr('required').find('select').removeAttr('required');
        }
    })

    $('#isunique').change(function() {
        if ($(this).prop('checked')) {
            $('#uniqueadmin').parent().removeClass('d-none');
        } else {
            $('#uniqueadmin').parent().addClass('d-none');
        }
    });
</script>