<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Tickets extends MY_Model
{
    protected $table = 'ticket';
    public $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.*, b.name AS creator_name')
            ->from($this->table . ' a')
            ->join('msusers b', 'b.id = a.userid')
            ->order_by('a.createddate', 'DESC');

        return $this;
    }
}
