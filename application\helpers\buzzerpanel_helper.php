<?php
defined('BASEPATH') or die('No direct script access allowed!');

class BuzzerPanel
{
    private $_api_key, $_secret_key;

    public function __construct($apikey, $secretkey)
    {
        $this->_api_key = $apikey;
        $this->_secret_key = $secretkey;
    }

    public function services()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://buzzerpanel.id/api/json.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_key=$this->_api_key&secret_key=$this->_secret_key&action=services");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function order($service_id, $target, $qty)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://buzzerpanel.id/api/json.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_key=$this->_api_key&secret_key=$this->_secret_key&action=order&service=$service_id&data=$target&quantity=$qty");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function status($id)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://buzzerpanel.id/api/json.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_key=$this->_api_key&secret_key=$this->_secret_key&action=status&id=$id");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function profile()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://buzzerpanel.id/api/json.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_key=$this->_api_key&secret_key=$this->_secret_key&action=profile");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }
}
