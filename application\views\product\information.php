<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Informasi Layanan</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Produk</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Informasi Layanan</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
    <a href="javascript:;" onclick="filter()" class="btn btn-dark fw-bold">Filter</a>
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">

                <div class="card-body">
                    <table class="table table-striped table-row-bordered gy-5 datatables-information">
                        <thead>
                            <tr class="fw-semibold fs-6 text-muted">
                                <th>Nama Produk</th>
                                <th>Informasi</th>
                                <th>Harga Sebelum</th>
                                <th>Harga Sesudah</th>
                                <th>Tanggal Update</th>
                            </tr>
                        </thead>

                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('.datatables-information').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
            }
        });

        $('#filter').click(function() {
            let kategori = $('#kategori').val();

            $('.datatables-information').DataTable().destroy();
            $('.datatables-information').DataTable({
                responsive: true,
                processing: true,
                serverSide: true,
                stateSave: true,
                ordering: false,
                ajax: {
                    url: '<?= base_url(uri_string() . '/datatables') ?>',
                    method: 'POST',
                    data: {
                        kategori: kategori,
                    }
                }
            });
        });
    };

    function filter() {
        $.ajax({
            url: '<?= base_url(uri_string() . '/filter') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    }
</script>