<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsSlider $slider
 * @property CI_Upload $upload
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver|CI_DB_query_builder $db
 */
class Slider extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsSlider', 'slider');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Slider';
        $data['content'] = 'manage/slider/index';
        $data['slider'] = $this->slider->result(array(
            'userid' => getCurrentIdUser()
        ));

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tambah Slider';
        $data['content'] = 'manage/slider/add';

        return $this->load->view('master', $data);
    }

    public function process_add_slider()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $picture = isset($_FILES['picture']) ? $_FILES['picture'] : null;
            $link = getPost('link', null);

            if ($picture == null) {
                throw new Exception('Gambar slider wajib diisi');
            }

            $config = array(
                'upload_path' => './uploads/',
                'allowed_types' => 'jpg|jpeg|png',
                'max_size' => 2048,
                'encrypt_name' => true,
            );

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('picture')) {
                throw new Exception($this->upload->display_errors('', ''));
            } else {
                $picture = $this->upload->data();

                $data = array(
                    'userid' => getCurrentIdUser(),
                    'picture' => $picture['file_name'],
                    'link' => $link,
                    'createddate' => getCurrentDate(),
                    'createdby' => getCurrentIdUser(),
                );

                $this->slider->insert($data);

                if ($this->db->trans_status() === FALSE) {
                    throw new Exception('Slider gagal ditambahkan!');
                }

                $this->db->trans_commit();

                return JSONResponseDefault('OK', 'Slider berhasil ditambahkan!');
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_slider()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();

                $datatables = $this->datatables->make('MsSlider', 'QueryDatatables', 'SearchDatatables');

                foreach ($datatables->getData(array('userid' => getCurrentIdUser())) as $key => $value) {
                    $detail = array();
                    $detail[] = "<img src=\"" . base_url('uploads/' . $value->picture) . "\" width=\"200\">";
                    $detail[] = $value->link ?? '-';
                    $detail[] = "<a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm\" onclick=\"deleteSlider('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-trash\"></i>
                    </a>";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Anda tidak memiliki akses!');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_slider()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = stringEncryption('decrypt', getPost('id'));

            $slider = $this->slider->get(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($slider->num_rows() == 0) {
                throw new Exception('Slider tidak ditemukan!');
            }

            $row = $slider->row();

            if (file_exists('./uploads/' . $row->picture)) {
                @unlink('./uploads/' . $row->picture);
            }

            $this->slider->delete(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Slider gagal dihapus!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Slider berhasil dihapus!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
