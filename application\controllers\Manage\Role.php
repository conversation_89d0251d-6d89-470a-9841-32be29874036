<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Datatables $datatables
 * @property MsRole $msrole
 * @property MsRoleDiscount $msrolediscount
 * @property MsRoleDiscountAdv $msrolediscountadv
 * @property CI_DB_mysqli_driver $db
 */
class Role extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsRole', 'msrole');
        $this->load->model('MsRoleDiscount', 'msrolediscount');
        $this->load->model('MsRoleDiscountAdv', 'msrolediscountadv');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Hak Akses';
        $data['content'] = 'manage/role/index';

        return $this->load->view('master', $data);
    }

    public function datatables_role()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();

                $datatables = $this->datatables->make('MsRole', 'QueryDatatables', 'SearchDatatables');

                foreach (
                    $datatables->getData(array(
                        'a.createdby' => getCurrentIdUser()
                    )) as $key => $value
                ) {
                    $actions = "<button type=\"button\" class=\"btn btn-primary btn-icon btn-sm mb-1 me-1\" onclick=\"editRole('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-edit\"></i>
                    </button>";

                    if ($value->isdefault != 1) {
                        $actions .= "<a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm mb-1 me-1\" onclick=\"deleteRole('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-trash\"></i>
                        </a>";
                    }

                    $detail = array();
                    $detail[] = $value->rolename;
                    $detail[] = $value->discounttype ?? 'Simple';

                    if ($value->discounttype == 'Simple' || $value->discounttype == null) {
                        $detail[] = "Rp " . IDR($value->trxdiscount);
                    } else {
                        $detail[] = "<a href=\"javascript:;\" onclick=\"modalAdvanced('" . stringEncryption('encrypt', $value->id) . "')\">
                            - Advanced Mode -
                        </a>";
                    }

                    $detail[] = $actions;

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Anda tidak memiliki akses!');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()  && !isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (!isAdmin() && getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/role/add', array(), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_add_role()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()  && !isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (!isAdmin() && getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $name = getPost('name');

            if ($name == null) {
                throw new Exception('Nama tidak boleh kosong');
            } else {
                $name = removeSymbol($name);
            }

            $insert = array();
            $insert['rolename'] = $name;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->msrole->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan hak akses');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menambahkan hak akses');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = stringEncryption('decrypt', getPost('id'));

            $get = $this->msrole->get(array(
                'a.id' => $id,
                'a.createdby' => getCurrentIdUser()
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan!');
            }

            $row = $get->row();

            $rolediscount = $this->msrolediscount->get(array(
                'userid' => getCurrentIdUser(),
                'roleid' => $id
            ));

            $rolediscountprabayar = $this->msrolediscountadv->select('a.*')
                ->join('msrole b', 'b.id = a.roleid')
                ->where('a.roleid', $id)
                ->where('a.createdby', getCurrentIdUser())
                ->where('a.servicetype', 'Prabayar')
                ->get();

            $rolediscountpascaprabayar = $this->msrolediscountadv->select('a.*')
                ->join('msrole b', 'b.id = a.roleid')
                ->where('a.roleid', $id)
                ->where('a.createdby', getCurrentIdUser())
                ->where('a.servicetype', 'Pascabayar')
                ->get();

            $rolediscountsmm = $this->msrolediscountadv->select('a.*')
                ->join('msrole b', 'b.id = a.roleid')
                ->where('a.roleid', $id)
                ->where('a.createdby', getCurrentIdUser())
                ->where('a.servicetype', 'SMM')
                ->get();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/role/edit', array(
                    'data' => $row,
                    'rolediscount' => $rolediscount->row(),
                    'rolediscountprabayar' => $rolediscountprabayar->result(),
                    'rolediscountpascaprabayar' => $rolediscountpascaprabayar->result(),
                    'rolediscountsmm' => $rolediscountsmm->result(),
                    'countrolediscountprabayar' => $rolediscountprabayar->num_rows(),
                    'countrolediscountpascaprabayar' => $rolediscountpascaprabayar->num_rows(),
                    'countrolediscountsmm' => $rolediscountsmm->num_rows(),
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_edit_role()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = stringEncryption('decrypt', getPost('id'));
            $name = getPost('name');
            $trxdiscount = getPost('trxdiscount', 0);
            $type = getPost('type');

            if ($name == null) {
                throw new Exception('Nama tidak boleh kosong');
            } else if (!in_array($type, array('Simple', 'Advanced'))) {
                throw new Exception('Tipe diskon tidak valid');
            } else {
                $name = removeSymbol($name);
            }

            $get = $this->msrole->total(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan!');
            }

            $this->msrole->update(array(
                'id' => $id
            ), array(
                'rolename' => $name,
                'discounttype' => $type,
                'updateddate' => getCurrentDate(),
                'updatedby' => getCurrentIdUser(),
            ));

            if ($type == 'Simple') {
                if ($trxdiscount < 0) {
                    throw new Exception('Diskon transaksi tidak boleh kurang dari 0');
                }

                $get = $this->msrole->total(array(
                    'id' => $id,
                    'a.createdby' => getCurrentIdUser()
                ));

                if ($get == 0) {
                    throw new Exception('Data tidak ditemukan!');
                }

                $rolediscount = $this->msrolediscount->total(array(
                    'userid' => getCurrentIdUser(),
                    'roleid' => $id
                ));

                if ($rolediscount == 0) {
                    $this->msrolediscount->insert(array(
                        'userid' => getCurrentIdUser(),
                        'roleid' => $id,
                        'trxdiscount' => $trxdiscount,
                        'createddate' => getCurrentDate(),
                        'createdby' => getCurrentIdUser()
                    ));
                } else {
                    $this->msrolediscount->update(array(
                        'userid' => getCurrentIdUser(),
                        'roleid' => $id
                    ), array(
                        'trxdiscount' => $trxdiscount,
                        'updateddate' => getCurrentDate(),
                        'updatedby' => getCurrentIdUser(),
                    ));
                }

                $this->msrolediscountadv->delete(array(
                    'roleid' => $id,
                ));
            } else if ($type == 'Advanced') {
                $tipe = getPost('tipe');
                $startrange = getPost('startrange');
                $endrange = getPost('endrange');
                $jumlah = getPost('jumlah');
                $servicetype = getPost('servicetype');

                $get = $this->msrole->total(array(
                    'a.id' => $id,
                    'a.createdby' => getCurrentIdUser()
                ));

                if ($get == 0) {
                    throw new Exception('Data tidak ditemukan!');
                }

                $rolediscountadv = $this->msrolediscountadv->total(array(
                    'roleid' => $id
                ));

                if ($rolediscountadv == 0) {
                    foreach ($startrange as $key => $value) {
                        if (empty($tipe[$key])) continue;

                        if ($tipe[$key] == 'Persentase' && $jumlah[$key] > 100) {
                            throw new Exception('Diskon persentase range ' . $value . ' - ' . $endrange[$key] . ' ' . $servicetype[$key] . ' tidak boleh lebih dari 100%');
                        } else if ($jumlah[$key] < 0) {
                            throw new Exception('Diskon tidak boleh kurang dari 0');
                        }

                        $this->msrolediscountadv->insert(array(
                            'roleid' => $id,
                            'startrange' => $value,
                            'endrange' => $endrange[$key],
                            'nominal' => $jumlah[$key],
                            'discounttype' => $tipe[$key],
                            'servicetype' => $servicetype[$key],
                            'createddate' => getCurrentDate(),
                            'createdby' => getCurrentIdUser(),
                        ));
                    }
                } else {
                    $idadvanced = getPost('idadvanced');

                    foreach ($startrange as $key => $value) {
                        if (empty($tipe[$key])) continue;

                        if ($tipe[$key] == 'Persentase' && $jumlah[$key] > 100) {
                            throw new Exception('Diskon persentase range ' . $value . ' - ' . $endrange[$key] . ' ' . $servicetype[$key] . ' tidak boleh lebih dari 100%');
                        } else if ($jumlah[$key] < 0) {
                            throw new Exception('Diskon tidak boleh kurang dari 0');
                        }

                        if (!isset($idadvanced[$key])) {
                            $this->msrolediscountadv->insert(array(
                                'roleid' => $id,
                                'startrange' => $value,
                                'endrange' => $endrange[$key],
                                'nominal' => $jumlah[$key],
                                'discounttype' => $tipe[$key],
                                'servicetype' => $servicetype[$key],
                                'createddate' => getCurrentDate(),
                                'createdby' => getCurrentIdUser(),
                            ));
                        } else {
                            $this->msrolediscountadv->update(array(
                                'id' => stringEncryption('decrypt', $idadvanced[$key])
                            ), array(
                                'startrange' => $value,
                                'endrange' => $endrange[$key],
                                'nominal' => $jumlah[$key],
                                'discounttype' => $tipe[$key],
                                'servicetype' => $servicetype[$key],
                                'updateddate' => getCurrentDate(),
                                'updatedby' => getCurrentIdUser(),
                            ));
                        }
                    }
                }

                $this->msrolediscount->delete(array(
                    'roleid' => $id,
                ));
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil mengubah data!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_role()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = stringEncryption('decrypt', getPost('id'));


            $role = $this->msrole->total(array(
                'id' => $id
            ));

            if ($role == 0) {
                throw new Exception('Data tidak ditemukan!');
            }

            $this->msrolediscount->delete(array(
                'roleid' => $id,
            ));

            $this->msrolediscountadv->delete(array(
                'roleid' => $id,
            ));

            $this->msrole->delete(array(
                'id' => $id,
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus data!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil dihapus!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_role_discountadv()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = stringEncryption('decrypt', getPost('id'));

            $rolediscountadv = $this->msrolediscountadv->total(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($rolediscountadv == 0) {
                throw new Exception('Data tidak ditemukan!');
            }

            $this->msrolediscountadv->delete(array(
                'id' => $id,
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus data!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil dihapus!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function modalAdvanced()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = stringEncryption('decrypt', getGet('id'));

            $getAdvanced = $this->msrolediscountadv->select('a.*')
                ->join('msrole b', 'b.id = a.roleid')
                ->where('a.roleid', $id)
                ->where('a.createdby', getCurrentIdUser())
                ->get()
                ->result();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view("manage/role/modalAdvanced", array(
                    'data' => $getAdvanced
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
