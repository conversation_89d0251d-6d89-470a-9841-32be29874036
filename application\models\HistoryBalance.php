<?php
defined('BASEPATH') or exit('No direct script access allowed');

class HistoryBalance extends MY_Model
{
    protected $table = 'historybalance';
    public $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.*, b.clientcode, c.code AS depositcode')
            ->from($this->table . ' a')
            ->join('trorder b', 'b.id = a.orderid', 'LEFT')
            ->join('deposit c', 'c.id = a.depositid', 'LEFT')
            ->order_by('a.createddate', 'DESC');

        return $this;
    }
}
