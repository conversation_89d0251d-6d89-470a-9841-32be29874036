<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Tambah Produk Prabayar</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Produk</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Prabayar</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Tambah Produk</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->


    <!--begin::Button-->
    <div>
        <a href="<?= base_url('product/prabayar') ?>" class="btn btn-danger fw-bold">Kembali</a>
    </div>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="card mb-5">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-7">
                        <label class="col-form-label fw-semibold fs-6 pt-0">Vendor</label>
                        <select class="form-select form-select-solid" name="vendor" id="vendor">
                            <option value="" selected>- Pilih -</option>
                            <?php foreach ($vendor as $key => $value): ?>
                                <option value="<?= $value->vendorid ?>"><?= $value->name ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="mb-7">
                        <label class="col-form-label fw-semibold fs-6 pt-0">Kategori</label>
                        <select class="form-select form-select-solid" name="category" id="category">
                            <option value="" selected>- Pilih -</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="mb-7">
                        <label class="col-form-label fw-semibold fs-6 pt-0">Brand</label>
                        <select class="form-select form-select-solid" name="brand" id="brand">
                            <option value="" selected>- Pilih -</option>
                        </select>
                    </div>
                </div>
            </div>

            <table class="table table-striped table-row-bordered gy-5 datatables-product text-nowrap">
                <thead>
                    <tr class="fw-semibold fs-6 text-muted">
                        <th></th>
                        <th>Kode Produk</th>
                        <th>Nama Produk</th>
                        <th>Kategori Produk</th>
                        <th>Sub Kategori Produk</th>
                        <th>Brand</th>
                    </tr>
                </thead>

                <tbody>
                </tbody>
            </table>

            <div class="mt-3">
                <button type="button" class="btn btn-dark w-100" onclick="doSaveSelectedItem()">Tambahkan Produk yang ditandai</button>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('.datatables-product').DataTable({
            responsive: true,
            ordering: false,
            lengthMenu: [
                [-1],
                ['All']
            ],
            pageLength: -1,
        });

        $('#vendor').change(function() {
            let vendorid = $(this).val();

            $('#category').empty();
            $('#category').append('<option value="" selected>- Pilih -</option>');

            if (vendorid == '') {
                $('#category').trigger('change');
                initializeDatatables();

                return;
            }

            $.ajax({
                url: '<?= base_url(uri_string() . '/select/category') ?>',
                method: 'POST',
                data: {
                    vendor: vendorid,
                },
                dataType: 'json',
                success: function(response) {
                    initializeDatatables();

                    if (response.RESULT == 'OK') {
                        $.each(response.DATA, function(index, value) {
                            $('#category').append('<option value="' + value + '">' + value + '</option>');
                        });

                        $('#category').trigger('change');
                    } else {
                        $('#category').trigger('change');

                        return Swal.fire({
                            icon: 'error',
                            title: 'Gagal',
                            text: response.MESSAGE
                        });
                    }
                }
            }).fail(function() {
                $('#category').trigger('change');

                return Swal.fire({
                    icon: 'error',
                    title: 'Gagal',
                    text: 'Server sedang sibuk, silahkan coba beberapa saat lagi'
                });
            });
        });

        $('#category').change(function() {
            let vendorid = $('#vendor').val();
            let category = $(this).val();

            $('#brand').empty();
            $('#brand').append('<option value="" selected>- Pilih -</option>');

            if (category == '') {
                $('#brand').trigger('change');
                initializeDatatables();

                return;
            }

            $.ajax({
                url: '<?= base_url(uri_string() . '/select/brand') ?>',
                method: 'POST',
                data: {
                    vendor: vendorid,
                    category: category,
                },
                dataType: 'json',
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        $.each(response.DATA, function(index, value) {
                            $('#brand').append('<option value="' + value + '">' + value + '</option>');
                        });

                        $('#brand').trigger('change');
                    } else {
                        $('#brand').trigger('change');

                        return Swal.fire({
                            icon: 'error',
                            title: 'Gagal',
                            text: response.MESSAGE
                        });
                    }
                }
            }).fail(function() {
                $('#brand').trigger('change');

                return Swal.fire({
                    icon: 'error',
                    title: 'Gagal',
                    text: 'Server sedang sibuk, silahkan coba beberapa saat lagi'
                });
            });
        });

        $('#brand').change(function() {
            initializeDatatables();
        });
    };

    function initializeDatatables() {
        $('.datatables-product').DataTable().destroy();

        if ($('#category').val() == '' || $('#brand').val() == '') {
            $('.datatables-product').DataTable({
                responsive: true,
                ordering: false,
                lengthMenu: [
                    [-1],
                    ['All']
                ],
                pageLength: -1,
            });

            return;
        } else {
            $.ajax({
                url: '<?= base_url(uri_string() . '/data') ?>',
                method: 'POST',
                data: {
                    vendor: $('#vendor').val(),
                    category: $('#category').val(),
                    brand: $('#brand').val(),
                },
                dataType: 'json',
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        $('.datatables-product tbody').empty();

                        $.each(response.DATA, function(index, value) {
                            let row = '<tr>';
                            row += '<td><div class="form-check form-check-custom form-check-solid ms-3"><input type="checkbox" name="product[]" value="' + value.code + '" class="form-check-input" checked></div></td>';
                            row += '<td>' + value.code + '</td>';
                            row += '<td>' + value.productname + '</td>';
                            row += '<td>' + value.category + '</td>';
                            row += '<td>' + value.type + '</td>';
                            row += '<td>' + value.brand + '</td>';
                            row += '</tr>';

                            $('.datatables-product tbody').append(row);
                        });

                        $('.datatables-product').DataTable({
                            responsive: true,
                            ordering: false,
                            lengthMenu: [
                                [-1],
                                ['All']
                            ],
                            pageLength: -1,
                        });
                    } else {
                        return Swal.fire({
                            icon: 'error',
                            title: 'Gagal',
                            text: response.MESSAGE
                        });
                    }
                }
            }).fail(function() {
                return Swal.fire({
                    icon: 'error',
                    title: 'Gagal',
                    text: 'Server sedang sibuk, silahkan coba beberapa saat lagi'
                });
            });
        }
    }

    function doSaveSelectedItem() {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Apakah anda yakin ingin menambahkan produk yang ditandai?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                let selected = $('.datatables-product tbody input[type="checkbox"]:checked');
                let ids = [];

                selected.each(function() {
                    ids.push($(this).val());
                });

                $.ajax({
                    url: '<?= base_url(uri_string() . '/process') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        ids: ids,
                        vendor: $('#vendor').val(),
                        category: $('#category').val()
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();
                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }
</script>