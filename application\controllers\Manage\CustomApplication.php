<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property CustomApp $customapp
 * @property QueueBuildApp $queuebuildapp
 * @property QueueUploadPlaystore $queueuploadplaystore
 * @property Datatables $datatables
 * @property QueueUploadPlaystore_Prerelease $queueuploadplaystore_prerelease
 * @property CI_DB_mysqli_driver|CI_DB_query_builder $db
 */
class CustomApplication extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('CustomApp', 'customapp');
        $this->load->model('QueueBuildApp', 'queuebuildapp');
        $this->load->model('QueueUploadPlaystore', 'queueuploadplaystore');
        $this->load->model('QueueUploadPlaystore_Prerelease', 'queueuploadplaystore_prerelease');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        if (getCurrentUser()->unlock_app != 1) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Custom Aplikasi';
        $data['content'] = 'manage/customapplication/index';
        $data['customapp'] = $this->customapp->get(array(
            'userid' => getCurrentIdUser()
        ))->row();
        $data['currentqueue'] = $this->queuebuildapp->get(array(
            'userid' => getCurrentIdUser(),
            'status' => 'Pending'
        ))->row();
        $data['currentqueueuploadplaystore'] = $this->queueuploadplaystore->get(array(
            'userid' => getCurrentIdUser(),
            'status' => 'Pending'
        ))->row();
        $data['historyqueue'] = $this->queuebuildapp->order_by('a.createddate', 'DESC')
            ->result(array(
                'userid' => getCurrentIdUser(),
            ));
        $data['historyqueueplaystore'] = $this->queueuploadplaystore->order_by('a.createddate', 'DESC')
            ->result(array(
                'userid' => getCurrentIdUser(),
            ));

        return $this->load->view('master', $data);
    }

    public function process_customapplication()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Silahkan login terlebih dahulu!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->unlock_app != 1) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $name = getPost('name');
            $packagename = getPost('packagename');
            $color = getPost('color');

            if ($name == null) {
                throw new Exception('Nama Aplikasi tidak boleh kosong!');
            } else if ($packagename == null) {
                throw new Exception('Package Name tidak boleh kosong!');
            } else if ($color == null) {
                throw new Exception('Color Picker tidak boleh kosong!');
            } else {
                if (strpos($packagename, ' ') !== false) {
                    throw new Exception('Package Name tidak boleh mengandung spasi!');
                }
            }

            $get_queue = $this->queuebuildapp->total(array(
                'userid' => getCurrentIdUser(),
                'status' => 'Pending'
            ));

            if ($get_queue > 0) {
                throw new Exception('Anda sudah memiliki antrian build aplikasi!');
            }

            $get = $this->customapp->total(array(
                'userid' => getCurrentIdUser()
            ));

            $execute = array();
            $execute['userid'] = getCurrentIdUser();
            $execute['appname'] = $name;
            $execute['packagename'] = $packagename;
            $execute['color'] = $color;

            if ($get == 0) {
                $execute['keystore_alias'] = strtolower($packagename);
                $execute['createddate'] = getCurrentDate();
                $execute['createdby'] = getCurrentIdUser();

                $this->customapp->insert($execute);
            } else {
                $execute['updateddate'] = getCurrentDate();
                $execute['updatedby'] = getCurrentIdUser();

                $this->customapp->update(array(
                    'userid' => getCurrentIdUser()
                ), $execute);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan data!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menyimpan data!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function build()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Silahkan login terlebih dahulu!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            if (getCurrentUser()->unlock_app != 1) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $applicationtype = getPost('applicationtype');

            if (!in_array($applicationtype, array('apk', 'aab'))) {
                throw new Exception('Tipe aplikasi tidak valid!');
            }

            $get = $this->queuebuildapp->total(array(
                'userid' => getCurrentIdUser(),
                'status' => 'Pending'
            ));

            if ($get > 0) {
                throw new Exception('Anda sudah memiliki antrian build aplikasi!');
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['status'] = 'Pending';
            $insert['applicationtype'] = $applicationtype;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->queuebuildapp->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan antrian build aplikasi!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menambahkan antrian build aplikasi!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function request()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Silahkan login terlebih dahulu!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->unlock_app != 1) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/customapplication/request', array(), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_requestuploadplaystore()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Silahkan login terlebih dahulu!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $getcustomapp = $this->customapp->get(array(
                'userid' => getCurrentIdUser()
            ));

            if ($getcustomapp->num_rows() == 0) {
                throw new Exception('Silahkan lakukan request aplikasi terlebih dahulu!');
            }

            if (getCurrentUser()->unlock_playstore != 1) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $get = $this->queuebuildapp->select('a.*')
                ->where(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.status' => 'Success',
                    'a.applicationtype' => 'aab'
                ))
                ->order_by('a.createddate', 'DESC')
                ->total();

            if ($get == 0) {
                throw new Exception('Silahkan lakukan build aplikasi terlebih dahulu!');
            }

            $get = $this->queueuploadplaystore->total(array(
                'userid' => getCurrentIdUser(),
                'status' => 'Pending'
            ));

            if ($get > 0) {
                throw new Exception('Anda sudah memiliki antrian upload aplikasi ke playstore!');
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['status'] = 'Pending';
            $insert['appbundle_link'] = $getcustomapp->row()->appbundle_link ?? null;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();
            $insert['updateddate'] = null;
            $insert['updatedby'] = null;

            $this->queueuploadplaystore->insert($insert, false);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan antrian upload aplikasi ke playstore!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menambahkan antrian upload aplikasi ke playstore!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function modal_reason()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Silahkan login terlebih dahulu!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $get = $this->queueuploadplaystore->get(array(
                'id' => $id,
                'status' => 'Failed'
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view(
                    'manage/customapplication/modalreason',
                    array('reason' => $get->row()),
                    true
                )
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function prerelease()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Silahkan login terlebih dahulu!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $get = $this->queueuploadplaystore->get(array(
                'id' => $id,
                'status' => 'Pending',
                'userid' => getCurrentIdUser()
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/customapplication/prerelease', array(
                    'data' => $get->row()
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_prerelease()
    {
        try {
            $id = getPost('id');

            if (isLogin() && (isUser())) {
                $data = array();

                $datatables = $this->datatables->make('QueueUploadPlaystore_Prerelease', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.queueuploadplaystoreid' => $id,
                    'b.userid' => getCurrentIdUser()
                );

                foreach ($datatables->getData($where) as $key => $value) {
                    $detail = array();
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = $value->email;
                    $detail[] = "<a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"deleteEmailPrerelease($value->id)\">
                        <i class=\"fa fa-trash\"></i>
                    </a>";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Anda tidak memiliki akses!');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_prerelease()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Silahkan login terlebih dahulu!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = getPost('id');
            $email = getPost('email');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            } else if ($email == null) {
                throw new Exception('Email tidak boleh kosong');
            } else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Email tidak valid');
            }

            $get = $this->queueuploadplaystore->total(array(
                'id' => $id,
                'status' => 'Pending',
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $validate_email = $this->queueuploadplaystore_prerelease->total(array(
                'queueuploadplaystoreid' => $id,
                'email' => $email
            ));

            if ($validate_email > 0) {
                throw new Exception('Email sudah terdaftar');
            }

            $insert = array();
            $insert['queueuploadplaystoreid'] = $id;
            $insert['email'] = $email;
            $insert['createddate'] = getCurrentDate();

            $this->queueuploadplaystore_prerelease->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan email');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menambahkan email');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_prerelease()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Silahkan login terlebih dahulu!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = getPost('id');
            $queueuploadplaystoreid = getPost('queueuploadplaystoreid');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            } else if ($queueuploadplaystoreid == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $get = $this->queueuploadplaystore->total(array(
                'id' => $queueuploadplaystoreid,
                'status' => 'Pending',
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $get = $this->queueuploadplaystore_prerelease->total(array(
                'id' => $id,
                'queueuploadplaystoreid' => $queueuploadplaystoreid
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $this->queueuploadplaystore_prerelease->delete(array(
                'id' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus email');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menghapus email');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
