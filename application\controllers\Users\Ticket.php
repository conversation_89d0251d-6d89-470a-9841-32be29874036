<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Ticket extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Tickets', 'tickets');
        $this->load->model('TicketsDetail', 'ticketsdetail');
    }

    public function history()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tiket';
        $data['content'] = 'user/ticket/index';

        return $this->load->view('master', $data);
    }

    public function datatables_history()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();

                $datatables = $this->datatables->make('Tickets', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'b.merchantid' => getCurrentIdUser()
                );

                foreach ($datatables->getData($where) as $key => $value) {
                    if ($value->status == 'Pending') {
                        $status = "<span class=\"badge badge-warning\">Pending</span>";
                    } else if ($value->status == 'Done') {
                        $status = "<span class=\"badge badge-success\">Selesai</span>";
                    } else {
                        $status = "<span class=\"badge badge-info\">$value->status</span>";
                    }

                    $conversation = "<a href=\"" . base_url("users/ticket/history/conversation/" . stringEncryption('encrypt', $value->id)) . "\" class=\"btn btn-primary btn-sm mb-1\">
                        <i class=\"fa fa-comments\"></i>
                        Percakapan
                    </a>";

                    $close = "";
                    if ($value->status != 'Done') {
                        $close = "<a href=\"javascript:;\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"closeTicket('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-close\"></i>
                            Tutup
                        </a>";
                    }

                    $detail = array();
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = $value->code;
                    $detail[] = $value->creator_name;
                    $detail[] = $value->title;
                    $detail[] = $status;
                    $detail[] = "$conversation $close";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_close_ticket()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Tiket tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->tickets->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->total(array(
                    'a.id' => $id,
                    'b.merchantid' => getCurrentIdUser(),
                    'a.status !=' => 'Done'
                ));

            if ($get == 0) {
                throw new Exception('Tiket tidak ditemukan atau sudah ditutup');
            }

            $update = array();
            $update['status'] = 'Done';

            $this->tickets->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menutup tiket');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Tiket berhasil ditutup');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function conversation($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $id = stringEncryption('decrypt', $id);

        $get = $this->tickets->select('a.*, b.name AS creator_name')
            ->join('msusers b', 'b.id = a.userid')
            ->get(array(
                'a.id' => $id,
                'b.merchantid' => getCurrentIdUser(),
            ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('users/ticket/history'));
        }

        $row = $get->row();

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Percakapan: ' . $row->title;
        $data['content'] = 'user/ticket/conversation';
        $data['ticket'] = $row;
        $data['conversation'] = $this->ticketsdetail->select('a.*, b.name AS sender_name')
            ->join('msusers b', 'b.id = a.userid')
            ->order_by('a.createddate', 'ASC')
            ->result(array(
                'ticketid' => $row->id
            ));

        return $this->load->view('master', $data);
    }

    public function process_send_conversation($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->tickets->select('a.*, b.name AS creator_name')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.id' => $id,
                    'b.merchantid' => getCurrentIdUser()
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Percakapan tidak ditemukan');
            }

            $row = $get->row();

            if ($row->status == 'Done') {
                throw new Exception('Tiket sudah ditutup');
            }

            $message = getPost('message');

            if ($message == null) {
                throw new Exception('Pesan wajib diisi');
            } else {
                $message = removeSymbol($message);
            }

            $insert = array();
            $insert['ticketid'] = $id;
            $insert['userid'] = getCurrentIdUser();
            $insert['message'] = $message;


            if (!empty($_FILES['screenshot']['name'])) {
                $config['upload_path'] = './live/demo/uploads/tickets/';
                $config['allowed_types'] = 'jpg|jpeg|png';
                $config['max_size'] = 2048;
                $config['encrypt_name'] = TRUE;

                $this->load->library('upload', $config);

                if ($this->upload->do_upload('screenshot')) {
                    $upload_data = $this->upload->data();
                    $insert['img_url'] = $upload_data['file_name'];
                }
            }

            $this->ticketsdetail->insert($insert);

            requestSocket('realtime/chat/store', array(
                'targetuserid' => stringEncryption('encrypt', $row->userid),
                'ticketid' => stringEncryption('encrypt', $id)
            ));

            $update = array();
            $update['status'] = 'Admin Membalas';

            $this->tickets->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengirimkan pesan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Pesan berhasil dikirim');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
