<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MY_Model extends CI_Model
{
    protected $table = '';
    protected $select = '*';

    public function __construct()
    {
        parent::__construct();
    }

    public function insert($data = array(), $updated = true)
    {
        if (!isset($data['createddate'])) {
            $data['createddate'] = getCurrentDate();
        }
        if (!isset($data['updateddate']) && $updated) {
            $data['updateddate'] = getCurrentDate();
        }

        if (isLogin()) {
            if (!isset($data['createdby'])) {
                $data['createdby'] = getCurrentIdUser();
            }

            if (!isset($data['updatedby']) && $updated) {
                $data['updatedby'] = getCurrentIdUser();
            }
        }

        return $this->db->insert($this->table, $data);
    }

    public function insert_batch($data = array())
    {
        return $this->db->insert_batch($this->table, $data);
    }

    public function update($where = array(), $data = array())
    {
        if (!isset($data['updateddate'])) {
            $data['updateddate'] = getCurrentDate();
        }

        if (isLogin()) {
            if (!isset($data['updatedby'])) {
                $data['updatedby'] = getCurrentIdUser();
            }
        }

        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        return $this->db->set($data)
            ->update($this->table);
    }

    public function update_batch($data = array(), $index = null)
    {
        return $this->db->update_batch($this->table, $data, $index);
    }

    public function get($where = array())
    {
        if ($this->select == '*')
            $this->db->select('a.*');

        $this->db->from($this->table . ' a');

        if (is_array($where) && count($where)) {
            $this->db->where($where);
        }

        return $this->db->get();
    }

    public function total($where = array())
    {
        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        return $this->db->from($this->table . ' a')->count_all_results();
    }

    public function delete($where = array())
    {
        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        return $this->db->delete($this->table);
    }

    public function order_by($orderby, $direction = '')
    {
        $this->db->order_by($orderby, $direction);

        return $this;
    }

    public function group_by($group)
    {
        $this->db->group_by($group);

        return $this;
    }

    public function limit($offset)
    {
        $this->db->limit($offset);

        return $this;
    }

    public function result($where = array())
    {
        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        return $this->db->get($this->table . ' a')->result();
    }

    public function select($column = '*', $escape = null)
    {
        $this->select = $column;
        $this->db->select($column, $escape);

        return $this;
    }

    public function join($table, $statement, $mode = null)
    {
        $this->db->join($table, $statement, $mode);

        return $this;
    }

    public function where($where, $val = null)
    {
        $this->db->where($where, $val);

        return $this;
    }

    public function or_where($where, $val = null)
    {
        $this->db->or_where($where, $val);

        return $this;
    }

    public function where_in($where, $val = array())
    {
        if (count($val) > 0) {
            $this->db->where_in($where, $val);
        }

        return $this;
    }

    public function where_not_in($where, $val = array())
    {
        if (count($val) > 0) {
            $this->db->where_not_in($where, $val);
        }

        return $this;
    }

    public function row($where = array())
    {
        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        return $this->db->get($this->table . ' a')->row();
    }

    public function sum($column, $where = array())
    {
        $this->db->select_sum($column, 'total')
            ->from($this->table . ' a');

        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        return $this->db->get()->row()->total;
    }

    public function group_start()
    {
        $this->db->group_start();

        return $this;
    }

    public function group_end()
    {
        $this->db->group_end();

        return $this;
    }

    public function like($column, $keyword = null)
    {
        $this->db->like($column, $keyword);

        return $this;
    }

    public function having($column, $keyword = null)
    {
        $this->db->having($column, $keyword);

        return $this;
    }
}
