<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-xl">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Ubah Hak Akses <?= $data->rolename ?></h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmChangeRole" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <input type="hidden" name="id" value="<?= stringEncryption('encrypt', $data->id) ?>">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Nama Hak Akses</label>
                    <input type="text" name="name" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Hak Akses" value="<?= $data->rolename ?>" required>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Tipe</label>
                    <select class="form-select form-select-solid" id="type" name="type">
                        <option value="" selected>- Pilih -</option>
                        <option value="Simple" <?= $data->discounttype == 'Simple' || $data->discounttype == null ? 'selected' : null ?>>Simple</option>
                        <option value="Advanced" <?= $data->discounttype == 'Advanced' ? 'selected' : null ?>>Advanced</option>
                    </select>
                </div>

                <div class="mb-7" id="divtrxdiscount" style="<?= $data->discounttype == 'Advanced' && $data->discounttype != null ? 'display:none' : null ?>">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Potongan Harga Produk (Nominal)</label>
                    <input type="number" id="trxdiscount" name="trxdiscount" min="0" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Potongan Harga Produk" value="<?= $rolediscount->trxdiscount ?? 0 ?>">
                </div>

                <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6" id="headertabs" style="<?= $data->discounttype == 'Simple' ||  $data->discounttype == null ? 'display:none' : null ?>">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#tabsprabayar">Prabayar</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#tabspascaprabayar">Pascabayar</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#tabssmm">SMM</a>
                    </li>
                </ul>

                <div class="tab-content" id="rowadvanced" style="<?= $data->discounttype == 'Simple' || $data->discounttype == null ? 'display:none' : null ?>">
                    <div class="tab-pane fade show active" id="tabsprabayar" role="tabpanel">
                        <?php if ($countrolediscountprabayar > 0) : ?>
                            <?php foreach ($rolediscountprabayar as $k => $val) : ?>
                                <div class="row mb-7">
                                    <div class="col-md-2">
                                        <input type="hidden" name="idadvanced[]" value="<?= stringEncryption('encrypt', $val->id) ?>">
                                        <input type="hidden" name="servicetype[]" value="Prabayar">

                                        <?php if ($k == 0) : ?>
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Range Harga</label>
                                        <?php endif; ?>

                                        <input type="number" id="startrange" min="0" name="startrange[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Range Harga Awal" value="<?= $val->startrange ?>">
                                    </div>

                                    <div class="col-md-2">
                                        <input type="number" id="endrange" min="0" name="endrange[]" class="form-control form-control-lg form-control-solid <?= $k == 0 ? 'mt-10' : 'mb-3' ?> mb-lg-0" placeholder="Masukkan Range Harga Akhir" value="<?= $val->endrange ?>">
                                    </div>

                                    <div class="col-md-4">
                                        <?php if ($k == 0) : ?>
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Tipe</label>
                                        <?php endif; ?>

                                        <select class="form-select form-select-solid" id="tipe" name="tipe[]">
                                            <option value="">- Pilih -</option>
                                            <option value="Persentase" <?= $val->discounttype == 'Persentase' ? 'selected' : null ?>>Persentase</option>
                                            <option value="Nominal" <?= $val->discounttype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                                        </select>
                                    </div>

                                    <div class="col-md-2">
                                        <?php if ($k == 0) : ?>
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Jumlah</label>
                                        <?php endif; ?>

                                        <input type="number" id="jumlah" min="0" name="jumlah[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah" value="<?= $val->nominal ?>">
                                    </div>

                                    <div class="col-md-2">
                                        <?php if ($k == 0) { ?>
                                            <button type="button" class="btn btn-danger btn-sm mt-10" onclick="deleteDiscountadv('<?= stringEncryption('encrypt', $val->id) ?>')">
                                                <i class="fa fa-trash pe-0"></i>
                                            </button>

                                            <button type="button" class="btn btn-primary btn-sm mt-10" onclick="addAdvanced(this,'Prabayar')">
                                                <i class="fa fa-plus pe-0"></i>
                                            </button>
                                        <?php } else { ?>
                                            <button type="button" class="btn btn-danger btn-sm mt-1" onclick="deleteDiscountadv('<?= stringEncryption('encrypt', $val->id) ?>')">
                                                <i class="fa fa-trash pe-0"></i>
                                            </button>
                                        <?php } ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else : ?>
                            <div class="row mb-7">
                                <div class="col-md-2">
                                    <input type="hidden" name="servicetype[]" value="Prabayar">

                                    <label class="col-form-label fw-semibold fs-6 pt-0">Range Harga</label>
                                    <input type="number" id="startrange" min="0" name="startrange[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Range Harga Awal">
                                </div>

                                <div class="col-md-2">
                                    <input type="number" id="endrange" min="0" name="endrange[]" class="form-control form-control-lg form-control-solid mt-10 mb-lg-0" placeholder="Masukkan Range Harga Akhir">
                                </div>

                                <div class="col-md-4">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Tipe</label>
                                    <select class="form-select form-select-solid" name="tipe[]" id="tipe">
                                        <option value="" selected>- Pilih -</option>
                                        <option value="Persentase">Persentase</option>
                                        <option value="Nominal">Nominal</option>
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Jumlah</label>
                                    <input type="number" id="jumlah" min="0" name="jumlah[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Range Harga Awal">
                                </div>

                                <div class="col-md-2">
                                    <button type="button" class="btn btn-primary btn-sm mt-10" onclick="addAdvanced(this,'Prabayar')">
                                        <i class="fa fa-plus pe-0"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="tab-pane fade" id="tabspascaprabayar" role="tabpanel">
                        <?php if ($countrolediscountpascaprabayar > 0) : ?>
                            <?php foreach ($rolediscountpascaprabayar as $k => $val) : ?>
                                <div class="row mb-7">
                                    <div class="col-md-2">
                                        <input type="hidden" name="idadvanced[]" value="<?= stringEncryption('encrypt', $val->id) ?>">
                                        <input type="hidden" name="servicetype[]" value="Pascabayar">

                                        <?php if ($k == 0) : ?>
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Range Harga</label>
                                        <?php endif; ?>

                                        <input type="number" id="startrange" min="0" name="startrange[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Range Harga Awal" value="<?= $val->startrange ?>">
                                    </div>

                                    <div class="col-md-2">
                                        <input type="number" id="endrange" min="0" name="endrange[]" class="form-control form-control-lg form-control-solid <?= $k == 0 ? 'mt-10' : 'mb-3' ?> mb-lg-0" placeholder="Masukkan Range Harga Akhir" value="<?= $val->endrange ?>">
                                    </div>

                                    <div class="col-md-4">
                                        <?php if ($k == 0) : ?>
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Tipe</label>
                                        <?php endif; ?>

                                        <select class="form-select form-select-solid" id="tipe" name="tipe[]">
                                            <option value="">- Pilih -</option>
                                            <option value="Persentase" <?= $val->discounttype == 'Persentase' ? 'selected' : null ?>>Persentase</option>
                                            <option value="Nominal" <?= $val->discounttype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                                        </select>
                                    </div>

                                    <div class="col-md-2">
                                        <?php if ($k == 0) : ?>
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Jumlah</label>
                                        <?php endif; ?>

                                        <input type="number" id="jumlah" min="0" name="jumlah[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah" value="<?= $val->nominal ?>">
                                    </div>

                                    <div class="col-md-2">
                                        <?php if ($k == 0) { ?>
                                            <button type="button" class="btn btn-danger btn-sm mt-10" onclick="deleteDiscountadv('<?= stringEncryption('encrypt', $val->id) ?>')">
                                                <i class="fa fa-trash pe-0"></i>
                                            </button>

                                            <button type="button" class="btn btn-primary btn-sm mt-10" onclick="addAdvanced(this,'Pascabayar')">
                                                <i class="fa fa-plus pe-0"></i>
                                            </button>
                                        <?php } else { ?>
                                            <button type="button" class="btn btn-danger btn-sm mt-1" onclick="deleteDiscountadv('<?= stringEncryption('encrypt', $val->id) ?>')">
                                                <i class="fa fa-trash pe-0"></i>
                                            </button>
                                        <?php } ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else : ?>
                            <div class="row mb-7">
                                <div class="col-md-2">
                                    <input type="hidden" name="servicetype[]" value="Pascabayar">

                                    <label class="col-form-label fw-semibold fs-6 pt-0">Range Harga</label>
                                    <input type="number" id="startrange" min="0" name="startrange[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Range Harga Awal">
                                </div>

                                <div class="col-md-2">
                                    <input type="number" id="endrange" min="0" name="endrange[]" class="form-control form-control-lg form-control-solid mt-10 mb-lg-0" placeholder="Masukkan Range Harga Akhir">
                                </div>

                                <div class="col-md-4">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Tipe</label>
                                    <select class="form-select form-select-solid" name="tipe[]" id="tipe">
                                        <option value="" selected>- Pilih -</option>
                                        <option value="Persentase">Persentase</option>
                                        <option value="Nominal">Nominal</option>
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Jumlah</label>
                                    <input type="number" id="jumlah" min="0" name="jumlah[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Range Harga Awal">
                                </div>

                                <div class="col-md-2">
                                    <button type="button" class="btn btn-primary btn-sm mt-10" onclick="addAdvanced(this,'Pascabayar')">
                                        <i class="fa fa-plus pe-0"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="tab-pane fade" id="tabssmm" role="tabpanel">
                        <?php if ($countrolediscountsmm > 0) : ?>
                            <?php foreach ($rolediscountsmm as $k => $val) : ?>
                                <div class="row mb-7">
                                    <div class="col-md-2">
                                        <input type="hidden" name="idadvanced[]" value="<?= stringEncryption('encrypt', $val->id) ?>">
                                        <input type="hidden" name="servicetype[]" value="SMM">

                                        <?php if ($k == 0) : ?>
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Range Harga</label>
                                        <?php endif; ?>

                                        <input type="number" id="startrange" min="0" name="startrange[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Range Harga Awal" value="<?= $val->startrange ?>">
                                    </div>

                                    <div class="col-md-2">
                                        <input type="number" id="endrange" min="0" name="endrange[]" class="form-control form-control-lg form-control-solid <?= $k == 0 ? 'mt-10' : 'mb-3' ?> mb-lg-0" placeholder="Masukkan Range Harga Akhir" value="<?= $val->endrange ?>">
                                    </div>

                                    <div class="col-md-4">
                                        <?php if ($k == 0) : ?>
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Tipe</label>
                                        <?php endif; ?>

                                        <select class="form-select form-select-solid" id="tipe" name="tipe[]">
                                            <option value="">- Pilih -</option>
                                            <option value="Persentase" <?= $val->discounttype == 'Persentase' ? 'selected' : null ?>>Persentase</option>
                                            <option value="Nominal" <?= $val->discounttype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                                        </select>
                                    </div>

                                    <div class="col-md-2">
                                        <?php if ($k == 0) : ?>
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Jumlah</label>
                                        <?php endif; ?>

                                        <input type="number" id="jumlah" min="0" name="jumlah[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah" value="<?= $val->nominal ?>">
                                    </div>

                                    <div class="col-md-2">
                                        <?php if ($k == 0) { ?>
                                            <button type="button" class="btn btn-danger btn-sm mt-10" onclick="deleteDiscountadv('<?= stringEncryption('encrypt', $val->id) ?>')">
                                                <i class="fa fa-trash pe-0"></i>
                                            </button>

                                            <button type="button" class="btn btn-primary btn-sm mt-10" onclick="addAdvanced(this,'SMM')">
                                                <i class="fa fa-plus pe-0"></i>
                                            </button>
                                        <?php } else { ?>
                                            <button type="button" class="btn btn-danger btn-sm mt-1" onclick="deleteDiscountadv('<?= stringEncryption('encrypt', $val->id) ?>')">
                                                <i class="fa fa-trash pe-0"></i>
                                            </button>
                                        <?php } ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else : ?>
                            <div class="row mb-7">
                                <div class="col-md-2">
                                    <input type="hidden" name="servicetype[]" value="SMM">

                                    <label class="col-form-label fw-semibold fs-6 pt-0">Range Harga</label>
                                    <input type="number" id="startrange" min="0" name="startrange[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Range Harga Awal">
                                </div>

                                <div class="col-md-2">
                                    <input type="number" id="endrange" min="0" name="endrange[]" class="form-control form-control-lg form-control-solid mt-10 mb-lg-0" placeholder="Masukkan Range Harga Akhir">
                                </div>

                                <div class="col-md-4">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Tipe</label>
                                    <select class="form-select form-select-solid" name="tipe[]" id="tipe">
                                        <option value="" selected>- Pilih -</option>
                                        <option value="Persentase">Persentase</option>
                                        <option value="Nominal">Nominal</option>
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Jumlah</label>
                                    <input type="number" id="jumlah" min="0" name="jumlah[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Range Harga Awal">
                                </div>

                                <div class="col-md-2">
                                    <button type="button" class="btn btn-primary btn-sm mt-10" onclick="addAdvanced(this,'SMM')">
                                        <i class="fa fa-plus pe-0"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    $.AjaxRequest('#frmChangeRole', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success'
                }).then(() => {
                    window.location.reload();
                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.MESSAGE,
                    icon: 'error'
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        }
    });

    $("#type").on('change', function() {
        $val = this.value;
        if ($val == "Simple") {
            $("#divtrxdiscount").show();
            $("#headertabs").hide();
            $("#rowadvanced").hide();
        } else {
            $("#divtrxdiscount").hide();
            $("#headertabs").show();
            $("#rowadvanced").show();
        }
    });

    function addAdvanced(these, servicetype) {
        $(these).parent().parent().parent().append(`<div class="row mb-7">
            <div class="col-md-2">
                <input type="hidden" name="servicetype[]" value="` + servicetype + `">
                <input type="number" name="startrange[]" min="0" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Range Harga Awal">
            </div>
            
            <div class="col-md-2">
                <input type="number" name="endrange[]" min="0" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Range Harga Akhir">
            </div>

            <div class="col-md-4">
                <select class="form-select form-select-solid" name="tipe[]">
                    <option value="" selected>- Pilih -</option>
                    <option value="Persentase">Persentase</option>
                    <option value="Nominal">Nominal</option>
                </select>
            </div>

            <div class="col-md-2">
                <input type="number" id="rangestart" min="0" name="jumlah[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah">
            </div>

            <div class="col-md-2">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeAdvanced(this)">
                    <i class="fa fa-minus pe-0"></i>
                </button>
            </div>
        </div>`);
    }

    function removeAdvanced(these) {
        $(these).parent().parent().remove();
    }

    function deleteDiscountadv(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Data yang dihapus tidak dapat dikembalikan',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url('manage/role/delete/discountadv') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }
</script>