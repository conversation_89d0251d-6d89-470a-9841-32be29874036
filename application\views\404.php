<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!-- 
   _____                              ____  ____  ____  ____     ___        _____ __  _____  ___
  / ___/___  ______   _____  _____   / __ \/ __ \/ __ \/ __ )   ( _ )      / ___//  |/  /  |/  /
  \__ \/ _ \/ ___/ | / / _ \/ ___/  / /_/ / /_/ / / / / __  |  / __ \/|    \__ \/ /|_/ / /|_/ / 
 ___/ /  __/ /   | |/ /  __/ /     / ____/ ____/ /_/ / /_/ /  / /_/  <    ___/ / /  / / /  / /  
/____/\___/_/    |___/\___/_/     /_/   /_/    \____/_____/   \____/\/   /____/_/  /_/_/  /_/   
                                                                                                
                        By PT. KARPEL DEVELOPER TEKNOLOGI INDONESIA
-->
<!DOCTYPE html>
<html lang="en">
<!--begin::Head-->

<head>
    <title>Server PPOB & SMM - Halaman Tidak Ditemukan</title>

    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="Server PPOB & SMM adalah sebuah platform digital yang menyediakan jasa penyedia layanan PPOB & SMM. Yang dimana setiap member yang terdaftar dapat melakukan konfigurasi nama usahanya sendiri.">
    <meta name="keywords" content="server ppob, server smm, server ppob & smm, server ppob h2h, server smm h2h, server ppob customize, server smm customize, ppob, smm, jasa pembuatan website ppob, pembuatan website ppob, website ppob, jasa pembuatan website smm, pembuatan website smm, website smm, sewa panel, sewa panel ppob, sewa panel smm, sewa ppob, sewa smm">
    <meta name="author" content="Server PPOB & SMM">
    <meta name="image" content="<?= base_url() ?>siteimage.png">

    <!-- Schema.org for Google -->
    <meta itemprop="name" content="Server PPOB & SMM - Buat Bisnis PPOB & SMM Secara Instan">
    <meta itemprop="description" content="Server PPOB & SMM adalah sebuah platform digital yang menyediakan jasa penyedia layanan PPOB & SMM. Yang dimana setiap member yang terdaftar dapat melakukan konfigurasi nama usahanya sendiri.">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Server PPOB & SMM - Buat Bisnis PPOB & SMM Secara Instan">
    <meta name="twitter:description" content="Server PPOB & SMM adalah sebuah platform digital yang menyediakan jasa penyedia layanan PPOB & SMM. Yang dimana setiap member yang terdaftar dapat melakukan konfigurasi nama usahanya sendiri.">

    <!-- Open Graph general (Facebook, Pinterest & Google+) -->
    <meta name="og:title" content="Server PPOB & SMM - Buat Bisnis PPOB & SMM Secara Instan">
    <meta name="og:description" content="Server PPOB & SMM adalah sebuah platform digital yang menyediakan jasa penyedia layanan PPOB & SMM. Yang dimana setiap member yang terdaftar dapat melakukan konfigurasi nama usahanya sendiri.">
    <meta name="og:url" content="https://server-ppobsmm.com/">
    <meta name="og:site_name" content="Server PPOB & SMM - Buat Bisnis PPOB & SMM Secara Instan">
    <meta name="og:type" content="website">

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "LocalBusiness",
            "name": "Server PPOB & SMM",
            "image": "https://server-ppobsmm.com/siteimage.png",
            "@id": "",
            "url": "https://server-ppobsmm.com/",
            "telephone": "085885263097",
            "priceRange": "150000",
            "address": {
                "@type": "PostalAddress",
                "streetAddress": "Jl. Karang Ampel Dusun Karang Widoro Kec. Dau",
                "addressLocality": "Malang",
                "postalCode": "65151",
                "addressCountry": "ID"
            },
            "openingHoursSpecification": {
                "@type": "OpeningHoursSpecification",
                "dayOfWeek": [
                    "Monday",
                    "Tuesday",
                    "Wednesday",
                    "Thursday",
                    "Friday",
                    "Saturday",
                    "Sunday"
                ],
                "opens": "00:00",
                "closes": "23:59"
            },
            "sameAs": [
                "https://www.facebook.com/profile.php?id=100087117707002",
                "https://www.instagram.com/server.ppobsmm/",
                "https://server-ppobsmm.com/"
            ]
        }
    </script>

    <link rel="shortcut icon" href="<?= base_url() ?>assets/media/logos/favicon.ico" />

    <!--begin::Fonts(mandatory for all pages)-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
    <!--end::Fonts-->

    <!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
    <link href="<?= base_url() ?>assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <link href="<?= base_url() ?>assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <!--end::Global Stylesheets Bundle-->

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-BZ3VXFC66C"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-BZ3VXFC66C');
    </script>
</head>
<!--end::Head-->
<!--begin::Body-->

<body id="kt_body" class="app-blank bgi-size-cover bgi-position-center bgi-no-repeat">
    <!--begin::Theme mode setup on page load-->
    <script>
        var defaultThemeMode = "light";
        var themeMode;
        if (document.documentElement) {
            if (document.documentElement.hasAttribute("data-theme-mode")) {
                themeMode = document.documentElement.getAttribute("data-theme-mode");
            } else {
                if (localStorage.getItem("data-theme") !== null) {
                    themeMode = localStorage.getItem("data-theme");
                } else {
                    themeMode = defaultThemeMode;
                }
            }
            if (themeMode === "system") {
                themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
            }
            document.documentElement.setAttribute("data-theme", themeMode);
        }
    </script>
    <!--end::Theme mode setup on page load-->

    <!--Begin::Google Tag Manager (noscript) -->
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5FS8GGP" height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>
    <!--End::Google Tag Manager (noscript) -->

    <!--begin::Main-->
    <!--begin::Root-->
    <div class="d-flex flex-column flex-root">
        <!--begin::Page bg image-->
        <style>
            body {
                background-image: url('<?= base_url() ?>assets/media/auth/bg1.jpg');
            }

            [data-theme="dark"] body {
                background-image: url('<?= base_url() ?>assets/media/auth/bg1-dark.jpg');
            }
        </style>
        <!--end::Page bg image-->

        <!--begin::Authentication - Signup Welcome Message -->
        <div class="d-flex flex-column flex-center flex-column-fluid">
            <!--begin::Content-->
            <div class="d-flex flex-column flex-center text-center p-10">
                <!--begin::Wrapper-->
                <div class="card card-flush w-lg-650px py-5">
                    <div class="card-body py-15 py-lg-20">
                        <!--begin::Title-->
                        <h1 class="fw-bolder fs-2hx text-gray-900 mb-4">Oops!</h1>
                        <!--end::Title-->

                        <!--begin::Text-->
                        <div class="fw-semibold fs-6 text-gray-500 mb-7">We can't find that page.</div>
                        <!--end::Text-->

                        <!--begin::Illustration-->
                        <div class="mb-3">
                            <img src="<?= base_url() ?>assets/media/auth/404-error.png" class="mw-100 mh-300px theme-light-show" alt="" />
                            <img src="<?= base_url() ?>assets/media/auth/404-error-dark.png" class="mw-100 mh-300px theme-dark-show" alt="" />
                        </div>
                        <!--end::Illustration-->

                        <!--begin::Link-->
                        <div class="mb-0">
                            <a href="<?= base_url() ?>" class="btn btn-sm btn-primary">Return Home</a>
                        </div>
                        <!--end::Link-->
                    </div>
                </div>
                <!--end::Wrapper-->
            </div>
            <!--end::Content-->
        </div>
        <!--end::Authentication - Signup Welcome Message-->
    </div>
    <!--end::Root-->
    <!--end::Main-->

    <!--begin::Javascript-->
    <!--begin::Global Javascript Bundle(mandatory for all pages)-->
    <script src="<?= base_url() ?>assets/plugins/global/plugins.bundle.js"></script>
    <script src="<?= base_url() ?>assets/js/scripts.bundle.js"></script>
    <!--end::Global Javascript Bundle-->
    <!--end::Javascript-->
</body>
<!--end::Body-->

</html>