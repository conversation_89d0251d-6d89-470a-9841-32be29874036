<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsProduct $msproduct
 * @property MsCategory $mscategory
 * @property MsVendor $msvendor
 * @property ApiKeys $apikeys
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver $db
 * @property ProductPriceLog $productpricelog
 * @property MsTempProduct $mstempproduct
 */
class Product extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('MsCategory', 'mscategory');
        $this->load->model('MsVendor', 'msvendor');
        $this->load->model('ApiKeys', 'apikeys');
        $this->load->model('ProductPriceLog', 'productpricelog');
        $this->load->model('MsTempProduct', 'mstempproduct');
    }

    public function prabayar()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'SMM') {
            return redirect(base_url('dashboard'));
        }

        $where = array(
            'a.userid' => getCurrentIdUser(),
            'a.category_apikey' => 'PPOB',
            'a.subcategory_apikey' => 'PRABAYAR'
        );

        if (getCurrentUser()->multivendor != 1) {
            $currentvendor = getCurrentVendor('PPOB', getCurrentIdUser());
            $where['a.vendor'] = $currentvendor;
            $where['a.vendorid'] = null;
        } else {
            $where['a.vendorid !='] = null;
            $where['a.vendorenabled'] = 1;
        }

        $where_vendor = "";
        if (getCurrentUser()->multivendor != 1) {
            $currentvendor = getCurrentVendor('PPOB', getCurrentIdUser());
            $where_vendor = "AND vendor = '$currentvendor' AND vendorid IS NULL";
        } else {
            $where_vendor = "AND vendorid IS NOT NULL AND vendorenabled = 1";
        }

        $category = $this->db->query("SELECT * FROM (SELECT category FROM msproduct WHERE userid = '" . getCurrentIdUser() . "' $where_vendor AND subcategory_apikey = 'PRABAYAR' GROUP BY category UNION ALL SELECT name AS category FROM mscategory WHERE servicetype = 'PPOB' AND userid = '" . getCurrentIdUser() . "') a ORDER BY a.category ASC")->result();

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Produk Prabayar';
        $data['content'] = 'product/prabayar';
        $data['last_update'] = $this->msproduct->order_by('a.updateddate', 'DESC')
            ->limit(1)
            ->get($where)
            ->row()
            ->updateddate ?? null;
        $data['category'] = $category;

        return $this->load->view('master', $data);
    }

    public function datatables_prabayar()
    {
        try {
            if (isLogin() && isUser() && $this->user->companycategory != 'SMM' && getCurrentUser()->licenseid != null && getCurrentUser()->licenseid != 3) {
                $data = array();

                $datatable = $this->datatables->make('MsProduct', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.userid' => getCurrentIdUser(),
                    'a.category_apikey' => 'PPOB',
                    'a.subcategory_apikey' => 'PRABAYAR',
                );

                if (getCurrentUser()->multivendor != 1) {
                    $vendor = getCurrentVendor('PPOB', getCurrentIdUser());
                    $where["(a.vendor = '$vendor' OR a.vendor IS NULL) ="] = true;
                    $where['a.vendorid'] = null;
                } else {
                    $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
                }

                $kategori = getPost('kategori');
                $brand = getPost('brand');
                $status = getPost('status');

                if ($kategori != null) {
                    $where['a.category'] = $kategori;
                }

                if ($brand != null) {
                    $where['a.brand'] = $brand;
                }

                if ($status != null) {
                    $where['a.status'] = $status == 'Normal' ? 1 : 0;
                }

                foreach ($datatable->getData($where) as $key => $value) {
                    if ($value->status == 1) {
                        $status = "<span class=\"badge badge-success\">Normal</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger\">Gangguan</span>";
                    }

                    if ($value->dontupdate == 1) {
                        $dontupdate = "<span class=\"badge badge-success\">Ya</span>";
                    } else {
                        $dontupdate = "<span class=\"badge badge-danger\">Tidak</span>";
                    }

                    $detail = array();
                    $detail[] = $value->vendor ?? '-';
                    $detail[] = $value->code ?? '-';
                    $detail[] = $value->productname;
                    $detail[] = $value->category;
                    $detail[] = $value->type ?? '-';
                    $detail[] = $value->brand;
                    $detail[] = $value->description ?? '-';
                    $detail[] = IDR($value->vendorprice);
                    $detail[] = IDR($value->price);
                    $detail[] = IDR($value->profit);
                    $detail[] = $value->isstock == 1 ? $value->stock : '-';
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = DateFormat($value->updateddate, 'd F Y H:i:s');
                    $detail[] = $status;
                    $detail[] = $dontupdate;
                    if ($value->rata2 != null) {
                        $rata2 = explode(':', date('H:i:s', strtotime($value->rata2)));
                        $detail[] = $rata2[0] . " jam " . $rata2[1] . " menit " . $rata2[2] . " detik";
                    } else {
                        $detail[] = "-";
                    }

                    if ($value->vendor == null) {
                        $action = "<a href=\"" . base_url('product/prabayar/edit/' . $value->id) . "\" class=\"btn btn-icon btn-warning btn-sm mb-1\">
                        <i class=\"fa fa-edit\"></i>
                    </a>
                    
                    <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"deleteProduct('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-trash\"></i>
                    </a>";

                        $detail[] = $action;
                    } else {
                        $detail[] = "<a href=\"" . base_url('product/prabayar/edit/' . $value->id) . "\" class=\"btn btn-icon btn-warning btn-sm mb-1\">
                        <i class=\"fa fa-edit\"></i>
                    </a>";
                    }

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add_prabayar()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'SMM') {
            return redirect(base_url('dashboard'));
        }

        if (getCurrentUser()->multivendor != 1) {
            $apikeysppob = getCurrentAPIKeys('PPOB', getCurrentIdUser());
            $vendor = $this->apikeys->select('vendor as vendorid,vendor as name')
                ->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'PPOB',
                ))
                ->get()
                ->result();
        } else {
            $apikeysppob = null;
            $vendor = $this->msvendor->select('id as vendorid,name')
                ->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'isactive' => 1
                ))
                ->get()
                ->result();
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tambah Produk Prabayar';
        $data['content'] = 'product/prabayar/add';
        $data['category'] = $this->mscategory->getCategory(getCurrentIdUser(), 'PPOB', $apikeysppob->vendor ?? null)->result();
        $data['vendor'] = $vendor;

        return $this->load->view('master', $data);
    }

    public function process_add_prabayar()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'SMM') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $kode_produk = getPost('kode_produk');
            $nama_produk = getPost('nama_produk');
            $kategori_produk = getPost('kategori_produk');
            $brand = getPost('brand');
            $deskripsi = getPost('deskripsi');
            $harga_jual = getPost('harga_jual');
            $harga_vendor = getPost('harga_vendor', 0);
            $isvendor = getPost('isvendor', 0);
            $isstock = getPost('isstock', 0);
            $isdatabase = getPost('isdatabase', 0);
            $database = getPost('database');
            $stock = getPost('stock');
            $stockproduct = getPost('stockproduct');
            $vendor = getPost('vendor');

            if ($isvendor > 0) {
                if ($kode_produk == null) {
                    throw new Exception('Kode produk tidak boleh kosong');
                }

                if ($vendor == null) {
                    throw new Exception('Vendor tidak boleh kosong');
                } else {
                    if (getCurrentUser()->multivendor != 1) {
                        if ($vendor != (getCurrentAPIKeys('PPOB')->vendor ?? null)) {
                            throw new Exception('Vendor tidak ditemukan');
                        }
                    } else {
                        $getvendor = $this->msvendor->select('name')
                            ->where(array(
                                'createdby' => getCurrentIdUser(),
                                'category' => 'PPOB',
                                'isactive' => 1,
                                'id' => $vendor
                            ))
                            ->total();

                        if ($getvendor == 0) {
                            throw new Exception('Vendor tidak ditemukan');
                        }
                    }
                }
            } else {
                if ($isstock > 0) {
                    if ($stock == null) {
                        throw new Exception('Stok produk tidak boleh kosong');
                    } else if (!is_numeric($stock)) {
                        throw new Exception('Stok produk harus berupa angka');
                    } else if ($stock <= 0) {
                        throw new Exception('Stok produk tidak boleh kurang dari 0');
                    }
                } else if ($isdatabase > 0) {
                    if ($database == null) {
                        throw new Exception('Database tidak boleh kosong');
                    } else if ($database == 'Stock Product') {
                        if ($stockproduct == null) {
                            throw new Exception('Stock Product tidak boleh kosong');
                        } else {
                            $cekdatabse = $this->msproduct->total(array(
                                'databasecategory' => 'Stock Product',
                                'userid' => getCurrentIdUser(),
                                'databaseid' => $stockproduct
                            ));

                            if ($cekdatabse > 0) {
                                throw new Exception('Stock Product sudah digunakan');
                            }
                        }
                    } else {
                        throw new Exception('Database tidak ditemukan');
                    }
                } else if ($harga_vendor == null) {
                    throw new Exception('Harga vendor produk tidak boleh kosong');
                } else if (!is_numeric($harga_vendor)) {
                    throw new Exception('Harga vendor produk harus berupa angka');
                } else if ($harga_vendor < 0) {
                    throw new Exception('Harga vendor produk tidak boleh kurang dari 0');
                }
            }

            if ($nama_produk == null) {
                throw new Exception('Nama produk tidak boleh kosong');
            } else if ($kategori_produk == null) {
                throw new Exception('Kategori produk tidak boleh kosong');
            } else if ($brand == null) {
                throw new Exception('Brand produk tidak boleh kosong');
            } else if ($harga_jual == null) {
                throw new Exception('Harga jual produk tidak boleh kosong');
            } else if (!is_numeric($harga_jual)) {
                throw new Exception('Harga jual produk harus berupa angka');
            } else if ($harga_jual < 0) {
                throw new Exception('Harga jual produk tidak boleh kurang dari 0');
            } else {
                $htmlpurifierconfig = HTMLPurifier_Config::createDefault();
                $purifier = new HTMLPurifier($htmlpurifierconfig);

                $nama_produk = $purifier->purify($nama_produk);
                $brand = $purifier->purify($brand);
                $deskripsi = $purifier->purify($deskripsi);
            }

            if (getCurrentUser(getCurrentIdUser())->licenseid == null) {
                $get = $this->msproduct->total(array(
                    'userid' => getCurrentIdUser()
                ));

                if ($get >= 10) {
                    throw new Exception('Anda tidak dapat menambahkan produk lebih dari 10 produk');
                }
            }

            $insert = array();
            if ($isvendor == 1) {
                $get = $this->msproduct->total(array(
                    'code' => $kode_produk,
                    'userid' => getCurrentIdUser(),
                ));

                if ($get > 0) {
                    throw new Exception('Kode Produk sudah digunakan');
                }

                $insert['code'] = $kode_produk;

                if (getCurrentUser()->multivendor != 1) {
                    $insert['vendor'] = $vendor;
                } else {
                    $insert['vendorid'] = $vendor;
                    $insert['vendorenabled'] = 1;
                }
            } else {
                $insert['code'] = null;

                if ($isstock == 1) {
                    $insert['isstock'] = 1;
                    $insert['stock'] = $stock;
                } else if ($isdatabase == 1) {
                    $insert['isdatabase'] = 1;
                    $insert['databasecategory'] = $database;

                    if ($database == 'Stock Product') {
                        $insert['databaseid'] = $stockproduct;
                    }
                }
            }

            $insert['userid'] = getCurrentIdUser();
            $insert['productname'] = $nama_produk;
            $insert['description'] = $deskripsi;
            $insert['status'] = 1;
            $insert['price'] = $harga_jual;
            $insert['category'] = $kategori_produk;
            $insert['brand'] = $brand;
            $insert['profit'] = $harga_jual - $harga_vendor;
            $insert['vendorprice'] = $harga_vendor;
            $insert['category_apikey'] = 'PPOB';
            $insert['subcategory_apikey'] = 'PRABAYAR';
            $insert['ismanualadd'] = 1;

            $this->msproduct->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Produk gagal ditambahkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil ditambahkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit_prabayar($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'SMM') {
            return redirect(base_url('dashboard'));
        }

        $get = $this->msproduct->get(array(
            'userid' => getCurrentIdUser(),
            'id' => $id,
            'category_apikey' => 'PPOB',
            'subcategory_apikey' => 'PRABAYAR',
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('product/prabayar'));
        }

        if (getCurrentUser()->multivendor != 1) {
            $apikeysppob = getCurrentAPIKeys('PPOB', getCurrentIdUser());
            $vendor = $this->apikeys->select('vendor as vendorid,vendor as name')
                ->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'PPOB',
                ))
                ->get()
                ->result();
            $selectedvendor = $get->row()->vendor ?? null;
        } else {
            $apikeysppob = null;
            $vendor = $this->msvendor->select('id as vendorid,name')
                ->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'isactive' => 1
                ))
                ->get()
                ->result();
            $selectedvendor = $get->row()->vendorid ?? null;
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Ubah Produk Prabayar';
        $data['content'] = 'product/prabayar/edit';
        $data['category'] = $this->mscategory->getCategory(getCurrentIdUser(), 'PPOB', $apikeysppob->vendor ?? null)->result();
        $data['product'] = $get->row();
        $data['vendor'] = $vendor;
        $data['selectedvendor'] = $selectedvendor;

        return $this->load->view('master', $data);
    }

    public function process_edit_prabayar($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'SMM') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $get = $this->msproduct->get(array(
                'userid' => getCurrentIdUser(),
                'id' => $id,
                'category_apikey' => 'PPOB',
                'subcategory_apikey' => 'PRABAYAR',
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $productRow = $get->row();

            $kode_produk = getPost('kode_produk');
            $nama_produk = getPost('nama_produk');
            $kategori_produk = getPost('kategori_produk');
            $brand = getPost('brand');
            $deskripsi = getPost('deskripsi');
            $harga_jual = getPost('harga_jual');
            $harga_vendor = getPost('harga_vendor');
            $status = getPost('status');
            $dontupdate = getPost('dontupdate');
            $isvendor = getPost('isvendor', 0);
            $isstock = getPost('isstock', 0);
            $isdatabase = getPost('isdatabase', 0);
            $stock = getPost('stock');
            $database = getPost('database');
            $stockproduct = getPost('stockproduct');
            $vendor = getPost('vendor');

            if ($isvendor > 0) {
                if ($kode_produk == null) {
                    throw new Exception('Kode Produk tidak boleh kosong');
                }

                if ($vendor == null) {
                    throw new Exception('Vendor tidak boleh kosong');
                } else {
                    if (getCurrentUser()->multivendor != 1) {
                        if ($vendor != (getCurrentAPIKeys('PPOB')->vendor ?? null)) {
                            throw new Exception('Vendor tidak ditemukan');
                        }
                    } else {
                        $getvendor = $this->msvendor->select('name')
                            ->where(array(
                                'createdby' => getCurrentIdUser(),
                                'category' => 'PPOB',
                                'isactive' => 1,
                                'id' => $vendor
                            ))
                            ->total();

                        if ($getvendor == 0) {
                            throw new Exception('Vendor tidak ditemukan');
                        }
                    }
                }
            } else {
                if ($harga_vendor == null) {
                    if ($productRow->vendor == null) {
                        throw new Exception('Harga vendor produk tidak boleh kosong');
                    }
                } else if (!is_numeric($harga_vendor)) {
                    if ($productRow->vendor == null) {
                        throw new Exception('Harga vendor produk harus berupa angka');
                    }
                } else if ($harga_vendor < 0) {
                    if ($productRow->vendor == null) {
                        throw new Exception('Harga vendor produk tidak boleh kurang dari 0');
                    }
                } else if ($isstock > 0) {
                    if ($stock == null) {
                        throw new Exception('Stok produk tidak boleh kosong');
                    } else if (!is_numeric($stock)) {
                        throw new Exception('Stok produk harus berupa angka');
                    } else if ($stock <= 0) {
                        throw new Exception('Stok produk tidak boleh kurang dari 0');
                    }
                } else if ($isdatabase > 0) {
                    if ($database == null) {
                        throw new Exception('Database tidak boleh kosong');
                    } else if ($database == 'Stock Product') {
                        if ($stockproduct == null) {
                            throw new Exception('Stock Product tidak boleh kosong');
                        } else {
                            $cekdatabse = $this->msproduct->total(array(
                                'databasecategory' => 'Stock Product',
                                'userid' => getCurrentIdUser(),
                                'databaseid' => $stockproduct,
                                'id !=' => $id
                            ));

                            if ($cekdatabse > 0) {
                                throw new Exception('Stock Product sudah digunakan');
                            }
                        }
                    } else {
                        throw new Exception('Database tidak ditemukan');
                    }
                }
            }

            if ($kode_produk == null && $isvendor > 0) {
                if ($productRow->vendor == null) {
                    throw new Exception('Kode produk tidak boleh kosong');
                }
            } else if ($nama_produk == null) {
                if ($productRow->vendor == null) {
                    throw new Exception('Nama produk tidak boleh kosong');
                }
            } else if ($kategori_produk == null) {
                if ($productRow->vendor == null) {
                    throw new Exception('Kategori produk tidak boleh kosong');
                }
            } else if ($brand == null) {
                if ($productRow->vendor == null) {
                    throw new Exception('Brand produk tidak boleh kosong');
                }
            } else if ($harga_jual == null) {
                if ($productRow->vendor == null) {
                    throw new Exception('Harga jual produk tidak boleh kosong');
                }
            } else if (!is_numeric($harga_jual)) {
                if ($productRow->vendor == null) {
                    throw new Exception('Harga jual produk harus berupa angka');
                }
            } else if ($harga_jual < 0) {
                if ($productRow->vendor == null) {
                    throw new Exception('Harga jual produk tidak boleh kurang dari 0');
                }
            } else {
                $htmlpurifierconfig = HTMLPurifier_Config::createDefault();
                $purifier = new HTMLPurifier($htmlpurifierconfig);

                $nama_produk = $purifier->purify($nama_produk);
                $brand = $purifier->purify($brand);
                $deskripsi = $purifier->purify($deskripsi);
            }

            $update = array();
            if ($productRow->vendor == null && $isvendor > 0) {
                if ($productRow->code != $kode_produk) {
                    $get = $this->msproduct->total(array(
                        'code' => $kode_produk,
                        'userid' => getCurrentIdUser(),
                    ));

                    if ($get > 0) {
                        throw new Exception('Kode produk sudah digunakan');
                    }
                }

                if (getCurrentUser()->multivendor != 1) {
                    $update['vendor'] = $vendor;
                    $update['vendorid'] = null;
                    $update['vendorenabled'] = null;
                } else {
                    $update['vendorid'] = $vendor;
                    $update['vendorenabled'] = 1;
                    $update['vendor'] = null;
                }
            }

            if ($productRow->productname != $nama_produk) {
                $get = $this->msproduct->total(array(
                    'UPPER(productname) =' => strtoupper($nama_produk),
                    'userid' => getCurrentIdUser(),
                ));

                if ($get > 0) {
                    throw new Exception('Nama produk sudah digunakan');
                }
            }

            $update['userid'] = getCurrentIdUser();
            $update['productname'] = $nama_produk;
            $update['description'] = $deskripsi;
            $update['status'] = $status ? '1' : '0';

            if ($productRow->vendor == null) {
                if ($isvendor == 1) {
                    $update['code'] = $kode_produk;

                    $update['vendorprice'] = 0;
                } else {
                    $update['code'] = null;

                    if ($isstock == 1 && $isdatabase != 1) {
                        $update['isstock'] = 1;
                        $update['stock'] = $stock;
                    } else {
                        $update['isstock'] = 0;
                        $update['stock'] = null;
                    }

                    if ($isdatabase == 1 && $isstock != 1) {
                        $update['isdatabase'] = 1;
                        $update['databasecategory'] = $database;

                        if ($database == 'Stock Product') {
                            $update['databaseid'] = $stockproduct;
                        } else {
                            $update['databaseid'] = null;
                        }
                    } else {
                        $update['isdatabase'] = 0;
                        $update['databasecategory'] = null;
                        $update['databaseid'] = null;
                    }

                    $update['vendorprice'] = $harga_vendor;
                }


                $update['price'] = $harga_jual;
                $update['category'] = $kategori_produk;
                $update['brand'] = $brand;
                $update['profit'] = $harga_jual - $harga_vendor;

                $update['category_apikey'] = 'PPOB';
                $update['subcategory_apikey'] = 'PRABAYAR';
            } else {
                $update['dontupdate'] = $dontupdate ? '1' : '0';
                $update['price'] = $harga_jual;
            }

            $this->msproduct->update(array(
                'id' => $id
            ), $update);

            $checkpricelog = $this->productpricelog->order_by('a.createddate', 'DESC')
                ->get(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.productid' => $id,
                    'DATE(a.createddate) =' => getCurrentDate('Y-m-d')
                ));

            if ($checkpricelog->num_rows() == 0) {
                $insert = array();
                $insert['userid'] = getCurrentIdUser();
                $insert['productid'] = $id;
                $insert['type'] = $productRow->price > $harga_jual ? 'DOWN' : 'UP';
                $insert['oldprice'] = $productRow->price;
                $insert['newprice'] = $harga_jual;
                $insert['createddate'] = getCurrentDate();
                $insert['createdby'] = getCurrentIdUser();

                $this->productpricelog->insert($insert);
            } else {
                $update = array();
                $update['type'] = $productRow->price > $harga_jual ? 'DOWN' : 'UP';
                $update['oldprice'] = $productRow->price;
                $update['newprice'] = $harga_jual;
                $update['updateddate'] = getCurrentDate();
                $update['updatedby'] = getCurrentIdUser();

                $this->productpricelog->update(array(
                    'id' => $checkpricelog->row()->id
                ), $update);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Produk gagal diubah');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_prabayar()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'SMM') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = stringEncryption('decrypt', getPost('id'));

            $get = $this->msproduct->total(array(
                'userid' => getCurrentIdUser(),
                'id' => $id,
                'category_apikey' => 'PPOB',
                'subcategory_apikey' => 'PRABAYAR',
                'vendor' => null
            ));

            if ($get == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $this->msproduct->delete(array(
                'id' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Produk gagal dihapus');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil dihapus');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function pascabayar()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'SMM') {
            return redirect(base_url('dashboard'));
        }

        $where = array(
            'a.userid' => getCurrentIdUser(),
            'a.category_apikey' => 'PPOB',
            'a.subcategory_apikey' => 'PASCABAYAR'
        );

        if (getCurrentUser()->multivendor != 1) {
            $currentvendor = getCurrentVendor('PPOB', getCurrentIdUser());
            $where['a.vendor'] = $currentvendor;
            $where['a.vendorid'] = null;
        } else {
            $where['a.vendorid !='] = null;
            $where['a.vendorenabled'] = 1;
        }

        $category = $this->msproduct->select('category')
            ->where($where)
            ->group_by('category')
            ->result();

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Produk Pascabayar';
        $data['content'] = 'product/pascabayar';
        $data['last_update'] = $this->msproduct->order_by('a.updateddate', 'DESC')
            ->limit(1)
            ->get($where)
            ->row()
            ->updateddate ?? null;
        $data['category'] = $category;

        return $this->load->view('master', $data);
    }

    public function datatables_pascabayar()
    {
        try {
            if (isLogin() && isUser() && $this->user->companycategory != 'SMM' && getCurrentUser()->licenseid != null && getCurrentUser()->licenseid != 3) {
                $data = array();

                $datatable = $this->datatables->make('MsProduct', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.userid' => getCurrentIdUser(),
                    'a.category_apikey' => 'PPOB',
                    'a.subcategory_apikey' => 'PASCABAYAR',
                );

                if (getCurrentUser()->multivendor != 1) {
                    $vendor = getCurrentVendor('PPOB', getCurrentIdUser());
                    $where["(a.vendor = '$vendor' OR a.vendor IS NULL) ="] = true;
                    $where['a.vendorid'] = null;
                } else {
                    $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
                }

                $kategori = getPost('kategori');
                $brand = getPost('brand');
                $status = getPost('status');

                if ($kategori != null) {
                    $where['a.category'] = $kategori;
                }

                if ($brand != null) {
                    $where['a.brand'] = $brand;
                }

                if ($status != null) {
                    $where['a.status'] = $status == 'Normal' ? 1 : 0;
                }
                foreach ($datatable->getData($where) as $key => $value) {
                    if ($value->status == 1) {
                        $status = "<span class=\"badge badge-success\">Normal</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger\">Gangguan</span>";
                    }

                    $detail = array();
                    $detail[] = $value->code;
                    $detail[] = $value->productname;
                    $detail[] = $value->brand;
                    $detail[] = $value->description ?? '-';
                    $detail[] = IDR($value->vendorprice);
                    $detail[] = IDR($value->admin);
                    $detail[] = IDR($value->commission);
                    $detail[] = IDR($value->profit);
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = DateFormat($value->updateddate, 'd F Y H:i:s');
                    $detail[] = $status;
                    if ($value->rata2 != null) {
                        $rata2 = explode(':', date('H:i:s', strtotime($value->rata2)));
                        $detail[] = $rata2[0] . " jam " . $rata2[1] . " menit " . $rata2[2] . " detik";
                    } else {
                        $detail[] = "-";
                    }

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function smm()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'PPOB') {
            return redirect(base_url('dashboard'));
        }

        $where = array(
            'a.userid' => getCurrentIdUser(),
            'a.category_apikey' => 'SMM',
        );

        if (getCurrentUser()->multivendor != 1) {
            $currentvendor = getCurrentVendor('SMM', getCurrentIdUser());
            $where['a.vendor'] = $currentvendor;
            $where['a.vendorid'] = null;
        } else {
            $where['a.vendorid !='] = null;
            $where['a.vendorenabled'] = 1;
        }

        $category = $this->msproduct->select('category')
            ->where($where)
            ->group_by('category')
            ->result();

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Produk Media Sosial';
        $data['content'] = 'product/smm';
        $data['last_update'] = $this->msproduct->order_by('a.updateddate', 'DESC')
            ->limit(1)
            ->get($where)
            ->row()
            ->updateddate ?? null;
        $data['category'] = $category;

        return $this->load->view('master', $data);
    }

    public function edit_smm($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'PPOB') {
            return redirect(base_url('dashboard'));
        }

        $get = $this->msproduct->get(array(
            'userid' => getCurrentIdUser(),
            'id' => $id,
            'category_apikey' => 'SMM',
            'subcategory_apikey' => 'SMM'
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('product/smm'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Ubah Produk Media Sosial';
        $data['content'] = 'product/smm/edit';
        $data['product'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit_smm($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'PPOB') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $get = $this->msproduct->get(array(
                'userid' => getCurrentIdUser(),
                'id' => $id,
                'category_apikey' => 'SMM',
                'subcategory_apikey' => 'SMM',
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $row = $get->row();

            $nama_produk = getPost('nama_produk');
            $deskripsi = getPost('deskripsi');
            $status = getPost('status');
            $dontupdate = getPost('dontupdate');

            if ($nama_produk == null) {
                throw new Exception('Nama produk tidak boleh kosong');
            } else if ($deskripsi == null) {
                throw new Exception('Deskripsi produk tidak boleh kosong');
            } else {
                $htmlpurifierconfig = HTMLPurifier_Config::createDefault();
                $purifier = new HTMLPurifier($htmlpurifierconfig);

                $nama_produk = $purifier->purify($nama_produk);
                $deskripsi = $purifier->purify($deskripsi);
            }

            if ($row->productname != $nama_produk) {
                $where = array(
                    'userid' => getCurrentIdUser(),
                    'productname' => $nama_produk,
                );

                if (getCurrentUser()->multivendor != 1) {
                    $apikeysmm = getCurrentAPIKeys('SMM');

                    $where['vendorid'] = null;
                    $where['vendor'] = $apikeysmm->vendor;
                } else {
                    $where['vendorid !='] = null;
                    $where['vendorenabled'] = 1;
                }

                $check = $this->msproduct->total($where);

                if ($check > 0) {
                    throw new Exception('Nama produk sudah digunakan');
                }
            }

            $update = array();
            $update['userid'] = getCurrentIdUser();
            $update['productname'] = $nama_produk;
            $update['description'] = $deskripsi;
            $update['status'] = $status ? '1' : '0';
            $update['dontupdate'] = $dontupdate ? '1' : '0';

            $this->msproduct->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Produk gagal diubah');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_smm()
    {
        try {
            if (isLogin() && isUser() && $this->user->companycategory != 'PPOB' && getCurrentUser()->licenseid != null && getCurrentUser()->licenseid != 2) {
                $data = array();

                $datatable = $this->datatables->make('MsProduct', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.userid' => getCurrentIdUser(),
                    'a.category_apikey' => 'SMM',
                    'a.subcategory_apikey' => 'SMM',
                    'a.category !=' => null
                );

                if (getCurrentUser()->multivendor != 1) {
                    $vendor = getCurrentVendor('SMM', getCurrentIdUser());
                    $where['a.vendor'] = $vendor;
                    $where['a.vendorid'] = null;
                } else {
                    $where['a.vendorid !='] = null;
                    $where['a.vendorenabled'] = 1;
                }

                $kategori = getPost('kategori');
                $status = getPost('status');

                if ($kategori != null) {
                    $where['a.category'] = $kategori;
                }

                if ($status != null) {
                    $where['a.status'] = $status == 'Normal' ? 1 : 0;
                }

                foreach ($datatable->getData($where) as $key => $value) {
                    if ($value->status == 1) {
                        $status = "<span class=\"badge badge-success\">Normal</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger\">Gangguan</span>";
                    }

                    $detail = array();
                    $detail[] = $value->vendor ?? '-';
                    $detail[] = $value->productname;
                    $detail[] = $value->category;
                    $detail[] = IDR($value->vendorprice);
                    $detail[] = IDR($value->price);
                    $detail[] = IDR($value->profit);
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = DateFormat($value->updateddate, 'd F Y H:i:s');
                    $detail[] = $status;
                    if ($value->rata2 != null) {
                        $rata2 = explode(':', date('H:i:s', strtotime($value->rata2)));
                        $detail[] = $rata2[0] . " jam " . $rata2[1] . " menit " . $rata2[2] . " detik";
                    } else {
                        $detail[] = "-";
                    }

                    $action = "";
                    if ($value->iscustom == null || $value->iscustom == 0) {
                        $action = "<button type=\"button\" class=\"btn btn-success btn-sm mb-1 me-1\" onclick=\"addAdditional(`$value->productname`, '" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-plus\"></i>
                        <span>Add Additional</span>
                    </button>";

                        $detail[] = "<i class=\"fa fa-times text-danger\"></i>";
                    } else {
                        $action = "<button type=\"button\" class=\"btn btn-danger btn-sm mb-1 me-1\" onclick=\"removeAdditional(`$value->productname`, '" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-minus\"></i>
                        <span>Remove Additional</span>
                    </button>";

                        $detail[] = "<i class=\"fa fa-check text-success\"></i>";
                    }

                    $action .= "<a href=\"" . base_url('product/smm/edit/' . $value->id) . "\" class=\"btn btn-icon btn-warning btn-sm mb-1\">
                    <i class=\"fa fa-edit\"></i>
                </a>";

                    $detail[] = $action;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add_smm()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'PPOB') {
            return redirect(base_url('dashboard'));
        }

        if (getCurrentUser()->multivendor != 1) {
            $apikeyssmm = getCurrentAPIKeys('SMM', getCurrentIdUser());
        } else {
            $apikeyssmm = null;
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tambah Produk Media Sosial';
        $data['content'] = 'product/smm/add';
        $data['category'] = $this->mscategory->getCategory(getCurrentIdUser(), 'SMM', $apikeyssmm->vendor ?? null)->result();

        return $this->load->view('master', $data);
    }

    public function process_add_smm()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'PPOB') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id_layanan = getPost('id_layanan');
            $nama_produk = getPost('nama_produk');
            $kategori_produk = getPost('kategori_produk');
            $status_produk = getPost('status_produk', null);
            $deskripsi = getPost('deskripsi');
            $harga_jual = getPost('harga_jual');
            $harga_vendor = getPost('harga_vendor');
            $min_order = getPost('min_order', null);
            $max_order = getPost('max_order', null);
            $isvendor = getPost('isvendor', 0);

            if ($isvendor > 0) {
                if ($id_layanan == null) {
                    throw new Exception('ID Layanan tidak boleh kosong');
                }
            } else if ($nama_produk == null) {
                throw new Exception('Nama produk tidak boleh kosong');
            } else if ($kategori_produk == null) {
                throw new Exception('Kategori produk tidak boleh kosong');
            } else if ($harga_jual == null) {
                throw new Exception('Harga jual produk tidak boleh kosong');
            } else if (!is_numeric($harga_jual)) {
                throw new Exception('Harga jual produk harus berupa angka');
            } else if ($harga_jual < 0) {
                throw new Exception('Harga jual produk tidak boleh kurang dari 0');
            } else if ($harga_vendor == null) {
                throw new Exception('Harga vendor produk tidak boleh kosong');
            } else if (!is_numeric($harga_vendor)) {
                throw new Exception('Harga vendor produk harus berupa angka');
            } else if ($harga_vendor < 0) {
                throw new Exception('Harga vendor produk tidak boleh kurang dari 0');
            } else if ($min_order == null) {
                throw new Exception('Minimal order tidak boleh kosong');
            } else if (!is_numeric($min_order)) {
                throw new Exception('Minimal order harus berupa angka');
            } else if ($min_order < 0) {
                throw new Exception('Minimal order tidak boleh kurang dari 0');
            } else if ($max_order == null) {
                throw new Exception('Maksimal order tidak boleh kosong');
            } else if (!is_numeric($max_order)) {
                throw new Exception('Maksimal order harus berupa angka');
            } else if ($max_order < 0) {
                throw new Exception('Maksimal order tidak boleh kurang dari 0');
            } else if ($max_order < $min_order) {
                throw new Exception('Maksimal order tidak boleh kurang dari minimal order');
            } else {
                $htmlpurifierconfig = HTMLPurifier_Config::createDefault();
                $purifier = new HTMLPurifier($htmlpurifierconfig);

                $nama_produk = $purifier->purify($nama_produk);
                $deskripsi = $purifier->purify($deskripsi);
            }

            if (getCurrentUser(getCurrentIdUser())->licenseid == null) {
                $get = $this->msproduct->total(array(
                    'userid' => getCurrentIdUser()
                ));

                if ($get >= 10) {
                    throw new Exception('Anda tidak dapat menambahkan produk lebih dari 10 produk');
                }
            }

            $insert = array();
            if ($isvendor == 1) {
                $get = $this->msproduct->total(array(
                    'code' => $id_layanan,
                    'userid' => getCurrentIdUser(),
                ));

                if ($get > 0) {
                    throw new Exception('ID Layanan sudah digunakan');
                }

                $insert['code'] = $id_layanan;
            } else {
                $insert['code'] = null;
            }

            $insert['userid'] = getCurrentIdUser();
            $insert['productname'] = $nama_produk;
            $insert['description'] = $deskripsi;
            $insert['status'] = $status_produk ? '1' : '0';
            $insert['price'] = $harga_jual;
            $insert['category'] = $kategori_produk;
            $insert['profit'] = $harga_jual - $harga_vendor;
            $insert['vendorprice'] = $harga_vendor;
            $insert['category_apikey'] = 'SMM';
            $insert['subcategory_apikey'] = 'SMM';
            $insert['vendor'] = getCurrentVendor('SMM');
            $insert['minorder'] = $min_order;
            $insert['maxorder'] = $max_order;
            $insert['ismanualadd'] = 1;

            $this->msproduct->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Produk gagal ditambahkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil ditambahkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add_custom_smm()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'PPOB') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('ID tidak boleh kosong');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->msproduct->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $update = array();
            $update['iscustom'] = 1;

            $this->msproduct->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Produk gagal diubah');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_remove_custom_smm()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'PPOB') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('ID tidak boleh kosong');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->msproduct->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $update = array();
            $update['iscustom'] = null;

            $this->msproduct->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Produk gagal diubah');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function information()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Informasi Layanan';
        $data['content'] = 'product/information';

        return $this->load->view('master', $data);
    }

    public function datatables_information()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();

                $datatable = $this->datatables->make('ProductPriceLog', 'QueryDatatables', 'SearchDatatables');

                $kategori = getPost('kategori');

                $where = array(
                    'a.userid' => getCurrentIdUser(),
                );

                if ($kategori != null) {
                    $where['b.category'] = $kategori;
                }

                foreach ($datatable->getData($where) as $key => $value) {
                    if ($value->type == 'UP') {
                        $status = "<span class=\"badge badge-success\">Harga Naik</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger\">Harga Turun</span>";
                    }

                    $detail = array();
                    $detail[] = $value->productname;
                    $detail[] = $status;
                    $detail[] = "Rp " . IDR($value->oldprice);
                    $detail[] = "Rp " . IDR($value->newprice);
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function filter_information()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $currentiduser = getCurrentIdUser();

        $vendor = null;

        if (getCurrentUser()->multivendor != 1) {
            $currentvendorppob = getCurrentVendor('PPOB', getCurrentIdUser());
            $currentvendorsmm = getCurrentVendor('SMM', getCurrentIdUser());

            if ($currentvendorppob != null && $currentvendorsmm != null) {
                $vendor = "vendor = '$currentvendorppob' OR vendor = '$currentvendorsmm'";
            } else if ($currentvendorppob != null) {
                $vendor = "vendor = '$currentvendorppob'";
            } else if ($currentvendorsmm != null) {
                $vendor = "vendor = '$currentvendorsmm'";
            } else {
                $vendor = "vendor IS NULL";
            }

            $vendor .= " AND vendorid IS NULL";
        } else {
            $vendor = "vendorid IS NOT NULL AND vendorenabled = 1";
        }

        $where = "";
        if ($vendor != null) {
            $where = "AND ( $vendor ) = TRUE";
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('product/information/filter', array(
                "category" => $this->db->query("SELECT * FROM ( SELECT category FROM msproduct WHERE userid = '" . getCurrentIdUser() . "' $where GROUP BY category UNION ALL SELECT NAME AS category FROM mscategory WHERE userid = $currentiduser GROUP BY NAME ) a WHERE a.category IS NOT NULL ORDER BY a.category ASC")->result()
            ), true)
        ));
    }

    public function bulk_delete_prabayar()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'SMM') {
            return redirect(base_url('dashboard'));
        }

        if (getCurrentUser()->multivendor != 1) {
            $vendor = $this->apikeys->select('vendor as vendorid, vendor as name')
                ->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'PPOB',
                ))
                ->get()
                ->result();
        } else {
            $vendor = $this->msvendor->select('id as vendorid, name')
                ->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'isactive' => 1
                ))
                ->get()
                ->result();
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Hapus Produk Prabayar';
        $data['content'] = 'product/prabayar/bulk_delete';
        $data['vendor'] = $vendor;

        return $this->load->view('master', $data);
    }

    public function datatables_bulk_delete_prabayar()
    {
        try {
            if (isLogin() && isUser() && $this->user->companycategory != 'SMM' && getCurrentUser()->licenseid != null && getCurrentUser()->licenseid != 3) {
                $data = array();

                $datatable = $this->datatables->make('MsProduct', 'QueryDatatables', 'SearchDatatables');

                $vendor = getPost('vendor');
                $category = getPost('category');
                $brand = getPost('brand');

                if ($vendor == null) {
                    throw new Exception('Vendor tidak boleh kosong');
                }

                if ($category == null) {
                    throw new Exception('Kategori tidak boleh kosong');
                }

                if ($brand == null) {
                    throw new Exception('Brand tidak boleh kosong');
                }

                $where = array(
                    'a.userid' => getCurrentIdUser(),
                    'a.category_apikey' => 'PPOB',
                    'a.subcategory_apikey' => 'PRABAYAR',
                    "(a.vendor = '$vendor' OR a.vendorid = '$vendor') =" => true,
                    'a.brand' => $brand,
                    'a.category' => $category
                );

                foreach ($datatable->getData($where) as $key => $value) {
                    $detail = array();
                    $detail[] = "<div class=\"form-check form-check-custom form-check-solid ms-3\">
                        <input class=\"form-check-input\" type=\"checkbox\" value=\"" . stringEncryption('encrypt', $value->id) . "\" name=\"productid\" checked>
                    </div>";
                    $detail[] = $value->code;
                    $detail[] = $value->productname;
                    $detail[] = $value->category;
                    $detail[] = $value->type;
                    $detail[] = $value->brand;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_bulk_delete_prabayar()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'SMM') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $productids = getPost('ids', []);

            if (count($productids) == 0) {
                throw new Exception('Produk tidak boleh kosong');
            }

            foreach ($productids as $productid) {
                $id = stringEncryption('decrypt', $productid);

                $this->msproduct->delete(array(
                    'id' => $id,
                    'userid' => getCurrentIdUser(),
                    'category_apikey' => 'PPOB',
                    'subcategory_apikey' => 'PRABAYAR',
                ));
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Produk gagal dihapus');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil dihapus');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function bulk_delete_pascabayar()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'SMM') {
            return redirect(base_url('dashboard'));
        }

        if (getCurrentUser()->multivendor != 1) {
            $vendor = $this->apikeys->select('vendor as vendorid, vendor as name')
                ->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'PPOB',
                ))
                ->get()
                ->result();
        } else {
            $vendor = $this->msvendor->select('id as vendorid, name')
                ->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'isactive' => 1
                ))
                ->get()
                ->result();
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Hapus Produk Pascabayar';
        $data['content'] = 'product/pascabayar/bulk_delete';
        $data['vendor'] = $vendor;

        return $this->load->view('master', $data);
    }

    public function datatables_bulk_delete_pascabayar()
    {
        try {
            if (isLogin() && isUser() && $this->user->companycategory != 'SMM' && getCurrentUser()->licenseid != null && getCurrentUser()->licenseid != 3) {
                $data = array();

                $datatable = $this->datatables->make('MsProduct', 'QueryDatatables', 'SearchDatatables');

                $brand = getPost('brand');

                if ($brand == null) {
                    throw new Exception('Brand tidak boleh kosong');
                }

                $where = array(
                    'a.userid' => getCurrentIdUser(),
                    'a.category_apikey' => 'PPOB',
                    'a.subcategory_apikey' => 'PASCABAYAR',
                    'a.brand' => $brand,
                );

                foreach ($datatable->getData($where) as $key => $value) {
                    $detail = array();
                    $detail[] = "<div class=\"form-check form-check-custom form-check-solid ms-3\">
                        <input class=\"form-check-input\" type=\"checkbox\" value=\"" . stringEncryption('encrypt', $value->id) . "\" name=\"productid\" checked>
                    </div>";
                    $detail[] = $value->code;
                    $detail[] = $value->productname;
                    $detail[] = $value->brand;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_bulk_delete_pascabayar()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'SMM') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $productids = getPost('ids', []);

            if (count($productids) == 0) {
                throw new Exception('Produk tidak boleh kosong');
            }

            foreach ($productids as $productid) {
                $id = stringEncryption('decrypt', $productid);

                $this->msproduct->delete(array(
                    'id' => $id,
                    'userid' => getCurrentIdUser(),
                    'category_apikey' => 'PPOB',
                    'subcategory_apikey' => 'PASCABAYAR',
                ));
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Produk gagal dihapus');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil dihapus');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function bulk_delete_smm()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'PPOB') {
            return redirect(base_url('dashboard'));
        }

        if (getCurrentUser()->multivendor != 1) {
            $vendor = $this->apikeys->select('vendor as vendorid, vendor as name')
                ->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'SMM',
                ))
                ->get()
                ->result();
        } else {
            $vendor = $this->msvendor->select('id as vendorid, name')
                ->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'SMM',
                    'isactive' => 1
                ))
                ->get()
                ->result();
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Hapus Produk Media Sosial';
        $data['content'] = 'product/smm/bulk_delete';
        $data['vendor'] = $vendor;

        return $this->load->view('master', $data);
    }

    public function datatables_bulk_delete_smm()
    {
        try {
            if (isLogin() && (isUser() && $this->user->companycategory != 'PPOB' && getCurrentUser()->licenseid != null && getCurrentUser()->licenseid != 3)) {
                $data = array();

                $datatable = $this->datatables->make('MsProduct', 'QueryDatatables', 'SearchDatatables');

                $vendor = getPost('vendor');
                $category = getPost('category');

                if ($vendor == null) {
                    throw new Exception('Vendor tidak boleh kosong');
                }

                if ($category == null) {
                    throw new Exception('Kategori tidak boleh kosong');
                }

                $where = array(
                    'a.userid' => getCurrentIdUser(),
                    'a.category_apikey' => 'SMM',
                    'a.subcategory_apikey' => 'SMM',
                    "(a.vendor = '$vendor' OR a.vendorid = '$vendor') =" => true,
                    'a.category' => $category
                );

                foreach ($datatable->getData($where) as $key => $value) {
                    $detail = array();
                    $detail[] = "<div class=\"form-check form-check-custom form-check-solid ms-3\">
                        <input class=\"form-check-input\" type=\"checkbox\" value=\"" . stringEncryption('encrypt', $value->id) . "\" name=\"productid\" checked>
                    </div>";
                    $detail[] = $value->code;
                    $detail[] = $value->productname;
                    $detail[] = $value->category;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_bulk_delete_smm()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'PPOB') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $productids = getPost('ids', []);

            if (count($productids) == 0) {
                throw new Exception('Produk tidak boleh kosong');
            }

            foreach ($productids as $productid) {
                $id = stringEncryption('decrypt', $productid);

                $this->msproduct->delete(array(
                    'id' => $id,
                    'userid' => getCurrentIdUser(),
                    'category_apikey' => 'SMM'
                ));
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Produk gagal dihapus');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil dihapus');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function bulk_add_smm()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'PPOB') {
            return redirect(base_url('dashboard'));
        }

        if (getCurrentUser()->multivendor != 1) {
            $vendor = $this->apikeys->select('vendor as vendorid,vendor as name')
                ->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'SMM',
                ))
                ->get()
                ->result();
        } else {
            $vendor = $this->msvendor->select('id as vendorid,name')
                ->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'SMM',
                    'isactive' => 1
                ))
                ->get()
                ->result();
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tambah Produk Media Sosial';
        $data['content'] = 'product/smm/bulk_add';
        $data['vendor'] = $vendor;

        return $this->load->view('master', $data);
    }

    public function select_category_prabayar()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'SMM') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $vendorid = getPost('vendor');

            if (getCurrentUser()->multivendor == 1) {
                $vendor = $this->msvendor->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'isactive' => 1,
                    'id' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendorid' => $vendorid,
                ));
            } else {
                $vendor = $this->apikeys->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'vendor' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendor' => $vendorid,
                    'a.vendorid' => null
                ));
            }

            $category = array();
            foreach ($tempproduct as $key => $value) {
                $data = json_decode($value->data);

                foreach ($data as $k => $v) {
                    if (!in_array($v->category, $category) && $v->category_apikey == 'PPOB' && $v->subcategory_apikey == 'PRABAYAR') {
                        $category[] = $v->category;
                    }
                }
            }

            sort($category);

            return JSONResponse(array(
                'RESULT' => 'OK',
                'DATA' => $category
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function select_brand_prabayar()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'SMM') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $vendorid = getPost('vendor');
            $category = getPost('category');

            if (getCurrentUser()->multivendor == 1) {
                $vendor = $this->msvendor->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'isactive' => 1,
                    'id' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendorid' => $vendorid,
                ));
            } else {
                $vendor = $this->apikeys->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'vendor' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendor' => $vendorid,
                    'a.vendorid' => null
                ));
            }

            $brand = array();
            foreach ($tempproduct as $key => $value) {
                $data = json_decode($value->data);

                foreach ($data as $k => $v) {
                    if ($v->category == $category && !in_array($v->brand, $brand) && $v->category_apikey == 'PPOB' && $v->subcategory_apikey == 'PRABAYAR') {
                        $brand[] = $v->brand;
                    }
                }
            }

            sort($brand);

            return JSONResponse(array(
                'RESULT' => 'OK',
                'DATA' => $brand
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function select_category_smm()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'PPOB') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $vendorid = getPost('vendor');

            if (getCurrentUser()->multivendor == 1) {
                $vendor = $this->msvendor->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'SMM',
                    'isactive' => 1,
                    'id' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendorid' => $vendorid,
                ));
            } else {
                $vendor = $this->apikeys->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'SMM',
                    'vendor' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendor' => $vendorid,
                    'a.vendorid' => null
                ));
            }

            $category = array();
            foreach ($tempproduct as $key => $value) {
                $data = json_decode($value->data);

                foreach ($data as $k => $v) {
                    if (!in_array($v->category, $category) && $v->category_apikey == 'SMM' && $v->subcategory_apikey == 'SMM') {
                        $category[] = $v->category;
                    }
                }
            }

            sort($category);

            return JSONResponse(array(
                'RESULT' => 'OK',
                'DATA' => $category
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function get_data_prabayar()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'SMM') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $vendorid = getPost('vendor');
            $category = getPost('category');
            $brand = getPost('brand');

            if (getCurrentUser()->multivendor == 1) {
                $vendor = $this->msvendor->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'isactive' => 1,
                    'id' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $product = $this->msproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendorid' => $vendorid,
                    'a.category' => $category
                ));

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendorid' => $vendorid,
                ));
            } else {
                $vendor = $this->apikeys->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'vendor' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $product = $this->msproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendor' => $vendorid,
                    'a.vendorid' => null,
                    'a.category' => $category
                ));

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendor' => $vendorid,
                    'a.vendorid' => null
                ));
            }

            $service = array();
            foreach ($product as $key => $value) {
                $service[] = $value->code;
            }

            $product = array();
            foreach ($tempproduct as $key => $value) {
                $data = json_decode($value->data);

                foreach ($data as $k => $v) {
                    if (!array_key_exists($v->code, $product) && $v->category == $category && $v->brand == $brand && !in_array($v->code, $service) && $v->category_apikey == 'PPOB' && $v->subcategory_apikey == 'PRABAYAR') {
                        $product[] = $v;
                    }
                }
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'DATA' => $product
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function get_data_smm()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'PPOB') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $vendorid = getPost('vendor');
            $category = getPost('category');

            if (getCurrentUser()->multivendor == 1) {
                $vendor = $this->msvendor->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'SMM',
                    'isactive' => 1,
                    'id' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $product = $this->msproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendorid' => $vendorid,
                    'a.category' => $category
                ));

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendorid' => $vendorid,
                ));
            } else {
                $vendor = $this->apikeys->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'SMM',
                    'vendor' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $product = $this->msproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendor' => $vendorid,
                    'a.vendorid' => null,
                    'a.category' => $category
                ));

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendor' => $vendorid,
                    'a.vendorid' => null
                ));
            }

            $service = array();
            foreach ($product as $key => $value) {
                $service[] = $value->code;
            }

            $product = array();
            foreach ($tempproduct as $key => $value) {
                $data = json_decode($value->data);

                foreach ($data as $k => $v) {
                    if (!array_key_exists($v->code, $product) && $category == $v->category && !in_array($v->code, $service) && $v->category_apikey == 'SMM' && $v->subcategory_apikey == 'SMM') {
                        $product[$v->code] = $v;
                    }
                }
            }

            sort($product);

            return JSONResponse(array(
                'RESULT' => 'OK',
                'DATA' => $product
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_bulk_add_prabayar()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'SMM') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $ids = getPost('ids', []);
            $vendorid = getPost('vendor');
            $category = getPost('category');

            if (count($ids) == 0) {
                throw new Exception('Produk tidak boleh kosong');
            } else if ($vendorid == null) {
                throw new Exception('Vendor tidak boleh kosong');
            } else if ($category == null) {
                throw new Exception('Kategori tidak boleh kosong');
            }

            $multivendor = false;
            if (getCurrentUser()->multivendor == 1) {
                $vendor = $this->msvendor->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'isactive' => 1,
                    'id' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendorid' => $vendorid,
                ));

                $multivendor = true;
            } else {
                $vendor = $this->apikeys->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'vendor' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendor' => $vendorid,
                    'a.vendorid' => null
                ));
            }

            foreach ($tempproduct as $key => $value) {
                $data = json_decode($value->data);

                foreach ($data as $k => $v) {
                    if (in_array($v->code, $ids)) {
                        if ($multivendor) {
                            $product = $this->msproduct->total(array(
                                'code' => $v->code,
                                'userid' => getCurrentIdUser(),
                                'category' => $category,
                                'vendorid' => $vendorid
                            ));
                        } else {
                            $product = $this->msproduct->total(array(
                                'code' => $v->code,
                                'userid' => getCurrentIdUser(),
                                'category' => $category,
                                'vendor' => $vendorid
                            ));
                        }

                        if ($product > 0) continue;

                        // convert v to array
                        $v = (array)$v;

                        $this->msproduct->insert($v);
                    }
                }
            }

            return JSONResponseDefault('OK', 'Produk berhasil ditambahkan');
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_bulk_add_smm()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'PPOB') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $ids = getPost('ids', []);
            $vendorid = getPost('vendor');
            $category = getPost('category');

            if (count($ids) == 0) {
                throw new Exception('Produk tidak boleh kosong');
            } else if ($vendorid == null) {
                throw new Exception('Vendor tidak boleh kosong');
            } else if ($category == null) {
                throw new Exception('Kategori tidak boleh kosong');
            }

            $multivendor = false;
            if (getCurrentUser()->multivendor == 1) {
                $vendor = $this->msvendor->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'SMM',
                    'isactive' => 1,
                    'id' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendorid' => $vendorid,
                ));

                $multivendor = true;
            } else {
                $vendor = $this->apikeys->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'SMM',
                    'vendor' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendor' => $vendorid,
                    'a.vendorid' => null
                ));
            }

            foreach ($tempproduct as $key => $value) {
                $data = json_decode($value->data);

                foreach ($data as $k => $v) {
                    if (in_array($v->code, $ids)) {
                        if ($multivendor) {
                            $product = $this->msproduct->total(array(
                                'code' => $v->code,
                                'userid' => getCurrentIdUser(),
                                'category' => $category,
                                'vendorid' => $vendorid
                            ));
                        } else {
                            $product = $this->msproduct->total(array(
                                'code' => $v->code,
                                'userid' => getCurrentIdUser(),
                                'category' => $category,
                                'vendor' => $vendorid
                            ));
                        }

                        if ($product > 0) continue;

                        // convert v to array
                        $v = (array)$v;

                        $this->msproduct->insert($v);
                    }
                }
            }

            return JSONResponseDefault('OK', 'Produk berhasil ditambahkan');
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function bulk_add_prabayar()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'SMM') {
            return redirect(base_url('dashboard'));
        }

        if (getCurrentUser()->multivendor != 1) {
            $vendor = $this->apikeys->select('vendor as vendorid,vendor as name')
                ->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'PPOB',
                ))
                ->get()
                ->result();
        } else {
            $vendor = $this->msvendor->select('id as vendorid,name')
                ->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'isactive' => 1
                ))
                ->get()
                ->result();
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tambah Produk Prabayar';
        $data['content'] = 'product/prabayar/bulk_add';
        $data['vendor'] = $vendor;

        return $this->load->view('master', $data);
    }

    public function bulk_delete_all_prabayar()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'SMM') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $this->msproduct->delete(array(
                'userid' => getCurrentIdUser(),
                'category_apikey' => 'PPOB',
                'subcategory_apikey' => 'PRABAYAR'
            ));

            return JSONResponseDefault('OK', 'Produk berhasil dihapus');
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function bulk_delete_all_pascabayar()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'SMM') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $this->msproduct->delete(array(
                'userid' => getCurrentIdUser(),
                'category_apikey' => 'PPOB',
                'subcategory_apikey' => 'PASCABAYAR'
            ));

            return JSONResponseDefault('OK', 'Produk berhasil dihapus');
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function bulk_delete_all_smm()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'PPOB') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $this->msproduct->delete(array(
                'userid' => getCurrentIdUser(),
                'category_apikey' => 'SMM',
                'subcategory_apikey' => 'SMM'
            ));

            return JSONResponseDefault('OK', 'Produk berhasil dihapus');
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function bulk_add_pascabayar()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'SMM') {
            return redirect(base_url('dashboard'));
        }

        if (getCurrentUser()->multivendor != 1) {
            $vendor = $this->apikeys->select('vendor as vendorid,vendor as name')
                ->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'PPOB',
                ))
                ->get()
                ->result();
        } else {
            $vendor = $this->msvendor->select('id as vendorid,name')
                ->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'isactive' => 1
                ))
                ->get()
                ->result();
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tambah Produk Pascabayar';
        $data['content'] = 'product/pascabayar/bulk_add';
        $data['vendor'] = $vendor;

        return $this->load->view('master', $data);
    }

    public function select_brand_pascabayar()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'SMM') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $vendorid = getPost('vendor');

            if (getCurrentUser()->multivendor == 1) {
                $vendor = $this->msvendor->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'isactive' => 1,
                    'id' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendorid' => $vendorid,
                ));
            } else {
                $vendor = $this->apikeys->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'vendor' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendor' => $vendorid,
                    'a.vendorid' => null
                ));
            }

            $brand = array();
            foreach ($tempproduct as $key => $value) {
                $data = json_decode($value->data);

                foreach ($data as $k => $v) {
                    if (!in_array($v->brand, $brand) && $v->category_apikey == 'PPOB' && $v->subcategory_apikey == 'PASCABAYAR') {
                        $brand[] = $v->brand;
                    }
                }
            }

            sort($brand);

            return JSONResponse(array(
                'RESULT' => 'OK',
                'DATA' => $brand
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function get_data_pascabayar()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'SMM') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $vendorid = getPost('vendor');
            $brand = getPost('brand');

            if (getCurrentUser()->multivendor == 1) {
                $vendor = $this->msvendor->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'isactive' => 1,
                    'id' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $product = $this->msproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendorid' => $vendorid,
                ));

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendorid' => $vendorid,
                ));
            } else {
                $vendor = $this->apikeys->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'vendor' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $product = $this->msproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendor' => $vendorid,
                    'a.vendorid' => null,
                ));

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendor' => $vendorid,
                    'a.vendorid' => null
                ));
            }

            $service = array();
            foreach ($product as $key => $value) {
                $service[] = $value->code;
            }

            $product = array();
            foreach ($tempproduct as $key => $value) {
                $data = json_decode($value->data);

                foreach ($data as $k => $v) {
                    if (!array_key_exists($v->code, $product) && $v->brand == $brand && !in_array($v->code, $service) && $v->category_apikey == 'PPOB' && $v->subcategory_apikey == 'PASCABAYAR') {
                        $product[] = $v;
                    }
                }
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'DATA' => $product
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_bulk_add_pascabayar()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if ($this->user->companycategory == 'SMM') {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $ids = getPost('ids', []);
            $vendorid = getPost('vendor');
            $brand = getPost('brand');

            if (count($ids) == 0) {
                throw new Exception('Produk tidak boleh kosong');
            } else if ($vendorid == null) {
                throw new Exception('Vendor tidak boleh kosong');
            }

            $multivendor = false;
            if (getCurrentUser()->multivendor == 1) {
                $vendor = $this->msvendor->where(array(
                    'createdby' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'isactive' => 1,
                    'id' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendorid' => $vendorid,
                ));

                $multivendor = true;
            } else {
                $vendor = $this->apikeys->where(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'PPOB',
                    'vendor' => $vendorid
                ))->total();

                if ($vendor == 0) {
                    throw new Exception('Vendor tidak ditemukan');
                }

                $tempproduct = $this->mstempproduct->result(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.vendor' => $vendorid,
                    'a.vendorid' => null
                ));
            }

            foreach ($tempproduct as $key => $value) {
                $data = json_decode($value->data);

                foreach ($data as $k => $v) {
                    if (in_array($v->code, $ids) && $v->brand == $brand) {
                        if ($multivendor) {
                            $product = $this->msproduct->total(array(
                                'code' => $v->code,
                                'userid' => getCurrentIdUser(),
                                'category' => 'PASCABAYAR',
                                'vendorid' => $vendorid
                            ));
                        } else {
                            $product = $this->msproduct->total(array(
                                'code' => $v->code,
                                'userid' => getCurrentIdUser(),
                                'category' => 'PASCABAYAR',
                                'vendor' => $vendorid
                            ));
                        }

                        if ($product > 0) continue;

                        // convert v to array
                        $v = (array)$v;

                        $this->msproduct->insert($v);
                    }
                }
            }

            return JSONResponseDefault('OK', 'Produk berhasil ditambahkan');
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
