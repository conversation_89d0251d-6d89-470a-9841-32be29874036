<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Digiflazz
{
    private $_head;
    private $_username;
    private $_key;

    public function __construct($username, $key)
    {
        $this->_head = array(
            'Content-Type:application/json'
        );

        $this->_username = $username;
        $this->_key = $key;
    }

    private function _request($url, $head, $data)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $head);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);
        curl_close($ch);

        return $result;
    }

    public function signature($cmd)
    {
        return md5($this->_username . $this->_key . $cmd);
    }

    public function price_list($cmd = 'prepaid')
    {
        $url = "https://api.digiflazz.com/v1/price-list";

        $sign = $this->signature("pricelist");

        $data = json_encode(array(
            'cmd' => $cmd,
            'username' => $this->_username,
            'sign' => $sign
        ));

        $exec = $this->_request($url, $this->_head, $data);

        return json_decode($exec);
    }

    public function check_balance()
    {
        $url = "https://api.digiflazz.com/v1/cek-saldo";

        $sign = $this->signature("depo");

        $data = json_encode(array(
            'cmd' => 'deposit',
            'username' => $this->_username,
            'sign' => $sign
        ));

        $exec = $this->_request($url, $this->_head, $data);

        return json_decode($exec);
    }

    public function topup($buyer_sku_code, $customer_no, $ref_id, $testing = false)
    {
        $url = "https://api.digiflazz.com/v1/transaction";

        $sign = $this->signature($ref_id);

        $data = json_encode(array(
            'username' => $this->_username,
            'buyer_sku_code' => $buyer_sku_code,
            'customer_no' => $customer_no,
            'ref_id' => $ref_id,
            'sign' => $sign,
            'testing' => $testing
        ));

        $exec = $this->_request($url, $this->_head, $data);

        return $exec;
    }

    public function status_pasca($buyer_sku_code, $customer_no, $ref_id)
    {
        $url = "https://api.digiflazz.com/v1/transaction";

        $sign = $this->signature($ref_id);

        $data = json_encode(array(
            'commands' => 'status-pasca',
            'username' => $this->_username,
            'buyer_sku_code' => $buyer_sku_code,
            'customer_no' => $customer_no,
            'ref_id' => $ref_id,
            'sign' => $sign,
            'testing' => false
        ));

        $exec = $this->_request($url, $this->_head, $data);

        return $exec;
    }
}
