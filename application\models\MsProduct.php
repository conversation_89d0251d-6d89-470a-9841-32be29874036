<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsProduct extends MY_Model
{
    protected $table = 'msproduct';
    public $SearchDatatables = array(
        'a.productname'
    );

    public function QueryDatatables()
    {
        $this->db->select('a.type, a.status, a.code, a.productname, a.category, a.brand, a.description, a.vendorprice, a.price, a.profit, a.createddate, a.updateddate, a.vendor, a.id, b.rata2, a.dontupdate, a.iscustom, a.admin, a.commission, a.isstock, a.stock')
            ->from($this->table . ' a')
            ->join("(SELECT a.serviceid, SEC_TO_TIME(TIME_TO_SEC(AVG(TIMEDIFF(a.updateddate, a.createddate)))) AS rata2 FROM trorder a JOIN msproduct b ON b.id = a.serviceid WHERE LOWER(a.status) IN ('success', 'sukses', 'completed') GROUP BY a.serviceid) b", 'b.serviceid = a.id', 'LEFT')
            ->order_by('a.category, a.brand');

        return $this;
    }

    public function QueryDatatablesPrefixOperator()
    {
        $this->db->select('a.id, a.brand as brandname, b.prefix')
            ->from($this->table . ' a')
            ->join('msprefixoperator b', 'b.operatorname = a.brand AND b.userid = a.userid', 'LEFT')
            ->where('a.brand IS NOT NULL')
            ->group_by('a.brand')
            ->order_by('a.brand', 'ASC');

        return $this;
    }
}
