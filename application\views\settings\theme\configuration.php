<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Konfigurasi Tema: AdminLTE 2 (Default)</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmConfiguration" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <input type="hidden" name="id" value="<?= $id ?>">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">
                        Nama Singkatan Usaha (Depan)

                        <a href="javascript:;" data-bs-toggle="popover" data-bs-placement="right" title="Nama Singkatan Usaha (Depan)" data-bs-html="true" data-bs-content="<div>
<p class='m-0'>Teks ini ditampilkan pada saat ukuran layar mobile atau pada saat navbar ditutup.</p>
<p class='mb-2'>Contoh gambar:</p>
<img src='<?= base_url('assets/media/tutorial/adminlte2/singkatan-depan.png') ?>' class='w-100'>
</div>">
                            <i class="far fa-question-circle"></i>
                        </a>
                    </label>

                    <input type="text" name="companyfirstabbr" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Singkatan Usaha (Depan)" value="<?= $config != null ? $config->companyfirstabbr : null ?>" required>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">
                        Nama Singkatan Usaha (Belakang)

                        <a href="javascript:;" data-bs-toggle="popover" data-bs-placement="right" title="Nama Singkatan Usaha (Belakang)" data-bs-html="true" data-bs-content="<div>
<p class='m-0'>Teks ini ditampilkan pada saat ukuran layar mobile atau pada saat navbar ditutup.</p>
<p class='mb-2'>Contoh gambar:</p>
<img src='<?= base_url('assets/media/tutorial/adminlte2/singkatan-belakang.png') ?>' class='w-100'>
</div>">
                            <i class="far fa-question-circle"></i>
                        </a>
                    </label>

                    <input type="text" name="companylastabbr" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Singkatan Usaha (Belakang)" value="<?= $config != null ? $config->companylastabbr : null ?>" required>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">
                        Nama Usaha (Depan)

                        <a href="javascript:;" data-bs-toggle="popover" data-bs-placement="right" title="Nama Usaha (Depan)" data-bs-html="true" data-bs-content="<div>
<p class='m-0'>Teks ini ditampilkan pada saat halaman dashboard diakses menggunakan layar yang lebar atau pada saat navbar dibuka.</p>
<p class='mb-2'>Contoh gambar:</p>
<img src='<?= base_url('assets/media/tutorial/adminlte2/usaha-depan.png') ?>' class='w-100'>
</div>">
                            <i class="far fa-question-circle"></i>
                        </a>
                    </label>

                    <input type="text" name="companyfirst" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Usaha (Depan)" value="<?= $config != null ? $config->companyfirst : null ?>" required>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">
                        Nama Usaha (Belakang)

                        <a href="javascript:;" data-bs-toggle="popover" data-bs-placement="right" title="Nama Usaha (Belakang)" data-bs-html="true" data-bs-content="<div>
<p class='m-0'>Teks ini ditampilkan pada saat halaman dashboard diakses menggunakan layar yang lebar atau pada saat navbar dibuka.</p>
<p class='mb-2'>Contoh gambar:</p>
<img src='<?= base_url('assets/media/tutorial/adminlte2/usaha-belakang.png') ?>' class='w-100'>
</div>">
                            <i class="far fa-question-circle"></i>
                        </a>
                    </label>
                    <input type="text" name="companylast" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Usaha (Belakang)" value="<?= $config != null ? $config->companylast : null ?>" required>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Warna Tema</label>

                    <!--begin::Row-->
                    <div class="row">
                        <!--begin::Col-->
                        <div class="col-4">
                            <label class="form-check-clip text-center w-100">
                                <input class="btn-check" type="radio" value="blue" <?= $config != null ? ($config->themecolor == 'blue' ? 'checked' : null) : 'checked' ?> name="themecolor" />

                                <div class="form-check-wrapper">
                                    <div class="form-check-indicator"></div>

                                    <div>
                                        <div style="display: block; box-shadow: 0 0 3px rgba(0,0,0,0.4)" class="clearfix full-opacity-hover">
                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 7px; background: #367fa9"></span>
                                                <span style="display:block; width: 80%; float: left; height: 7px; background: #3c8dbc;"></span>
                                            </div>

                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 50px; background: #222d32"></span>
                                                <span style="display:block; width: 80%; float: left; height: 50px; background: #f4f5f7"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check-label">
                                    <small>Blue</small>
                                </div>
                            </label>
                        </div>
                        <!--end::Col-->

                        <!--begin::Col-->
                        <div class="col-4">
                            <label class="form-check-clip text-center w-100">
                                <input class="btn-check" type="radio" value="black" <?= $config != null ? ($config->themecolor == 'black' ? 'checked' : null) : null ?> name="themecolor" />

                                <div class="form-check-wrapper">
                                    <div class="form-check-indicator"></div>

                                    <div>
                                        <div style="display: block; box-shadow: 0 0 3px rgba(0,0,0,0.4)" class="clearfix full-opacity-hover">
                                            <div style="box-shadow: 0 0 2px rgba(0,0,0,0.1)" class="clearfix">
                                                <span style="display:block; width: 20%; float: left; height: 7px; background: #fefefe"></span>
                                                <span style="display:block; width: 80%; float: left; height: 7px; background: #fefefe"></span>
                                            </div>

                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 50px; background: #222"></span>
                                                <span style="display:block; width: 80%; float: left; height: 50px; background: #f4f5f7"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check-label">
                                    <small>Black</small>
                                </div>
                            </label>
                        </div>
                        <!--end::Col-->

                        <!--begin::Col-->
                        <div class="col-4">
                            <label class="form-check-clip text-center w-100">
                                <input class="btn-check" type="radio" value="purple" <?= $config != null ? ($config->themecolor == 'purple' ? 'checked' : null) : null ?> name="themecolor" />

                                <div class="form-check-wrapper">
                                    <div class="form-check-indicator"></div>

                                    <div>
                                        <div style="display: block; box-shadow: 0 0 3px rgba(0,0,0,0.4)" class="clearfix full-opacity-hover">
                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 7px; background: #555299;"></span>
                                                <span style="display:block; width: 80%; float: left; height: 7px; background: #605ca8;"></span>
                                            </div>

                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 50px; background: #222d32"></span>
                                                <span style="display:block; width: 80%; float: left; height: 50px; background: #f4f5f7"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check-label">
                                    <small>Purple</small>
                                </div>
                            </label>
                        </div>
                        <!--end::Col-->

                        <!--begin::Col-->
                        <div class="col-4">
                            <label class="form-check-clip text-center w-100">
                                <input class="btn-check" type="radio" value="green" <?= $config != null ? ($config->themecolor == 'green' ? 'checked' : null) : null ?> name="themecolor" />

                                <div class="form-check-wrapper">
                                    <div class="form-check-indicator"></div>

                                    <div>
                                        <div style="display: block; box-shadow: 0 0 3px rgba(0,0,0,0.4)" class="clearfix full-opacity-hover">
                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 7px; background: #008d4c;"></span>
                                                <span style="display:block; width: 80%; float: left; height: 7px; background: #00a65a;"></span>
                                            </div>

                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 50px; background: #222d32"></span>
                                                <span style="display:block; width: 80%; float: left; height: 50px; background: #f4f5f7"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check-label">
                                    <small>Green</small>
                                </div>
                            </label>
                        </div>
                        <!--end::Col-->

                        <!--begin::Col-->
                        <div class="col-4">
                            <label class="form-check-clip text-center w-100">
                                <input class="btn-check" type="radio" value="red" <?= $config != null ? ($config->themecolor == 'red' ? 'checked' : null) : null ?> name="themecolor" />

                                <div class="form-check-wrapper">
                                    <div class="form-check-indicator"></div>

                                    <div>
                                        <div style="display: block; box-shadow: 0 0 3px rgba(0,0,0,0.4)" class="clearfix full-opacity-hover">
                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 7px; background: #d33724;"></span>
                                                <span style="display:block; width: 80%; float: left; height: 7px; background: #dd4b39;"></span>
                                            </div>

                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 50px; background: #222d32"></span>
                                                <span style="display:block; width: 80%; float: left; height: 50px; background: #f4f5f7"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check-label">
                                    <small>Red</small>
                                </div>
                            </label>
                        </div>
                        <!--end::Col-->

                        <!--begin::Col-->
                        <div class="col-4">
                            <label class="form-check-clip text-center w-100">
                                <input class="btn-check" type="radio" value="yellow" <?= $config != null ? ($config->themecolor == 'yellow' ? 'checked' : null) : null ?> name="themecolor" />

                                <div class="form-check-wrapper">
                                    <div class="form-check-indicator"></div>

                                    <div>
                                        <div style="display: block; box-shadow: 0 0 3px rgba(0,0,0,0.4)" class="clearfix full-opacity-hover">
                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 7px; background: #db8b0b;"></span>
                                                <span style="display:block; width: 80%; float: left; height: 7px; background: #f39c12;"></span>
                                            </div>

                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 50px; background: #222d32"></span>
                                                <span style="display:block; width: 80%; float: left; height: 50px; background: #f4f5f7"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check-label">
                                    <small>Yellow</small>
                                </div>
                            </label>
                        </div>
                        <!--end::Col-->

                        <!--begin::Col-->
                        <div class="col-4">
                            <label class="form-check-clip text-center w-100">
                                <input class="btn-check" type="radio" value="bluelight" <?= $config != null ? ($config->themecolor == 'bluelight' ? 'checked' : null) : null ?> name="themecolor" />

                                <div class="form-check-wrapper">
                                    <div class="form-check-indicator"></div>

                                    <div>
                                        <div style="display: block; box-shadow: 0 0 3px rgba(0,0,0,0.4)" class="clearfix full-opacity-hover">
                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 7px; background: #367fa9;"></span>
                                                <span style="display:block; width: 80%; float: left; height: 7px; background: #3c8dbc;"></span>
                                            </div>

                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 50px; background: #f9fafc"></span>
                                                <span style="display:block; width: 80%; float: left; height: 50px; background: #f4f5f7"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check-label">
                                    <small>Blue Light</small>
                                </div>
                            </label>
                        </div>
                        <!--end::Col-->

                        <!--begin::Col-->
                        <div class="col-4">
                            <label class="form-check-clip text-center w-100">
                                <input class="btn-check" type="radio" value="blacklight" <?= $config != null ? ($config->themecolor == 'blacklight' ? 'checked' : null) : null ?> name="themecolor" />

                                <div class="form-check-wrapper">
                                    <div class="form-check-indicator"></div>

                                    <div>
                                        <div style="display: block; box-shadow: 0 0 3px rgba(0,0,0,0.4)" class="clearfix full-opacity-hover">
                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 7px; background: #fefefe;"></span>
                                                <span style="display:block; width: 80%; float: left; height: 7px; background: #fefefe;"></span>
                                            </div>

                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 50px; background: #f9fafc"></span>
                                                <span style="display:block; width: 80%; float: left; height: 50px; background: #f4f5f7"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check-label">
                                    <small>Black Light</small>
                                </div>
                            </label>
                        </div>
                        <!--end::Col-->

                        <!--begin::Col-->
                        <div class="col-4">
                            <label class="form-check-clip text-center w-100">
                                <input class="btn-check" type="radio" value="purplelight" <?= $config != null ? ($config->themecolor == 'purplelight' ? 'checked' : null) : null ?> name="themecolor" />

                                <div class="form-check-wrapper">
                                    <div class="form-check-indicator"></div>

                                    <div>
                                        <div style="display: block; box-shadow: 0 0 3px rgba(0,0,0,0.4)" class="clearfix full-opacity-hover">
                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 7px; background: #555299;"></span>
                                                <span style="display:block; width: 80%; float: left; height: 7px; background: #605ca8;"></span>
                                            </div>

                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 50px; background: #f9fafc"></span>
                                                <span style="display:block; width: 80%; float: left; height: 50px; background: #f4f5f7"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check-label">
                                    <small>Purple Light</small>
                                </div>
                            </label>
                        </div>
                        <!--end::Col-->

                        <!--begin::Col-->
                        <div class="col-4">
                            <label class="form-check-clip text-center w-100">
                                <input class="btn-check" type="radio" value="greenlight" <?= $config != null ? ($config->themecolor == 'greenlight' ? 'checked' : null) : null ?> name="themecolor" />

                                <div class="form-check-wrapper">
                                    <div class="form-check-indicator"></div>

                                    <div>
                                        <div style="display: block; box-shadow: 0 0 3px rgba(0,0,0,0.4)" class="clearfix full-opacity-hover">
                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 7px; background: #008d4c;"></span>
                                                <span style="display:block; width: 80%; float: left; height: 7px; background: #00a65a;"></span>
                                            </div>

                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 50px; background: #f9fafc"></span>
                                                <span style="display:block; width: 80%; float: left; height: 50px; background: #f4f5f7"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check-label">
                                    <small>Green Light</small>
                                </div>
                            </label>
                        </div>
                        <!--end::Col-->

                        <!--begin::Col-->
                        <div class="col-4">
                            <label class="form-check-clip text-center w-100">
                                <input class="btn-check" type="radio" value="redlight" <?= $config != null ? ($config->themecolor == 'redlight' ? 'checked' : null) : null ?> name="themecolor" />

                                <div class="form-check-wrapper">
                                    <div class="form-check-indicator"></div>

                                    <div>
                                        <div style="display: block; box-shadow: 0 0 3px rgba(0,0,0,0.4)" class="clearfix full-opacity-hover">
                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 7px; background: #d33724;"></span>
                                                <span style="display:block; width: 80%; float: left; height: 7px; background: #dd4b39;"></span>
                                            </div>

                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 50px; background: #f9fafc"></span>
                                                <span style="display:block; width: 80%; float: left; height: 50px; background: #f4f5f7"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check-label">
                                    <small>Red Light</small>
                                </div>
                            </label>
                        </div>
                        <!--end::Col-->

                        <!--begin::Col-->
                        <div class="col-4">
                            <label class="form-check-clip text-center w-100">
                                <input class="btn-check" type="radio" value="yellowlight" <?= $config != null ? ($config->themecolor == 'yellowlight' ? 'checked' : null) : null ?> name="themecolor" />

                                <div class="form-check-wrapper">
                                    <div class="form-check-indicator"></div>

                                    <div>
                                        <div style="display: block; box-shadow: 0 0 3px rgba(0,0,0,0.4)" class="clearfix full-opacity-hover">
                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 7px; background: #db8b0b;"></span>
                                                <span style="display:block; width: 80%; float: left; height: 7px; background: #f39c12;"></span>
                                            </div>

                                            <div>
                                                <span style="display:block; width: 20%; float: left; height: 50px; background: #f9fafc"></span>
                                                <span style="display:block; width: 80%; float: left; height: 50px; background: #f4f5f7"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check-label">
                                    <small>Yellow Light</small>
                                </div>
                            </label>
                        </div>
                        <!--end::Col-->
                    </div>
                    <!--end::Row-->
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    KTApp.init();

    $.AjaxRequest('#frmConfiguration', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success'
                }).then(function(result) {
                    return window.location.reload();

                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.RESULT,
                    icon: 'error'
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        }
    });
</script>