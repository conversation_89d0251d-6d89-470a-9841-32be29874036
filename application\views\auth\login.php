<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!-- 
   _____                              ____  ____  ____  ____     ___        _____ __  _____  ___
  / ___/___  ______   _____  _____   / __ \/ __ \/ __ \/ __ )   ( _ )      / ___//  |/  /  |/  /
  \__ \/ _ \/ ___/ | / / _ \/ ___/  / /_/ / /_/ / / / / __  |  / __ \/|    \__ \/ /|_/ / /|_/ / 
 ___/ /  __/ /   | |/ /  __/ /     / ____/ ____/ /_/ / /_/ /  / /_/  <    ___/ / /  / / /  / /  
/____/\___/_/    |___/\___/_/     /_/   /_/    \____/_____/   \____/\/   /____/_/  /_/_/  /_/   
                                                                                                
                        By PT. KARPEL DEVELOPER TEKNOLOGI INDONESIA
-->
<!DOCTYPE html>

<html lang="en">
<!--begin::Head-->

<head>
    <title>Server PPOB & SMM</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="Server PPOB & SMM adalah sebuah platform digital yang menyediakan jasa penyedia layanan PPOB & SMM. Yang dimana setiap member yang terdaftar dapat melakukan konfigurasi nama usahanya sendiri.">
    <meta name="keywords" content="server ppob, server smm, server ppob & smm, server ppob h2h, server smm h2h, server ppob customize, server smm customize, ppob, smm, jasa pembuatan website ppob, pembuatan website ppob, website ppob, jasa pembuatan website smm, pembuatan website smm, website smm, sewa panel, sewa panel ppob, sewa panel smm, sewa ppob, sewa smm">
    <meta name="author" content="Server PPOB & SMM">
    <meta name="image" content="<?= base_url() ?>siteimage.png">

    <!-- Schema.org for Google -->
    <meta itemprop="name" content="Server PPOB & SMM - Buat Bisnis PPOB & SMM Secara Instan">
    <meta itemprop="description" content="Server PPOB & SMM adalah sebuah platform digital yang menyediakan jasa penyedia layanan PPOB & SMM. Yang dimana setiap member yang terdaftar dapat melakukan konfigurasi nama usahanya sendiri.">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Server PPOB & SMM - Buat Bisnis PPOB & SMM Secara Instan">
    <meta name="twitter:description" content="Server PPOB & SMM adalah sebuah platform digital yang menyediakan jasa penyedia layanan PPOB & SMM. Yang dimana setiap member yang terdaftar dapat melakukan konfigurasi nama usahanya sendiri.">

    <!-- Open Graph general (Facebook, Pinterest & Google+) -->
    <meta name="og:title" content="Server PPOB & SMM - Buat Bisnis PPOB & SMM Secara Instan">
    <meta name="og:description" content="Server PPOB & SMM adalah sebuah platform digital yang menyediakan jasa penyedia layanan PPOB & SMM. Yang dimana setiap member yang terdaftar dapat melakukan konfigurasi nama usahanya sendiri.">
    <meta name="og:url" content="https://server-ppobsmm.com/">
    <meta name="og:site_name" content="Server PPOB & SMM - Buat Bisnis PPOB & SMM Secara Instan">
    <meta name="og:type" content="website">

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "LocalBusiness",
            "name": "Server PPOB & SMM",
            "image": "https://server-ppobsmm.com/siteimage.png",
            "@id": "",
            "url": "https://server-ppobsmm.com/",
            "telephone": "085885263097",
            "priceRange": "150000",
            "address": {
                "@type": "PostalAddress",
                "streetAddress": "Jl. Karang Ampel Dusun Karang Widoro Kec. Dau",
                "addressLocality": "Malang",
                "postalCode": "65151",
                "addressCountry": "ID"
            },
            "openingHoursSpecification": {
                "@type": "OpeningHoursSpecification",
                "dayOfWeek": [
                    "Monday",
                    "Tuesday",
                    "Wednesday",
                    "Thursday",
                    "Friday",
                    "Saturday",
                    "Sunday"
                ],
                "opens": "00:00",
                "closes": "23:59"
            },
            "sameAs": [
                "https://www.facebook.com/profile.php?id=100087117707002",
                "https://www.instagram.com/server.ppobsmm/",
                "https://server-ppobsmm.com/"
            ]
        }
    </script>

    <link rel="shortcut icon" href="<?= base_url() ?>assets/media/logos/favicon.ico" />

    <!--begin::Fonts(mandatory for all pages)-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
    <!--end::Fonts-->

    <!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
    <link href="<?= base_url() ?>assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <link href="<?= base_url() ?>assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <!--end::Global Stylesheets Bundle-->

    <!--begin::Global Javascript Bundle(mandatory for all pages)-->
    <script src="<?= base_url() ?>assets/plugins/global/plugins.bundle.js"></script>
    <script src="<?= base_url() ?>assets/js/scripts.bundle.js"></script>
    <!--end::Global Javascript Bundle-->

    <style>
        .g-recaptcha {
            margin: 15px auto !important;
            width: auto !important;
            height: auto !important;
            text-align: -webkit-center;
            text-align: -moz-center;
            text-align: -o-center;
            text-align: -ms-center;
        }
    </style>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-BZ3VXFC66C"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-BZ3VXFC66C');
    </script>

    <meta name="csrf-name" content="<?= $this->security->get_csrf_token_name() ?>">
    <meta name="csrf-token" content="<?= $this->security->get_csrf_hash() ?>">

    <script>
        $.ajaxSetup({
            data: {
                '<?= $this->security->get_csrf_token_name() ?>': '<?= $this->security->get_csrf_hash() ?>'
            }
        })
    </script>
</head>
<!--end::Head-->

<!--begin::Body-->

<body id="kt_body" class="app-blank bgi-size-cover bgi-position-center bgi-no-repeat">
    <!--begin::Theme mode setup on page load-->
    <script>
        var defaultThemeMode = "light";
        var themeMode;
        if (document.documentElement) {
            if (document.documentElement.hasAttribute("data-theme-mode")) {
                themeMode = document.documentElement.getAttribute("data-theme-mode");
            } else {
                if (localStorage.getItem("data-theme") !== null) {
                    themeMode = localStorage.getItem("data-theme");
                } else {
                    themeMode = defaultThemeMode;
                }
            }
            if (themeMode === "system") {
                themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
            }
            document.documentElement.setAttribute("data-theme", themeMode);
        }
    </script>
    <!--end::Theme mode setup on page load-->

    <!--Begin::Google Tag Manager (noscript) -->
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5FS8GGP" height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>
    <!--End::Google Tag Manager (noscript) -->

    <!--begin::Main-->
    <!--begin::Root-->
    <div class="d-flex flex-column flex-root">
        <!--begin::Page bg image-->
        <style>
            body {
                background-image: url('<?= base_url() ?>assets/media/auth/bg10.jpg');
            }

            [data-theme="dark"] body {
                background-image: url('<?= base_url() ?>assets/media/auth/bg10-dark.jpg');
            }
        </style>
        <!--end::Page bg image-->

        <!--begin::Authentication - Sign-in -->
        <div class="d-flex flex-column flex-lg-row flex-column-fluid">
            <!--begin::Aside-->
            <div class="d-flex flex-lg-row-fluid">
                <!--begin::Content-->
                <div class="d-flex flex-column flex-center pb-0 pb-lg-10 p-10 w-100">
                    <!--begin::Image-->
                    <img class="theme-light-show mx-auto mw-100 w-150px w-lg-300px mb-10 mb-lg-20" src="<?= base_url() ?>assets/media/auth/agency.png" alt="" />
                    <img class="theme-dark-show mx-auto mw-100 w-150px w-lg-300px mb-10 mb-lg-20" src="<?= base_url() ?>assets/media/auth/agency-dark.png" alt="" />
                    <!--end::Image-->

                    <!--begin::Title-->
                    <h1 class="text-gray-800 fs-2qx fw-bold text-center mb-7">Mudah, Cepat dan Efisien</h1>
                    <!--end::Title-->

                    <!--begin::Text-->
                    <div class="text-gray-600 fs-base text-center fw-semibold">
                        Buat website PPOB & SMM dengan mudah, cepat dan efisien menggunakan <span class="text-primary">Server PPOB & SMM</span> kami.<br>Kelola produk dengan berbagai macam pilihan vendor PPOB & SMM yang bisa anda tentukan sendiri!.
                    </div>
                    <!--end::Text-->
                </div>
                <!--end::Content-->
            </div>
            <!--begin::Aside-->

            <!--begin::Aside-->
            <!--begin::Body-->
            <div class="d-flex flex-column-fluid flex-lg-row-auto justify-content-center justify-content-lg-end p-12">
                <!--begin::Wrapper-->
                <div class="bg-body d-flex flex-center rounded-4 w-md-600px p-10">
                    <!--begin::Content-->
                    <div class="w-md-400px">
                        <!--begin::Form-->
                        <form class="form w-100" novalidate="novalidate" id="kt_sign_in_form" action="<?= base_url(uri_string() . '/process') ?>" method="POST">
                            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                            <!--begin::Heading-->
                            <div class="text-center mb-11">
                                <!--begin::Title-->
                                <h1 class="text-dark fw-bolder mb-3">Masuk</h1>
                                <!--end::Title-->

                                <!--begin::Subtitle-->
                                <div class="text-gray-500 fw-semibold fs-6">Formulir Login Akun Server PPOB & SMM</div>
                                <!--end::Subtitle=-->
                            </div>

                            <!--begin::Heading-->

                            <?php if ($success != null) : ?>
                                <!--begin::Alert-->
                                <div class="alert alert-success d-flex align-items-center p-5">
                                    <!--begin::Icon-->
                                    <span class="svg-icon svg-icon-2hx svg-icon-success me-3">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor" />
                                            <path d="M10.4343 12.4343L8.75 10.75C8.33579 10.3358 7.66421 10.3358 7.25 10.75C6.83579 11.1642 6.83579 11.8358 7.25 12.25L10.2929 15.2929C10.6834 15.6834 11.3166 15.6834 11.7071 15.2929L17.25 9.75C17.6642 9.33579 17.6642 8.66421 17.25 8.25C16.8358 7.83579 16.1642 7.83579 15.75 8.25L11.5657 12.4343C11.2533 12.7467 10.7467 12.7467 10.4343 12.4343Z" fill="currentColor" />
                                        </svg>
                                    </span>
                                    <!--end::Icon-->

                                    <!--begin::Wrapper-->
                                    <div class="d-flex flex-column">
                                        <!--begin::Title-->
                                        <h4 class="mb-1 text-success">Berhasil</h4>
                                        <!--end::Title-->

                                        <!--begin::Content-->
                                        <span><?= $success ?></span>
                                        <!--end::Content-->
                                    </div>
                                    <!--end::Wrapper-->
                                </div>
                                <!--end::Alert-->
                            <?php endif; ?>

                            <?php if ($failed != null) : ?>
                                <!--begin::Alert-->
                                <div class="alert alert-danger d-flex align-items-center p-5">
                                    <!--begin::Icon-->
                                    <span class="svg-icon svg-icon-2hx svg-icon-danger me-3">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="5" fill="currentColor" />
                                            <rect x="7" y="15.3137" width="12" height="2" rx="1" transform="rotate(-45 7 15.3137)" fill="currentColor" />
                                            <rect x="8.41422" y="7" width="12" height="2" rx="1" transform="rotate(45 8.41422 7)" fill="currentColor" />
                                        </svg>
                                    </span>
                                    <!--end::Icon-->

                                    <!--begin::Wrapper-->
                                    <div class="d-flex flex-column">
                                        <!--begin::Title-->
                                        <h4 class="mb-1 text-danger">Gagal</h4>
                                        <!--end::Title-->

                                        <!--begin::Content-->
                                        <span><?= $failed ?></span>
                                        <!--end::Content-->
                                    </div>
                                    <!--end::Wrapper-->
                                </div>
                                <!--end::Alert-->
                            <?php endif; ?>

                            <!--begin::Login options-->
                            <div class="g-3 mb-9">
                                <!--begin::Google link=-->
                                <a href="<?= $loginwithgoogle ?>" class="btn btn-flex btn-outline btn-text-gray-700 btn-active-color-primary bg-state-light flex-center text-nowrap w-100">
                                    <img alt="Logo" src="<?= base_url() ?>assets/media/svg/brand-logos/google-icon.svg" class="h-15px me-3" />Login Menggunakan akun Google</a>
                                <!--end::Google link=-->
                            </div>
                            <!--end::Login options-->

                            <!--begin::Separator-->
                            <div class="separator separator-content my-14">
                                <span class="w-125px text-gray-500 fw-semibold fs-7">Atau dengan</span>
                            </div>
                            <!--end::Separator-->

                            <!--begin::Input group=-->
                            <div class="fv-row mb-8">
                                <!--begin::Email-->
                                <input type="text" placeholder="Alamat Email" name="email" autocomplete="off" class="form-control bg-transparent" />
                                <!--end::Email-->
                            </div>
                            <!--end::Input group=-->

                            <div class="fv-row mb-3">
                                <!--begin::Password-->
                                <input type="password" placeholder="Kata Sandi" name="password" autocomplete="off" class="form-control bg-transparent" />
                                <!--end::Password-->
                            </div>
                            <!--end::Input group=-->

                            <!--begin::Wrapper-->
                            <div class="d-flex flex-stack flex-wrap gap-3 fs-base fw-semibold mb-8">
                                <div></div>
                                <!--begin::Link-->
                                <a href="<?= base_url('auth/forgot') ?>" class="link-primary">Lupa kata sandi?</a>
                                <!--end::Link-->
                            </div>
                            <!--end::Wrapper-->

                            <div class="mb-8" style="margin: auto;">
                                <?= get_widget_captcha() ?>
                            </div>

                            <!--begin::Submit button-->
                            <div class="d-grid mb-10">
                                <button type="submit" id="kt_sign_in_submit" class="btn btn-primary">
                                    <!--begin::Indicator label-->
                                    <span class="indicator-label">Masuk</span>
                                    <!--end::Indicator label-->

                                    <!--begin::Indicator progress-->
                                    <span class="indicator-progress">Please wait...
                                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    <!--end::Indicator progress-->
                                </button>
                            </div>
                            <!--end::Submit button-->

                            <!--begin::Sign up-->
                            <div class="text-gray-500 text-center fw-semibold fs-6">Belum memiliki akun?
                                <a href="<?= base_url('auth/register') ?>" class="link-primary">Daftar</a>
                            </div>
                            <!--end::Sign up-->
                        </form>
                        <!--end::Form-->
                    </div>
                    <!--end::Content-->
                </div>
                <!--end::Wrapper-->
            </div>
            <!--end::Body-->
        </div>
        <!--end::Authentication - Sign-in-->
    </div>
    <!--end::Root-->
    <!--end::Main-->

    <!--begin::Javascript-->
    <!--begin::Custom Javascript(used for this page only)-->
    <script src="<?= base_url() ?>assets/js/custom/authentication/sign-in/general.js?ver=1.0.1"></script>
    <!--end::Custom Javascript-->

    <?= get_script_captcha() ?>
    <!--end::Javascript-->
</body>
<!--end::Body-->

</html>