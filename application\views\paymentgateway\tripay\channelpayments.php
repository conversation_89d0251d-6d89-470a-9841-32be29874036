<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Channel Pembayaran</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmChannelPayments" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <table class="table table-striped table-row-bordered gy-5 datatables-enabled-payments">
                    <thead>
                        <tr>
                            <th></th>
                            <th>Nama Pembayaran</th>
                            <th>Alias</th>
                        </tr>
                    </thead>

                    <tbody>
                        <?php foreach (getTripayPayments() as $key => $value) : ?>
                            <tr>
                                <td>
                                    <input class="form-check-input" type="checkbox" value="<?= $key ?>" id="<?= $key ?>" name="channelpayments[]" <?= $channel_payments != null ? (in_array($key, $channel_payments) ? 'checked' : null) : null ?>>
                                </td>
                                <td><?= $value ?></td>
                                <td>
                                    <input type="hidden" name="keypayments[]" value="<?= $key ?>">
                                    <input type="hidden" name="valuepayments[]" value="<?= $value ?>">
                                    <input type="text" class="form-control" name="aliaspayments[]" value="<?= $alias_payments[array_keys($channel_payments, $key)[0] ?? ""] ?? $value ?>">
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    $.AjaxRequest('#frmChannelPayments', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success',
                }).then(function(result) {
                    return window.location.reload();

                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.MESSAGE,
                    icon: 'error',
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        }
    });

    $('.datatables-enabled-payments').DataTable({
        "lengthChange": false,
        "lengthMenu": [
            [10, 25, 50, 100, -1],
            [10, 25, 50, 100, "All"]
        ],
        "pageLength": -1,
        "paging": false,
        "searching": false,
        "ordering": false,
    });
</script>