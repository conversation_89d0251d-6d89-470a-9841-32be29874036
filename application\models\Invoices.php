<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Invoices extends MY_Model
{
    protected $table = 'invoice';
    public $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.*, b.name')
            ->from($this->table . ' a')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->order_by('a.createddate', 'DESC');

        return $this->db;
    }
}
