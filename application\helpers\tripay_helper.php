<?php
defined('BASEPATH') or die('No direct script access allowed!');

function getTripayPayments()
{
    return array(
        'PERMATAVA' => 'Permata Virtual Account',
        'BNIVA' => 'BNI Virtual Account',
        'BRIVA' => 'BRI Virtual Account',
        'MANDIRIVA' => 'Mandiri Virtual Account',
        'MUAM<PERSON><PERSON>VA' => 'Muamalat Virtual Account',
        'CIMBVA' => 'CIMB Niaga Virtual Account',
        'BSIVA' => 'BSI Virtual Account',
        'OCBCVA' => 'OCBC NISP Virtual Account',
        'DANAMONVA' => 'Danamon Virtual Account',
        'OTHERBANKVA' => 'Other Bank Virtual Account',
        'ALFAMART' => 'Alfamart',
        'INDOMARET' => 'Indomaret',
        'ALFAMIDI' => 'Alfamidi',
        'OVO' => 'OVO',
        'QRIS' => 'QRIS by ShopeePay',
        'QRISC' => 'QRIS (Customizable)',
        'QRIS2' => 'QRIS',
        'DANA' => 'DANA',
        'SHOPEEPAY' => 'SHOPEEPAY',
        'QRIS_SHOPEEPAY' => 'QRIS Custom by ShopeePay',
    );
}

class Tripay
{
    private $_merchantcode;
    private $_apikey;
    private $_privatekey;
    private $_production;

    public function __construct($merchantcode, $apikey, $privatekey, $production = true)
    {
        $this->_merchantcode = $merchantcode;
        $this->_apikey = $apikey;
        $this->_privatekey = $privatekey;
        $this->_production = $production;
    }

    public function createSignature($merchantref, $amount)
    {
        $signature = hash_hmac('sha256', $this->_merchantcode . $merchantref . $amount, $this->_privatekey);
        return $signature;
    }

    public function requestTransaction($method, $merchantref, $amount, $customername, $customeremail, $customerphone, $productname, $qty, $return_url)
    {
        if ($this->_production) {
            $url = "https://tripay.co.id/api/transaction/create";
        } else {
            $url = "https://tripay.co.id/api-sandbox/transaction/create";
        }

        $data = array(
            'method' => $method,
            'merchant_ref' => $merchantref,
            'amount' => $amount,
            'customer_name' => $customername,
            'customer_email' => $customeremail,
            'customer_phone' => $customerphone,
            'order_items' => array(
                [
                    'sku' => $merchantref,
                    'name' => $productname,
                    'price' => $amount,
                    'quantity' => $qty
                ]
            ),
            'return_url' => $return_url,
            'signature' => $this->createSignature($merchantref, $amount)
        );

        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_FRESH_CONNECT => true,
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HEADER => false,
            CURLOPT_HTTPHEADER => ['Authorization: Bearer ' . $this->_apikey],
            CURLOPT_FAILONERROR => false,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($data),
            CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4
        ]);

        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response);
    }

    public function detailTransaction($reference)
    {
        if ($this->_production) {
            $url = "https://tripay.co.id/api/transaction/detail";
        } else {
            $url = "https://tripay.co.id/api-sandbox/transaction/detail";
        }

        $payload = [
            'reference' => $reference
        ];

        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_FRESH_CONNECT => true,
            CURLOPT_URL => $url . '?' . http_build_query($payload),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HEADER => false,
            CURLOPT_HTTPHEADER => ['Authorization: Bearer ' . $this->_apikey],
            CURLOPT_FAILONERROR => false,
            CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4
        ]);

        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response);
    }
}
