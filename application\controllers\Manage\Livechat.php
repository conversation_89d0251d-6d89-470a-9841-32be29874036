<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsLivechat $mslivechat
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver|CI_DB_query_builder $db
 */
class Livechat extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsLivechat', 'mslivechat');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Live Chat';
        $data['content'] = 'manage/livechat/index';
        $data['livechat'] = $this->mslivechat->get(array(
            'createdby' => getCurrentIdUser()
        ))->row();

        return $this->load->view('master', $data);
    }

    public function process_edit_livechat()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $plugin = getPost('plugin');
            $cdnlink = getPost('cdnlink');

            if ($plugin == null) {
                throw new Exception('Plugin tidak boleh kosong!');
            } else if ($cdnlink == null) {
                throw new Exception('CDN Link tidak boleh kosong!');
            } else {
                if ($plugin != 'Tawk.to') {
                    throw new Exception('Plugin tidak ditemukan!');
                } elseif (filter_var($cdnlink, FILTER_VALIDATE_URL) === FALSE) {
                    throw new Exception('CDN Link tidak valid!');
                }
            }

            $get = $this->mslivechat->total(array(
                'createdby' => getCurrentIdUser()
            ));

            $execute = array();
            $execute['plugin'] = $plugin;
            $execute['cdnlink'] = $cdnlink;

            if ($get == 0) {
                $execute['createddate'] = getCurrentDate();
                $execute['createdby'] = getCurrentIdUser();

                $this->mslivechat->insert($execute);
            } else {
                $execute['updateddate'] = getCurrentDate();
                $execute['updatedby'] = getCurrentIdUser();

                $this->mslivechat->update(array(
                    'createdby' => getCurrentIdUser()
                ), $execute);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan data!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menyimpan data!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
