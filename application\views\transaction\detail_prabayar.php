<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Transaksi Prabayar</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Transaksi</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Prabayar</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Detail</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <a href="<?= base_url('transaction/prabayar') ?>" class="btn btn-danger fw-bold">Kembali</a>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-6">
            <div class="card mb 5">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="transactiondate" class="col-form-label fw-bold fs-6 pt-0 pb-1">Tanggal Transaksi</label>
                                <p><?= tgl_indo(date('Y-m-d', strtotime($transaction->createddate))) ?> <?= date('H:i:s', strtotime($transaction->createddate)) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="updateddate" class="col-form-label fw-bold fs-6 pt-0 pb-1">Tanggal Update</label>
                                <p><?= tgl_indo(date('Y-m-d', strtotime($transaction->updateddate))) ?> <?= date('H:i:s', strtotime($transaction->updateddate)) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="productvendor" class="col-form-label fw-bold fs-6 pt-0 pb-1">Vendor</label>
                                <p><?= $transaction->vendor_product ?? $transaction->vendor ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="buyername" class="col-form-label fw-bold fs-6 pt-0 pb-1">Pembeli</label>
                                <p><?= $transaction->buyer_name ?? 'Tamu' ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="transactioncode" class="col-form-label fw-bold fs-6 pt-0 pb-1">Kode Transaksi</label>
                                <p><?= $transaction->clientcode ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="refid" class="col-form-label fw-bold fs-6 pt-0 pb-1">Kode Referensi</label>
                                <p><?= $transaction->servercode ?? '-' ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="productcode" class="col-form-label fw-bold fs-6 pt-0 pb-1">Kode Produk</label>
                                <p><?= $transaction->product_code ?? $transaction->productcode ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="productname" class="col-form-label fw-bold fs-6 pt-0 pb-1">Nama Produk</label>
                                <p><?= $transaction->productname ?? $transaction->productname_order ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="target" class="col-form-label fw-bold fs-6 pt-0 pb-1">Tujuan</label>
                                <p><?= $transaction->target ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="paymenttype" class="col-form-label fw-bold fs-6 pt-0 pb-1">Pembayaran</label>
                                <p>
                                    <?php if ($transaction->paymenttype == null || $transaction->paymenttype == 'Saldo'): ?>
                                        Saldo
                                    <?php elseif ($transaction->paymenttype == 'Otomatis'): ?>
                                        <?= $transaction->payment ?> - Otomatis
                                    <?php elseif ($transaction->paymenttype == 'Manual'): ?>
                                        <?= $transaction->payment ?> - Manual
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="pricevendor" class="col-form-label fw-bold fs-6 pt-0 pb-1">Harga Modal</label>
                                <p>Rp <?= IDR($transaction->price - $transaction->profit) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="sellingprice" class="col-form-label fw-bold fs-6 pt-0 pb-1">Harga Jual</label>
                                <p>Rp <?= IDR($transaction->price) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="profit" class="col-form-label fw-bold fs-6 pt-0 pb-1">Profit</label>
                                <p class="text-success">Rp <?= IDR($transaction->profit) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="orderplatform" class="col-form-label fw-bold fs-6 pt-0 pb-1">Pembelian Melalui</label>
                                <p><?= strtoupper($transaction->orderplatform ?? 'WEB') ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="serialnumber" class="col-form-label fw-bold fs-6 pt-0 pb-1">Serial Number</label>
                                <p><?= !empty($transaction->sn) ? $transaction->sn : '-' ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="note" class="col-form-label fw-bold fs-6 pt-0 pb-1">Keterangan</label>
                                <p><?= $transaction->note ?? '-' ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="balancebefore" class="col-form-label fw-bold fs-6 pt-0 pb-1">Saldo Sebelum Transaksi</label>
                                <p>Rp <?= IDR($transaction->currentsaldo) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="balanceafter" class="col-form-label fw-bold fs-6 pt-0 pb-1">Saldo Sesudah Transaksi</label>
                                <p>Rp <?= IDR($transaction->currentsaldo - $transaction->price) ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer text-center fw-bold fs-4 <?= (in_array(strtolower($transaction->status), ['pending', 'waiting'])) ? 'bg-warning text-white' : ((in_array(strtolower($transaction->status), ['success', 'sukses'])) ? 'bg-success text-white' : 'bg-danger text-white') ?>">
                    <?php if (in_array(strtolower($transaction->status), ['pending', 'waiting'])): ?>
                        PENDING
                    <?php elseif (in_array(strtolower($transaction->status), ['success', 'sukses'])): ?>
                        SUKSES
                    <?php else: ?>
                        <?= strtoupper($transaction->status) ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>