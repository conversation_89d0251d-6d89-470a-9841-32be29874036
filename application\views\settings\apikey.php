<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">API Key</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Akun</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">API Key</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <!--begin::Alert-->
            <div class="alert alert-success d-flex align-items-center p-5 mb-3">
                <!--begin::Svg Icon | path: icons/duotune/general/gen048.svg-->
                <span class="svg-icon svg-icon-2hx svg-icon-success me-4">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.3" d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor"></path>
                        <path d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z" fill="currentColor"></path>
                    </svg>
                </span>
                <!--end::Svg Icon-->

                <div class="d-flex flex-column">
                    <h4 class="mb-1 text-success">Data anda akan dienkripsi end-to-end</h4>
                    <span>Anda tidak perlu khawatir dengan keamanan data anda, Karena kami akan mengenkripsi data anda sebelum disimpan.</span>
                </div>
            </div>
            <!--end::Alert-->
        </div>

        <?php if (isUser()) : ?>
            <div class="col-md-12">
                <!--begin::Alert-->
                <div class="alert alert-info d-flex align-items-center p-5 mb-3">
                    <!--begin::Svg Icon | path: icons/duotune/general/gen048.svg-->
                    <span class="svg-icon svg-icon-2hx svg-icon-info me-4">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.3" d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor"></path>
                            <path d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z" fill="currentColor"></path>
                        </svg>
                    </span>
                    <!--end::Svg Icon-->

                    <div class="d-flex flex-column">
                        <h4 class="mb-1 text-info">Informasi Multi-Vendor</h4>
                        <span>Sekarang anda sudah bisa menambahkan Vendor lebih dari 1 dalam Kategori Usaha Anda. Namun, Anda perlu melakukan konfigurasi ulang pada API Key Anda. Silahkan klik <a href="javascript:;" class="fw-bold" onclick="doMultiVendor()">Konfigurasi Ulang</a> untuk melakukan Konfigurasi Ulang, Tampilan API Key akan berubah ke Versi Multi-Vendor</span>
                    </div>
                </div>
                <!--end::Alert-->
            </div>

            <div class="col-md-12">
                <div class="alert alert-warning d-flex align-items-center p-5 mb-10">
                    <!--begin::Svg Icon | path: /var/www/preview.keenthemes.com/keenthemes/metronic/docs/core/html/src/media/icons/duotune/general/gen044.svg-->
                    <span class="svg-icon svg-icon-2hx svg-icon-warning me-4"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor" />
                            <rect x="11" y="14" width="7" height="2" rx="1" transform="rotate(-90 11 14)" fill="currentColor" />
                            <rect x="11" y="17" width="2" height="2" rx="1" transform="rotate(-90 11 17)" fill="currentColor" />
                        </svg>
                    </span>
                    <!--end::Svg Icon-->

                    <div class="d-flex flex-column">
                        <h4 class="mb-1 text-warning">Multi-Vendor Belum Tersedia untuk Pembayaran Pascabayar</h4>
                        <span>Untuk saat ini, Multi-Vendor hanya tersedia untuk Pembayaran Prabayar dan SMM. Pembayaran Pascabayar akan segera tersedia dalam waktu dekat.</span>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if (($this->user->companycategory == 'PPOB & SMM' || $this->user->companycategory == 'PPOB' || $this->user->companycategory == null)) : ?>
            <div class="col-md-6">
                <div class="card mb-5">
                    <div class="card-header">
                        <div class="card-title m-0">
                            <h3 class="m-0 fw-bold">API Key PPOB</h3>
                        </div>
                    </div>

                    <form id="frmPPOB" action="<?= base_url(uri_string() . '/process/ppob') ?>" method="POST" autocomplete="off">
                        <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                        <div class="card-body">
                            <div class="mb-7">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Vendor</label>
                                <select id="vendor_ppob" name="vendor" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                    <?php foreach (listVendor() as $key => $value) : ?>
                                        <?php if ($key == 'PPOB') : ?>
                                            <?php foreach ($value as $k => $v) : ?>
                                                <option value="<?= $k ?>" <?= $ppob != null ? ($ppob->vendor == $k ? 'selected' : null) : null ?>><?= $k ?></option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-7">
                                <label class="col-form-label fw-semibold fs-6 pt-0" id="usercode_ppob"><?= $ppob != null ? ($ppob->vendor == 'VIPayment' ? 'API ID' : 'Username') : 'User Code' ?></label>
                                <input type="text" name="usercode" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan <?= $ppob != null ? ($ppob->vendor == 'VIPayment' ? 'API ID' : 'Username') : 'User Code' ?>" value="<?= $ppob != null ? stringEncryption('decrypt', $ppob->usercode) : null ?>" required>
                            </div>

                            <div class="mb-7">
                                <label class="col-form-label fw-semibold fs-6 pt-0" id="apikey_ppob"><?= $ppob != null ? ($ppob->vendor == 'VIPayment' ? 'API Key' : 'Key') : 'API Key' ?></label>
                                <input type="text" name="apikey" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan API Key" value="<?= $ppob != null ? stringEncryption('decrypt', $ppob->apikey) : null ?>" required>
                            </div>

                            <div class="mb-7 <?= $ppob != null && $ppob->vendor != 'Digiflazz' ? 'd-none' : ($ppob == null ? 'd-none' : null) ?>">
                                <label class="col-form-label fw-semibold fs-6 pt-0" id="secretvalue_ppob">Secret Value (Callback)</label>
                                <input type="text" name="secretvalue" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Secret Value" value="<?= $ppob != null ? stringEncryption('decrypt', $ppob->secretvalue) : null ?>">
                                <small>*Secret value menyesuaikan dengan apa yang anda inputkan pada Digiflazz Webhook,<br>Payload URL isikan: https://websitekamu.com/<b>callback/digiflazz</b></small>
                            </div>

                            <div class="form-check form-check-custom form-check-solid mb-7 <?= $ppob != null && $ppob->vendor != 'Digiflazz' ? 'd-none' : null ?>">
                                <input class="form-check-input" type="checkbox" value="1" id="prioritycallback" name="prioritycallback" <?= $ppob != null && $ppob->priority_callback == 1 ? 'checked' : null ?> />
                                <label class="form-check-label" for="prioritycallback">
                                    Prioritaskan dari Callback (Menghilangkan fungsi pengecekan status transaksi dari Cronjobs)
                                </label>
                            </div>

                            <div class="form-check form-check-custom form-check-solid mb-7">
                                <input class="form-check-input" type="checkbox" value="1" id="dontadd_product_ppob" name="dontadd_product" <?= $ppob != null && $ppob->dontadd_product == 1 ? 'checked' : null ?>>
                                <label class="form-check-label" for="dontadd_product_ppob">
                                    Jangan Tambahkan Produk Baru (Menghilangkan fungsi penambahan produk baru secara otomatis)
                                </label>
                            </div>
                        </div>

                        <div class="card-footer py-6 px-9">
                            <div class="d-flex justify-content-end align-items-center">
                                <button type="submit" class="btn btn-primary">Simpan</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>

        <?php if (($this->user->companycategory == 'PPOB & SMM' || $this->user->companycategory == 'SMM' || $this->user->companycategory == null)) : ?>
            <div class="col-md-6">
                <div class="card mb-5">
                    <div class="card-header">
                        <div class="card-title m-0">
                            <h3 class="m-0 fw-bold">API Key SMM</h3>
                        </div>
                    </div>

                    <form id="frmSMM" action="<?= base_url(uri_string() . '/process/smm') ?>" method="POST" autocomplete="off">
                        <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                        <div class="card-body">
                            <div class="mb-7">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Vendor</label>
                                <select id="vendor_smm" name="vendor" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                    <?php foreach (listVendor() as $key => $value) : ?>
                                        <?php if ($key == 'SMM') : ?>
                                            <?php foreach ($value as $k => $v) : ?>
                                                <option value="<?= $k ?>" <?= $smm != null ? ($smm->vendor == $k ? 'selected' : null) : null ?>><?= $k ?></option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-7 <?= $smm != null ? ($smm->vendor == 'MedanPedia' || $smm->vendor == 'IrvanKede' || $smm->vendor == 'WStore' || $smm->vendor == 'SosmedOnline' || $smm->vendor == 'SosmedOnlineVIP' || $smm->vendor == 'V1Pedia' ? null : 'd-none') : null ?>">
                                <label class="col-form-label fw-semibold fs-6 pt-0" id="usercode_smm"><?= $smm != null && ($smm->vendor == 'MedanPedia' || $smm->vendor == 'IrvanKede' || $smm->vendor == 'WStore' || $smm->vendor == 'SosmedOnline' || $smm->vendor == 'SosmedOnlineVIP' || $smm->vendor == 'V1Pedia') ? 'API ID' : 'User Code' ?></label>
                                <input type="text" name="usercode" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan <?= $smm != null && ($smm->vendor == 'MedanPedia' || $smm->vendor == 'IrvanKede' || $smm->vendor == 'WStore' || $smm->vendor == 'SosmedOnline' || $smm->vendor == 'SosmedOnlineVIP' || $smm->vendor == 'V1Pedia') ? 'API ID' : 'User Code' ?>" value="<?= $smm != null ? stringEncryption('decrypt', $smm->usercode) : null ?>" <?= $smm != null ? ($smm->vendor == 'MedanPedia' || $smm->vendor == 'IrvanKede' || $smm->vendor == 'WStore' || $smm->vendor == 'V1Pedia' ? 'required' : null) : 'required' ?>>
                            </div>

                            <div class="mb-7">
                                <label class="col-form-label fw-semibold fs-6 pt-0" id="apikey_smm">API Key</label>
                                <input type="text" name="apikey" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan API Key" value="<?= $smm != null ? stringEncryption('decrypt', $smm->apikey) : null ?>" required>
                            </div>

                            <div class="mb-7 <?= $smm != null ? ($smm->vendor == 'MedanPedia' || $smm->vendor == 'IrvanKede' || $smm->vendor == 'UNDRCTRL' || $smm->vendor == 'SosmedOnline' || $smm->vendor == 'SosmedOnlineVIP' || $smm->vendor == 'DjuraganSosmed' || $smm->vendor == 'SMMRaja' || $smm->vendor == 'Snow' || $smm->vendor == 'SMMIllusion' ? 'd-none' : null) : 'd-none' ?>">
                                <label class="col-form-label fw-semibold fs-6 pt-0" id="secretkey_smm">Secret Key</label>
                                <input type="text" name="secretkey" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Secret Key" value="<?= $smm != null ? stringEncryption('decrypt', $smm->secretkey) : null ?>" <?= $smm != null ? ($smm->vendor == 'MedanPedia' || $smm->vendor == 'IrvanKede' || $smm->vendor == 'UNDRCTRL' || $smm->vendor == 'SosmedOnline' || $smm->vendor == 'SosmedOnlineVIP' || $smm->vendor == 'DjuraganSosmed' || $smm->vendor == 'SMMRaja' || $smm->vendor == 'Snow' || $smm->vendor == 'SMMIllusion' ? null : 'required') : null ?>>
                            </div>

                            <div class="mb-7 <?= $smm != null ? ($smm->vendor == 'BuzzerPanel' || $smm->vendor == 'MedanPedia' || $smm->vendor == 'IrvanKede' || $smm->vendor == 'DailyPanel' || $smm->vendor == 'WStore' || $smm->vendor == 'UNDRCTRL' || $smm->vendor == 'SosmedOnline' || $smm->vendor == 'SosmedOnlineVIP' || $smm->vendor == 'DjuraganSosmed' || $smm->vendor == 'V1Pedia' ? 'd-none' : null) : 'd-none' ?>">
                                <label class="col-form-label fw-semibold fs-6 pt-0" id="currencyrate_smm">Currency Rate</label>
                                <input type="number" name="currencyrate" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Currency Rate" value="<?= $smm != null ? $smm->currency_rate : null ?>" <?= $smm != null ? ($smm->vendor == 'BuzzerPanel' || $smm->vendor == 'MedanPedia' || $smm->vendor == 'IrvanKede' || $smm->vendor == 'DailyPanel' || $smm->vendor == 'WStore' || $smm->vendor == 'UNDRCTRL' || $smm->vendor == 'SosmedOnline' || $smm->vendor == 'SosmedOnlineVIP' || $smm->vendor == 'DjuraganSosmed' || $smm->vendor == 'V1Pedia' ? null : 'required') : null ?>>
                            </div>

                            <div class="mb-7 <?= $smm != null ? ($smm->vendor != 'UNDRCTRL' ? 'd-none' : null) : 'd-none' ?>">
                                <label class="col-form-label fw-semibold fs-6 pt-0" id="public_key">Public Key</label>
                                <input type="text" name="public_key" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Public Key" value="<?= $smm != null ? stringEncryption('decrypt', $smm->public_key ?? '') : null ?>" <?= $smm != null ? ($smm->vendor != 'UNDRCTRL' ? null : 'required') : null ?>>
                            </div>

                            <div class="form-check form-check-custom form-check-solid mb-7">
                                <input class="form-check-input" type="checkbox" value="1" id="dontadd_product_smm" name="dontadd_product" <?= $smm != null && $smm->dontadd_product == 1 ? 'checked' : null ?>>
                                <label class="form-check-label" for="dontadd_product_smm">
                                    Jangan Tambahkan Produk Baru (Menghilangkan fungsi penambahan produk baru secara otomatis)
                                </label>
                            </div>
                        </div>

                        <div class="card-footer py-6 px-9">
                            <div class="d-flex justify-content-end align-items-center">
                                <button type="submit" class="btn btn-primary">Simpan</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
    window.onload = function() {
        $.AjaxRequest('#frmPPOB', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success',
                    }).then(function(result) {
                        return window.location.reload();

                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            }
        });

        $.AjaxRequest('#frmSMM', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success',
                    }).then(function(result) {
                        return window.location.reload();

                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            }
        });

        $('select#vendor_ppob').change(function() {
            let isapikeyuser = $(this).find(`option[value="${$(this).val()}"]`);

            if ($(this).val() == 'Digiflazz' && isapikeyuser.attr('data-vendor') != 'users') {
                $('#usercode_ppob').html('Username');
                $('#apikey_ppob').html('Key');
                $('#secretvalue_ppob').parent().removeClass('d-none');
                $('#prioritycallback').parent().removeClass('d-none');
            } else if ($(this).val() == 'VIPayment' && isapikeyuser.attr('data-vendor') != 'users') {
                $('#usercode_ppob').html('API ID');
                $('#apikey_ppob').html('API Key');
                $('#secretvalue_ppob').parent().addClass('d-none');
                $('#prioritycallback').parent().addClass('d-none');
            } else if (isapikeyuser.attr('data-vendor') == 'users') {
                $('#usercode_ppob').html('API ID');
                $('#apikey_ppob').html('API Key');
                $('#secretvalue_ppob').parent().addClass('d-none').find('input');
                $('#prioritycallback').parent().addClass('d-none');
            }
        });

        $('select#vendor_smm').change(function() {
            let isapikeyuser = $(this).find(`option[value="${$(this).val()}"]`);

            if (($(this).val() == 'MedanPedia' || $(this).val() == 'IrvanKede' || $(this).val() == 'SosmedOnline' || $(this).val() == 'SosmedOnlineVIP') && isapikeyuser.attr('data-vendor') != 'users') {
                $('#usercode_smm').html('API ID').parent().removeClass('d-none').find('input').attr('required', true);
                $('#apikey_smm').html('API Key');
                $('#secretkey_smm').parent().addClass('d-none').find('input').removeAttr('required');
                $('#currencyrate_smm').parent().addClass('d-none').find('input').removeAttr('required');
            } else if (($(this).val() == 'BuzzerPanel' || $(this).val() == 'DailyPanel') && isapikeyuser.attr('data-vendor') != 'users') {
                $('#usercode_smm').parent().addClass('d-none').find('input').removeAttr('required');
                $('#apikey_smm').html('API Key');
                $('#secretkey_smm').parent().removeClass('d-none').find('input').attr('required', true);
                $('#currencyrate_smm').parent().addClass('d-none').find('input').removeAttr('required');
            } else if (($(this).val() == 'WStore') || ($(this).val() == 'V1Pedia') && isapikeyuser.attr('data-vendor') != 'users') {
                $('#usercode_smm').html('API ID').parent().removeClass('d-none').find('input').attr('required', true);
                $('#secretkey_smm').parent().removeClass('d-none').find('input').attr('required', true);
                $('#currencyrate_smm').parent().addClass('d-none').find('input').removeAttr('required');
            } else if (($(this).val() == 'UNDRCTRL' || $(this).val() == 'DjuraganSosmed') && isapikeyuser.attr('data-vendor') != 'users') {
                $('#usercode_smm').parent().addClass('d-none').find('input').removeAttr('required');
                $('#secretkey_smm').parent().addClass('d-none').find('input').removeAttr('required');
                $('#currencyrate_smm').parent().addClass('d-none').find('input').removeAttr('required');
            } else if ($(this).val() == 'Snow' || $(this).val() == 'SMMRaja' || $(this).val() == 'SMMIllusion' && isapikeyuser.attr('data-vendor') != 'users') {
                $('#usercode_smm').parent().addClass('d-none').find('input').removeAttr('required');
                $('#secretkey_smm').parent().addClass('d-none').find('input').removeAttr('required');
                $('#currencyrate_smm').parent().removeClass('d-none').find('input').attr('required', true);
            } else if (isapikeyuser.attr('data-vendor') == 'users') {
                $('#usercode_smm').html('API ID').parent().removeClass('d-none').find('input').attr('required', true);
                $('#apikey_smm').html('API Key');
                $('#secretkey_smm').parent().addClass('d-none').find('input').removeAttr('required');
                $('#currencyrate_smm').parent().addClass('d-none').find('input').removeAttr('required');
            }

            if ($(this).val() == 'UNDRCTRL') {
                $('#public_key').parent().removeClass('d-none').find('input').attr('required', true);
            } else {
                $('#public_key').parent().addClass('d-none').find('input').removeAttr('required');
            }
        });

        $('select#vendor_ppob').change();
        $('select#vendor_smm').change();
    }

    function doMultiVendor() {
        return Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah anda yakin ingin mengubah tampilan API Key ke Versi Multi-Vendor? Data yang saat ini ada akan dihapus dan tidak bisa dikembalikan lagi.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Ya, Lanjutkan',
            cancelButtonText: 'Batal',
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/set/multivendor') ?>',
                    method: 'POST',
                    dataType: 'json',
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success',
                            }).then(function(result) {
                                return window.location.reload();
                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error',
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error',
                    });
                })
            }
        })
    }
</script>