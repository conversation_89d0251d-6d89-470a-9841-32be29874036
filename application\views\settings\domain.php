<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Domain</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Akun</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Domain</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Domain</h3>
                    </div>
                </div>

                <form id="frmDomain" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Domain</label>
                            <input type="text" name="domain" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Domain" value="<?= $user != null ? $user->domain : null ?>" required>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <?php if ($user->domain != null && $request == 0 && !$validcloudflare) : ?>
                            <button type="button" class="btn btn-warning me-1" onclick="requestNameserver()">Request Nameserver</button>
                        <?php endif; ?>

                        <button type="submit" class="btn btn-primary" <?= $request > 0 ? 'disabled' : null ?>>Simpan</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Informasi Server</h3>
                    </div>
                </div>

                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>Whitelist IP Server</th>
                            <td><?= getCurrentUser()->licenseid == null ? 'Akun free tidak dapat melihat informasi ini' : '***********' ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Nameserver Configuration</h3>
                    </div>
                </div>

                <div class="card-body">
                    <table class="table">
                        <?php if (isset($cloudflare[0]->name_servers)) : ?>
                            <?php foreach ($cloudflare[0]->name_servers as $key => $value) : ?>
                                <tr>
                                    <th>NS <?= $key + 1 ?></th>
                                    <td><?= $value ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>

                        <tr>
                            <th>Pairing Status</th>
                            <td>
                                <?php if ($foundcloudflare && $validcloudflare) : ?>
                                    <span class="badge badge-success">OK</span>
                                <?php else : ?>
                                    <?php if ($request == 1) : ?>
                                        <span class="badge badge-warning">PAIRING PROCESS</span>
                                    <?php else : ?>
                                        <?php if ($validcloudflare) : ?>
                                            <span class="badge badge-warning">WAITING CONFIGURATION</span>
                                        <?php else : ?>
                                            <span class="badge badge-danger">BAD</span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $.AjaxRequest('#frmDomain', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success',
                    }).then(function(result) {
                        return window.location.reload();

                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            }
        });
    }

    function requestNameserver() {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Apakah anda yakin ingin request name server pada domain tersebut?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/request') ?>',
                    method: 'POST',
                    dataType: 'json',
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }
</script>