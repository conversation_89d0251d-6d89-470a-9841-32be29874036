<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-xl">
    <div class="modal-content">
        <div class="modal-body">
            <div class="row">
                <div class="col-md-12">
                    <div class="mb-5">
                        <h3>Detail Keuntungan per Hari</h3>

                        <table class="table table-striped table-row-bordered gy-5 datatables-clientside">
                            <thead>
                                <tr class="fw-semibold fs-6 text-muted">
                                    <th>Tanggal</th>
                                    <th>Omset</th>
                                    <th>Keuntungan</th>
                                </tr>
                            </thead>

                            <tbody>
                                <?php foreach ($profit_date as $key => $value) : ?>
                                    <tr>
                                        <td><?= DateFormat($value->date, 'd F Y') ?></td>
                                        <td>Rp <?= IDR($value->omzet) ?></td>
                                        <td>Rp <?= IDR($value->profit) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-5">
                        <h3>Keuntungan Berdasarkan Kategori Produk</h3>

                        <table class="table table-striped table-row-bordered gy-5 datatables-clientside">
                            <thead>
                                <tr class="fw-semibold fs-6 text-muted">
                                    <th>Kategori Produk</th>
                                    <th>Omset</th>
                                    <th>Keuntungan</th>
                                </tr>
                            </thead>

                            <tbody>
                                <?php foreach ($profit_categoryproduct as $key => $value) : ?>
                                    <tr>
                                        <td><?= $value->category ?></td>
                                        <td>Rp <?= IDR($value->omzet) ?></td>
                                        <td>Rp <?= IDR($value->profit) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-5">
                        <div class="text-center">
                            <h3 class="mb-3">Grafik Keuntungan Berdasarkan Kategori Produk</h3>
                        </div>

                        <canvas id="chart_product"></canvas>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-5">
                        <h3>Keuntungan Berdasarkan Kategori Usaha</h3>

                        <table class="table table-striped table-row-bordered gy-5 datatables-clientside">
                            <thead>
                                <tr class="fw-semibold fs-6 text-muted">
                                    <th>Kategori Usaha</th>
                                    <th>Omset</th>
                                    <th>Keuntungan</th>
                                </tr>
                            </thead>

                            <tbody>
                                <?php foreach ($profit_business as $key => $value) : ?>
                                    <tr>
                                        <td><?= $value->category_apikey ?></td>
                                        <td>Rp <?= IDR($value->omzet) ?></td>
                                        <td>Rp <?= IDR($value->profit) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-5">
                        <div class="text-center">
                            <h3 class="mb-3">Grafik Keuntungan Berdasarkan Kategori Usaha</h3>
                        </div>

                        <canvas id="chart_business" class="mh-400px"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
        </div>
    </div>
</div>

<script>
    $('.datatables-clientside').DataTable({
        responsive: true,
        ordering: false,
    });

    var ctx = document.getElementById('chart_product');
    var ctx2 = document.getElementById('chart_business');

    // Define colors
    var primaryColor = KTUtil.getCssVariableValue('--kt-primary');
    var dangerColor = KTUtil.getCssVariableValue('--kt-danger');
    var successColor = KTUtil.getCssVariableValue('--kt-success');
    var warningColor = KTUtil.getCssVariableValue('--kt-warning');
    var infoColor = KTUtil.getCssVariableValue('--kt-info');

    // Define fonts
    var fontFamily = KTUtil.getCssVariableValue('--bs-font-sans-serif');

    // Chart labels
    var labels = <?= json_encode($profit_categoryproduct_label) ?>;
    var labels2 = <?= json_encode($profit_business_label) ?>;

    // Chart data
    var data = {
        labels: labels,
        datasets: [{
            backgroundColor: <?= json_encode($profit_categoryproduct_color) ?>,
            data: <?= json_encode($profit_categoryproduct_profit) ?>
        }]
    };

    var data2 = {
        labels: labels2,
        datasets: [{
            backgroundColor: [primaryColor, successColor],
            data: <?= json_encode($profit_business_profit) ?>
        }]
    };

    // Chart config
    var config = {
        type: 'pie',
        data: data,
        options: {
            plugins: {
                title: {
                    display: false,
                },
            },
            responsive: true,
        },
        defaults: {
            global: {
                defaultFont: fontFamily
            }
        }
    };
    var config2 = {
        type: 'pie',
        data: data2,
        options: {
            plugins: {
                title: {
                    display: false,
                },
            },
            responsive: true,
        },
        defaults: {
            global: {
                defaultFont: fontFamily
            }
        }
    };

    // Init ChartJS -- for more info, please visit: https://www.chartjs.org/docs/latest/
    var myChart = new Chart(ctx, config);
    var myChart2 = new Chart(ctx2, config2);
</script>