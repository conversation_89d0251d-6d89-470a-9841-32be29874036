<?php
defined('BASEPATH') or die('No direct script access allowed!');

function getMidtransPayments()
{
    return [
        "credit_card" => 'Kartu Kredit',
        "gopay" => 'Gopay',
        "shopeepay" => 'Shopeepay',
        "permata_va" => 'Permata Virtual Account',
        "bca_va" => 'BCA Virtual Account',
        "bni_va" => 'BNI Virtual Account',
        "bri_va" => 'BRI Virtual Account',
        "danamon_online" => 'Danamon Online',
        "mandiri_clickpay" => 'Mandiri KlikPay',
        "cimb_clicks" => 'CIMB Klik',
        "bca_klikbca" => 'KlikBCA',
        "bca_klikpay" => 'BCA KlikPay',
        "bri_epay" => 'BRI E-Pay',
        "xl_tunai" => 'XL Tunai',
        "indosat_dompetku" => 'Indosat Dompetku',
        "kioson" => 'Kioson',
        "Indomaret" => 'Indomart',
        "alfamart" => 'Alfamart',
        "akulaku" => 'Akulaku'
    ];
}

class MidtransHelper
{
    private $_serverkey;
    private $_isproduction;

    public function __construct($serverkey, $isproduction = true)
    {
        $this->_serverkey = $serverkey;
        $this->_isproduction = $isproduction;

        Midtrans\Config::$serverKey = $this->_serverkey;
        Midtrans\Config::$isProduction = $this->_isproduction;
    }

    public function status($transactioncode)
    {
        return (object)Midtrans\Transaction::status($transactioncode);
    }
}
