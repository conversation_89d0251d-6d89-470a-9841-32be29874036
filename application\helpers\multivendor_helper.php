<?php
defined('BASEPATH') or exit('No direct script access allowed');

class DynamicVendor
{
    private $ci;
    private $vendorid;
    private $vendor;
    private $config;
    private $multiplerequest;

    public function __construct($vendorid, $config = array())
    {
        $this->ci = &get_instance();
        $this->vendorid = $vendorid;
        $this->config = $config;

        $this->initialize();
    }

    private function initialize()
    {
        $get = $this->ci->db->get_where('msvendor', array(
            'id' => $this->vendorid
        ));

        if ($get->num_rows() == 0) {
            throw new Exception('Vendor not found');
        }

        $this->vendor = $get->row();
    }

    private function _get_vendor_detail($apitype)
    {
        $get = $this->ci->db->get_where('msvendordetail', array(
            'vendorid' => $this->vendorid,
            'apitype' => $apitype,
        ));

        if ($get->num_rows() == 0) {
            throw new Exception('Profile not found');
        }

        return $get->row();
    }

    private function _get_order($orderid)
    {
        $get = $this->ci->db->get_where('trorder', array(
            'id' => $orderid,
            "(vendorid = $this->vendorid OR vendorid IS NULL) =" => true,
        ));

        if ($get->num_rows() == 0) {
            throw new Exception('Order not found');
        }

        return $get->row();
    }

    private function _get_product($productid)
    {
        $get = $this->ci->db->get_where('msproduct', array(
            'id' => $productid,
            "(vendorid = $this->vendorid OR vendorid IS NULL) =" => true,
        ));

        if ($get->num_rows() == 0) {
            throw new Exception('Product not found');
        }

        return $get->row();
    }

    private function request($config, $order = null)
    {
        $dorequest = true;
        $splitrequestindex = 0;
        $response = array();
        do {
            $multiplerequest = false;
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $config->endpoint);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $config->requestmethod);

            if ($config->requestmethod == 'POST') {
                $requestbody = array();
                $params = json_decode($this->vendor->parameter);

                foreach ($params as $key => $value) {
                    $requestbody[$value] = $this->config[$value] ?? '';
                }

                $detail_params = json_decode($config->detail);

                foreach ($detail_params as $key => $value) {
                    if (strpos($value->extends, 'MAIN-') !== false) {
                        $value_extends = str_replace('MAIN-', '', $value->extends);
                        $requestbody[$value->parameter] = $this->config[$value_extends] ?? '';
                    } else {
                        if ($value->extends == 'Custom') {
                            if (strpos($value->value, '${split}') !== false) {
                                $split = explode('${split}', $value->value);

                                $requestbody[$value->parameter] = $split[$splitrequestindex];

                                if (count($split) != $splitrequestindex + 1) {
                                    $multiplerequest = true;
                                    $splitrequestindex++;
                                }
                            } else {
                                $requestbody[$value->parameter] = $value->value;
                            }
                        } else if ($value->extends == 'Signature') {
                            if ($value->value->encryptiontype == 'md5') {
                                $formula = $value->value->formula;

                                foreach ($params as $k => $v) {
                                    $formula = str_replace('${' . $v . '}', $this->config[$v] ?? '', $formula);
                                }

                                $requestbody[$value->parameter] = md5($formula);
                            }
                        } else {
                            if ($value->extends == 'Service ID') {
                                $product = $this->_get_product($order->serviceid);
                                $requestbody[$value->parameter] = $product->code;
                            } elseif ($value->extends == 'Target') {
                                $requestbody[$value->parameter] = $order->target;
                            } elseif ($value->extends == 'Quantity') {
                                $requestbody[$value->parameter] = $order->qty;
                            } elseif ($value->extends == 'Additional Data') {
                                $requestbody[$value->parameter] = $order->additional;
                            } elseif ($value->extends == 'Transaction ID') {
                                $requestbody[$value->parameter] = $order->clientcode;
                            } elseif ($value->extends == 'Server Transaction ID') {
                                $requestbody[$value->parameter] = $order->servercode;
                            }
                        }
                    }
                }

                curl_setopt($ch, CURLOPT_POST, 1);

                if ($config->contenttype == 'application/x-www-form-urlencoded') {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($requestbody));
                } else {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestbody));
                }
            }

            $headers = array();
            $headers[] = 'Content-Type: ' . $config->contenttype;

            if ($config->headers != null) {
                $_headers = json_decode($config->headers);

                foreach ($_headers as $key => $value) {
                    if (strpos($value->extends, 'MAIN-') !== false) {
                        $value_extends = str_replace('MAIN-', '', $value->extends);
                        $headers[] = $value->header . ': ' . $this->config[$value_extends] ?? '';
                    } else {
                        $headers[] = $value->header . ': ' . $value->value;
                    }
                }
            }

            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

            $result = curl_exec($ch);

            if (curl_errno($ch)) {
                throw new Exception('Error:' . curl_error($ch));
            }

            curl_close($ch);

            $response[] = json_decode($result, true);

            if ($multiplerequest == false) {
                $dorequest = false;
            }
        } while ($dorequest);

        if (count($response) == 1) {
            return $response[0];
        } else {
            $this->multiplerequest = true;

            return $response;
        }
    }

    public function isMultipleRequest()
    {
        return $this->multiplerequest;
    }

    public function profile()
    {
        $profile = $this->_get_vendor_detail('Profile');

        return $this->request($profile);
    }

    public function service()
    {
        $service = $this->_get_vendor_detail('Service');

        return $this->request($service);
    }

    public function status($orderid)
    {
        $status = $this->_get_vendor_detail('Status');
        $order = $this->_get_order($orderid);

        return $this->request($status, $order);
    }

    public function order($orderid)
    {
        $order = $this->_get_vendor_detail('Order');
        $get_order = $this->_get_order($orderid);

        return $this->request($order, $get_order);
    }
}
