<?php
defined('BASEPATH') or die('No direct script access allowed!');

class VIPayment
{
    private $_api_id, $_api_key;

    public function __construct($apiid, $apikey)
    {
        $this->_api_id = $apiid;
        $this->_api_key = $apikey;
    }

    public function services_prepaid()
    {
        $ch = curl_init();

        $sign = md5($this->_api_id . $this->_api_key);

        curl_setopt($ch, CURLOPT_URL, "https://vip-reseller.co.id/api/prepaid");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_api_key&sign=$sign&type=services");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function services_postpaid()
    {
        $ch = curl_init();

        $sign = md5($this->_api_id . $this->_api_key);

        curl_setopt($ch, CURLOPT_URL, "https://vip-reseller.co.id/api/postpaid");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_api_key&sign=$sign&type=services");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function services_game()
    {
        $ch = curl_init();

        $sign = md5($this->_api_id . $this->_api_key);

        curl_setopt($ch, CURLOPT_URL, "https://vip-reseller.co.id/api/game-feature");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_api_key&sign=$sign&type=services");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function status_prepaid($trxid)
    {
        $ch = curl_init();

        $sign = md5($this->_api_id . $this->_api_key);

        curl_setopt($ch, CURLOPT_URL, "https://vip-reseller.co.id/api/prepaid");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_api_key&sign=$sign&type=status&trxid=$trxid");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function status_postpaid($trxid)
    {
        $ch = curl_init();

        $sign = md5($this->_api_id . $this->_api_key);

        curl_setopt($ch, CURLOPT_URL, "https://vip-reseller.co.id/api/postpaid");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_api_key&sign=$sign&type=status&trxid=$trxid");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function status_game($trxid)
    {
        $ch = curl_init();

        $sign = md5($this->_api_id . $this->_api_key);

        curl_setopt($ch, CURLOPT_URL, "https://vip-reseller.co.id/api/game-feature");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_api_key&sign=$sign&type=status&trxid=$trxid");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function profile()
    {
        $ch = curl_init();

        $sign = md5($this->_api_id . $this->_api_key);

        curl_setopt($ch, CURLOPT_URL, "https://vip-reseller.co.id/api/profile");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_api_key&sign=$sign");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function order_prepaid($serviceid, $target)
    {
        $ch = curl_init();

        $sign = md5($this->_api_id . $this->_api_key);

        curl_setopt($ch, CURLOPT_URL, "https://vip-reseller.co.id/api/prepaid");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_api_key&sign=$sign&type=order&service=$serviceid&data_no=$target");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);

        return json_decode($result);
    }

    public function order_game($serviceid, $target, $zoneid)
    {
        $ch = curl_init();

        $sign = md5($this->_api_id . $this->_api_key);

        curl_setopt($ch, CURLOPT_URL, "https://vip-reseller.co.id/api/game-feature");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_api_key&sign=$sign&type=order&service=$serviceid&data_no=$target&data_zone=$zoneid");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);

        return json_decode($result);
    }
}
