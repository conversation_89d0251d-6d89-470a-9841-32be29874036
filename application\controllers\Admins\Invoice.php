<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Datatables $datatables
 * @property Deposits $deposits
 * @property MsUsers $msusers
 * @property MsPaymentGateway $paymentgateway
 * @property MsPaymentMethod $paymentmethod
 * @property FeePaymentGateway $feepaymentgateway
 * @property Invoices $invoices
 * @property CI_DB_mysqli_driver $db
 */
class Invoice extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Deposits', 'deposits');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsPaymentGateway', 'paymentgateway');
        $this->load->model('MsPaymentMethod', 'paymentmethod');
        $this->load->model('FeePaymentGateway', 'feepaymentgateway');
        $this->load->model('Invoices', 'invoices');
    }

    public function history()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tagihan';
        $data['content'] = 'admin/invoice/history';
        $data['users'] = $this->invoices->select('a.userid, b.name')
            ->join('msusers b', 'b.id = a.userid')
            ->where('b.isdeleted', null)
            ->group_by('a.userid')
            ->order_by('b.name', 'ASC')
            ->get()
            ->result();

        $this->load->view('master', $data);
    }

    public function datatables_history()
    {
        try {
            if (isLogin() && isAdmin()) {
                $data = array();
                $date = getPost('date');
                $code = getPost('code');
                $users = getPost('users');
                $status = getPost('status');

                $datatable = $this->datatables->make('Invoices', 'QueryDatatables', 'SearchDatatables');

                $where = array();
                $where['b.merchantid'] = null;

                if ($date != null && isset(explode(' - ', $date)[0]) && isset(explode(' - ', $date)[1])) {
                    $startdate = explode(' - ', $date)[0];
                    $startdate = date('Y-m-d', strtotime($startdate));

                    $enddate = explode(' - ', $date)[1];
                    $enddate = date('Y-m-d', strtotime($enddate));

                    $where['DATE(a.createddate) >='] = $startdate;
                    $where['DATE(a.createddate) <='] = $enddate;
                } else {
                    $where['DATE(a.createddate) >='] = date('Y-m-01');
                    $where['DATE(a.createddate) <='] = date('Y-m-t');
                }

                if ($code != null) {
                    $where['a.invoicecode'] = $code;
                }

                if ($users != null) {
                    $where['a.userid'] = $users;
                }

                if ($status != null) {
                    if ($status == 'Success') {
                        $where["(a.status = 'Paid' OR a.status = 'Success')="] = TRUE;
                    } else {
                        $where['a.status'] = $status;
                    }
                }

                foreach ($datatable->getData($where) as $key => $value) {
                    $detail = array();

                    if ($value->status == 'Pending') {
                        $status = "<span class=\"badge badge-warning\">Menunggu</span>";
                    } else if ($value->status == 'Success' || $value->status == 'Paid') {
                        $status = "<span class=\"badge badge-success\">Berhasil</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger\">Gagal</span>";
                    }

                    $actions = "";

                    if ($value->status == 'Pending') {
                        $actions .= "<a href=\"javascript:;\" class=\"btn btn-success btn-sm mb-1\" onclick=\"confirmPayment('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-check\"></i>
                            <span>Konfirmasi</span>
                        </a>

                        <button type=\"button\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"cancelPayment('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-times\"></i>
                            <span>Cancel</span>
                        </button>";
                    }

                    if ($actions == "") {
                        $actions = "N/A";
                    }

                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = $value->invoicecode;
                    $detail[] = $value->name;
                    $detail[] = $value->description;
                    $detail[] = $value->note;
                    $detail[] = IDR($value->nominal);
                    $detail[] = $status;
                    $detail[] = $actions;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_confirm_topup()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda harus login terlebih dahulu');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->invoices->select('a.*, b.expireddate, b.uplinkid, c.royalty, c.name AS packagename')
                ->join('msusers b', 'b.id = a.targetuserid', 'LEFT')
                ->join('mslicense c', 'c.id = a.packageid')
                ->where(array(
                    'a.id' => $id,
                    'a.status' => 'Pending',
                    'b.merchantid' => null,
                ))
                ->get();

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $today = $row->expireddate == null ? date('Y-m-d H:i:s') : $row->expireddate;
            $expireDate = date('Y-m-d H:i:s', strtotime($today . ' +1 month'));

            $update = array();
            $update['licenseid'] = $row->packageid;
            $update['expireddate'] = $expireDate;

            if ($row->packageid == 2) {
                $update['companycategory'] = 'PPOB';
            } else if ($row->packageid == 3) {
                $update['companycategory'] = 'SMM';
            }

            $this->msusers->update(array(
                'id' => $row->targetuserid
            ), $update);

            $update = array();
            $update['status'] = 'Success';

            $this->invoices->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal melakukan konfirmasi');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil dikonfirmasi');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_cancel_topup()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda harus login terlebih dahulu');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->invoices->join('msusers b', 'b.id = a.userid')
                ->total(array(
                    'a.id' => $id,
                    'a.status' => 'Pending',
                    'b.merchantid' => null,
                ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $update = array();
            $update['status'] = 'Cancel';

            $this->invoices->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal membatalkan permintaan invoices');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Permintaan invoices berhasil dibatalkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
