<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-xl">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Ubah Detail Vendor</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmAddDetail" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <input type="hidden" name="id" value="<?= $detail->id ?>">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h4>Request Headers</h4>
                        <hr>

                        <div>
                            <h6>Informasi Parameter untuk Custom Parameter</h6>
                            <hr>

                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <tr>
                                        <td class="text-primary">${ai_trx_number}</td>
                                        <td>:</td>
                                        <td class="text-success">Mendapatkan jumlah transaksi yang dilakukan oleh target yang sama +1</td>
                                    </tr>

                                    <tr>
                                        <td class="text-primary">${split}</td>
                                        <td>:</td>
                                        <td class="text-success">Memotong string dan mengkonversinya menjadi simbol , (koma) pada saat melakukan request <br> <span class="text-danger">*Perlu diperhatikan setiap item pada koma akan melakukan request ke vendor 1x</span><br><span class="text-danger">*Variabel ini dikhususkan untuk kolom Parameter</span></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">End Point</label>
                                    <input type="text" name="endpoint" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan End Point" value="<?= $detail->endpoint ?>" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Tipe API</label>
                                    <select id="api" name="api" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                        <option value="Profile" <?= $detail->apitype == 'Profile' ? 'selected' : null ?>>Profile</option>
                                        <option value="Service" <?= $detail->apitype == 'Service' ? 'selected' : null ?>>Service</option>
                                        <option value="Order" <?= $detail->apitype == 'Order' ? 'selected' : null ?>>Order</option>
                                        <option value="Status" <?= $detail->apitype == 'Status' ? 'selected' : null ?>>Status</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-12 <?= $detail->apitype != 'Service' ? 'd-none' : null ?>" id="display_autoaddproduct">
                                <div class="form-check form-check-custom form-check-solid mb-7">
                                    <input class="form-check-input" type="checkbox" value="1" id="autoaddproduct" name="autoaddproduct" <?= $detail->autoaddproduct ? 'checked' : null ?> />
                                    <label class="form-check-label" for="autoaddproduct">
                                        Tambahkan Produk Secara Otomatis
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Request Method</label>
                                    <select name="requestmethod" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                        <option value="POST" <?= $detail->requestmethod == 'POST' ? 'selected' : null ?>>POST</option>
                                        <option value="GET" <?= $detail->requestmethod == 'GET' ? 'selected' : null ?>>GET</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Content Type</label>
                                    <select name="contenttype" id="contenttype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                        <option value="application/x-www-form-urlencoded" <?= $detail->contenttype == 'application/x-www-form-urlencoded' ? 'selected' : null ?>>application/x-www-form-urlencoded</option>
                                        <option value="application/json" <?= $detail->contenttype == 'application/json' ? 'selected' : null ?>>application/json</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="headers">
                            <?php if (count(json_decode($detail->headers ?? '[]')) > 0) : ?>
                                <?php foreach (json_decode($detail->headers) as $k => $v) : ?>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-7">
                                                <label class="col-form-label fw-semibold fs-6 pt-0">Headers</label>
                                                <input type="text" name="headers[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Headers" value="<?= $v->header ?>">
                                            </div>
                                        </div>

                                        <div class="col-md-4">
                                            <div class="mb-7">
                                                <label class="col-form-label fw-semibold fs-6 pt-0">Extends</label>
                                                <select name="headers_value[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" onchange="changeHeadersExtends(this)">
                                                    <?php foreach (json_decode($vendor->parameter) as $key => $value) : ?>
                                                        <option value="MAIN-<?= $value ?>" <?= $v->extends == 'MAIN-' . $value ? 'selected' : null ?>>Main: <?= $value ?></option>
                                                    <?php endforeach; ?>
                                                    <option value="Custom" <?= $v->extends == 'Custom' ? 'selected' : null ?>>Custom</option>
                                                </select>
                                            </div>
                                        </div>

                                        <?php if ($k == 0) : ?>
                                            <div class="col-md-2 mt-10">
                                                <button type="button" class="btn btn-primary btn-sm" onclick="addHeaders()">
                                                    <i class="fa fa-plus pe-0"></i>
                                                </button>
                                            </div>
                                        <?php else : ?>
                                            <div class="col-md-2 mt-10">
                                                <button type="button" class="btn btn-danger btn-sm" onclick="removeParameter(this)">
                                                    <i class="fa fa-minus pe-0"></i>
                                                </button>
                                            </div>
                                        <?php endif; ?>

                                        <div class="col-md-12 mb-7 div-headers-custom d-none">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Custom</label>
                                            <input type="text" name="custom_headers[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Custom">
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else : ?>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-7">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Headers</label>
                                            <input type="text" name="headers[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Headers">
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="mb-7">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Extends</label>
                                            <select name="headers_value[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" onchange="changeHeadersExtends(this)">
                                                <?php foreach (json_decode($vendor->parameter) as $key => $value) : ?>
                                                    <option value="MAIN-<?= $value ?>">Main: <?= $value ?></option>
                                                <?php endforeach; ?>
                                                <option value="Custom">Custom</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-2 mt-10">
                                        <button type="button" class="btn btn-primary btn-sm" onclick="addHeaders()">
                                            <i class="fa fa-plus pe-0"></i>
                                        </button>
                                    </div>

                                    <div class="col-md-12 mb-7 div-headers-custom d-none">
                                        <label class="col-form-label fw-semibold fs-6 pt-0">Custom</label>
                                        <input type="text" name="custom_headers[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Custom">
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div id="parameter">
                            <?php if (count(json_decode($detail->detail ?? '[]')) > 0) : ?>
                                <?php foreach (json_decode($detail->detail) as $k => $v) : ?>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-7">
                                                <label class="col-form-label fw-semibold fs-6 pt-0">Parameter</label>
                                                <input type="text" name="parameter[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Parameter" value="<?= $v->parameter ?>" required>
                                            </div>
                                        </div>

                                        <div class="col-md-4">
                                            <div class="mb-7">
                                                <label class="col-form-label fw-semibold fs-6 pt-0">Extends</label>
                                                <select name="extends[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" onchange="changeExtends(this)" required>
                                                    <?php foreach (json_decode($vendor->parameter) as $key => $value) : ?>
                                                        <option value="MAIN-<?= $value ?>" <?= $v->extends == 'MAIN-' . $value ? 'selected' : null ?>>Main: <?= $value ?></option>
                                                    <?php endforeach; ?>
                                                    <option value="Service ID" <?= $v->extends == 'Service ID' ? 'selected' : null ?>>Service ID</option>
                                                    <option value="Target" <?= $v->extends == 'Target' ? 'selected' : null ?>>Target</option>
                                                    <option value="Quantity" <?= $v->extends == 'Quantity' ? 'selected' : null ?>>Quantity</option>
                                                    <option value="Additional Data" <?= $v->extends == 'Additional Data' ? 'selected' : null ?>>Additional Data</option>
                                                    <option value="Transaction ID" <?= $v->extends == 'Transaction ID' ? 'selected' : null ?>>Transaction ID</option>
                                                    <option value="Server Transaction ID" <?= $v->extends == 'Server Transaction ID' ? 'selected' : null ?>>Server Transaction ID</option>
                                                    <option value="Signature" <?= $v->extends == 'Signature' ? 'selected' : null ?>>Signature</option>
                                                    <option value="Custom" <?= $v->extends == 'Custom' ? 'selected' : null ?>>Custom</option>
                                                </select>
                                            </div>
                                        </div>

                                        <?php if ($k == 0) : ?>
                                            <div class="col-md-2 mt-10">
                                                <button type="button" class="btn btn-primary btn-sm" onclick="addParameter()">
                                                    <i class="fa fa-plus pe-0"></i>
                                                </button>
                                            </div>
                                        <?php else : ?>
                                            <div class="col-md-2 mt-10">
                                                <button type="button" class="btn btn-danger btn-sm" onclick="removeParameter(this)">
                                                    <i class="fa fa-minus pe-0"></i>
                                                </button>
                                            </div>
                                        <?php endif; ?>

                                        <div class="col-md-12 mb-7 div-custom <?= $v->extends != 'Custom' ? 'd-none' : null ?>">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Custom</label>
                                            <input type="text" name="custom[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0 custom" placeholder="Masukkan Custom" value="<?= is_string($v->value) ? $v->value : null ?>">
                                        </div>

                                        <div class="col-md-12 mb-7 div-signature <?= $v->extends != 'Signature' ? 'd-none' : null ?>">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-7">
                                                        <label class="col-form-label fw-semibold fs-6 pt-0">Encryption Type</label>
                                                        <select name="encryptiontype[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                                            <option value="md5" <?= isset($v->value->encryptiontype) && $v->value->encryptiontype == 'md5' ? 'selected' : null ?>>MD5</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="col-md-6">
                                                    <div class="mb-7">
                                                        <label class="col-form-label fw-semibold fs-6 pt-0">Formula</label>
                                                        <input type="text" name="formula[]" class="form-control form-control-lg form-control-solid mb-lg-0 formula" placeholder="Masukkan Formula" value="<?= isset($v->value->formula) ? $v->value->formula : null ?>">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else : ?>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-7">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Parameter</label>
                                            <input type="text" name="parameter[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Parameter">
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="mb-7">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Extends</label>
                                            <select name="extends[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" onchange="changeExtends(this)">
                                                <?php foreach (json_decode($vendor->parameter) as $key => $value) : ?>
                                                    <option value="MAIN-<?= $value ?>">Main: <?= $value ?></option>
                                                <?php endforeach; ?>
                                                <option value="Service ID">Service ID</option>
                                                <option value="Target">Target</option>
                                                <option value="Quantity">Quantity</option>
                                                <option value="Additional Data">Additional Data</option>
                                                <option value="Transaction ID">Transaction ID</option>
                                                <option value="Server Transaction ID">Server Transaction ID</option>
                                                <option value="Signature">Signature</option>
                                                <option value="Custom">Custom</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-2 mt-10">
                                        <button type="button" class="btn btn-primary btn-sm" onclick="addParameter()">
                                            <i class="fa fa-plus pe-0"></i>
                                        </button>
                                    </div>

                                    <div class="col-md-12 mb-7 div-custom d-none">
                                        <label class="col-form-label fw-semibold fs-6 pt-0">Custom</label>
                                        <input type="text" name="custom[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0 custom" placeholder="Masukkan Custom">
                                    </div>

                                    <div class="col-md-12 mb-7 div-signature d-none">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-7">
                                                    <label class="col-form-label fw-semibold fs-6 pt-0">Encryption Type</label>
                                                    <select name="encryptiontype[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                                        <option value="md5">MD5</option>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-7">
                                                    <label class="col-form-label fw-semibold fs-6 pt-0">Formula</label>
                                                    <input type="text" name="formula[]" class="form-control form-control-lg form-control-solid mb-lg-0 formula" placeholder="Masukkan Formula">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h4>Response</h4>
                        <hr>

                        <h5>Response Indicator</h5>

                        <div class="mt-5">
                            <!--begin::Input group-->
                            <div class="input-group mb-5">
                                <span class="input-group-text">Index Variable</span>

                                <div id="display_plus_indexvar" class="<?= isset($indicator->index) && !empty($indicator->index) ? 'd-none' : null ?>">
                                    <button type="button" class="btn btn-primary" onclick="addIndexVar()" style="border-radius: 0px;">
                                        <i class="fa fa-plus"></i>
                                    </button>
                                </div>

                                <div id="display_minus_indexvar" class="<?= !isset($indicator->index) || empty($indicator->index) ? 'd-none' : null ?>">
                                    <button type="button" class="btn btn-danger" onclick="removeIndexVar()" style="border-radius: 0px;">
                                        <i class="fa fa-minus"></i>
                                    </button>
                                </div>

                                <input type="text" name="response_indicator_index" class="form-control <?= !isset($indicator->index) || empty($indicator->index) ? 'd-none' : null ?>" placeholder="Masukkan Index Variable" id="display_input_indexvar" value="<?= $indicator->index ?? null ?>">
                            </div>
                            <!--end::Input group-->

                            <!--begin::Input group-->
                            <div class="input-group mb-5">
                                <span class="input-group-text">Response Key Variable</span>
                                <input type="text" name="response_indicator_key" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Response Key Variable" value="<?= $indicator->key ?? 0 ?>" required>
                            </div>
                            <!--end::Input group-->

                            <div class="mb-7">
                                <label for="" class="col-form-label fw-semibold fs-6 pt-0">Valid Value</label>

                                <div class="input-group mb-5">
                                    <div class="input-group-text">
                                        <select name="response_indicator_datatype" id="response_indicator_datatype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                            <option value="string" <?= isset($indicator->datatype) && $indicator->datatype == 'string' ? 'selected' : null ?>>String</option>
                                            <option value="number" <?= isset($indicator->datatype) && $indicator->datatype == 'number' ? 'selected' : null ?>>Number</option>
                                            <option value="boolean" <?= isset($indicator->datatype) && $indicator->datatype == 'boolean' ? 'selected' : null ?>>Boolean</option>
                                            <option value="exists" <?= isset($indicator->datatype) && $indicator->datatype == 'exists' ? 'selected' : null ?>>Exists</option>
                                        </select>
                                    </div>

                                    <input type="text" name="response_indicator_valid_value" id="response_indicator_valid_value" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Value" value="<?= $indicator->value ?>" <?= isset($indicator->datatype) && $indicator->datatype == 'exists' ? 'readonly' : null ?> required>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <h5>Response Setting</h5>

                        <div class="mt-5">
                            <!--begin::Input group-->
                            <div class="input-group mb-5">
                                <span class="input-group-text">Index Variable</span>

                                <div id="display_plus_resindexvar" class="<?= $setting->index != null ? 'd-none' : null ?>">
                                    <button type="button" class="btn btn-primary" onclick="addResIndexVar()" style="border-radius: 0px;">
                                        <i class="fa fa-plus"></i>
                                    </button>
                                </div>

                                <div id="display_minus_resindexvar" class="<?= $setting->index == null ? 'd-none' : null ?>">
                                    <button type="button" class="btn btn-danger" onclick="removeResIndexVar()" style="border-radius: 0px;">
                                        <i class="fa fa-minus"></i>
                                    </button>
                                </div>

                                <input type="text" name="response_setting_index" class="form-control <?= $setting->index == null ? 'd-none' : null ?>" placeholder="Masukkan Index Variable" id="display_input_resindexvar" value="<?= $setting->index ?>">
                            </div>
                            <!--end::Input group-->

                            <div id="display_profile" class="<?= $detail->apitype != 'Profile' ? 'd-none' : null ?>">
                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Balance</span>
                                    <input type="text" name="response_profile_balance" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->balance) ? $setting->balance : null ?>">
                                </div>
                                <!--end::Input group-->
                            </div>

                            <div id="display_service" class="<?= $detail->apitype != 'Service' ? 'd-none' : null ?>">
                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Code</span>
                                    <input type="text" name="response_service_code" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->code) ? $setting->code : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Product Name</span>
                                    <input type="text" name="response_service_productname" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->productname) ? $setting->productname : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Description</span>
                                    <input type="text" name="response_service_description" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->description) ? $setting->description : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="mb-5">
                                    <label for="" class="col-form-label fw-semibold fs-6 pt-0">Status</label>
                                    <div class="row align-items-center">
                                        <div class="col-md-4">
                                            <input type="text" name="response_service_status" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->status) ? $setting->status : null ?>">
                                        </div>

                                        <div class="col-md-8">
                                            <div class="input-group mb-5">
                                                <div class="input-group-text">
                                                    <select name="response_service_statusdatatype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                                        <option value="string" <?= isset($setting->statusdatatype) && $setting->statusdatatype == 'string' ? 'selected' : null ?>>String</option>
                                                        <option value="number" <?= isset($setting->statusdatatype) && $setting->statusdatatype == 'number' ? 'selected' : null ?>>Number</option>
                                                        <option value="boolean" <?= isset($setting->statusdatatype) && $setting->statusdatatype == 'boolean' ? 'selected' : null ?>>Boolean</option>
                                                    </select>
                                                </div>

                                                <input type="text" name="response_service_validstatus" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Valid Value" value="<?= isset($setting->validstatus) ? $setting->validstatus : null ?>">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Price</span>
                                    <input type="text" name="response_service_price" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->price) ? $setting->price : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Category</span>
                                    <input type="text" name="response_service_category" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->category) ? $setting->category : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Brand</span>
                                    <input type="text" name="response_service_brand" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->brand) ? $setting->brand : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Min Order</span>
                                    <input type="text" name="response_service_minorder" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->minorder) ? $setting->minorder : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Max Order</span>
                                    <input type="text" name="response_service_maxorder" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->maxorder) ? $setting->maxorder : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Type</span>
                                    <input type="text" name="response_service_type" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->type) ? $setting->type : null ?>">
                                </div>
                                <!--end::Input group-->
                            </div>

                            <div id="display_order" class="<?= $detail->apitype != 'Order' ? 'd-none' : null ?>">
                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Reference ID</span>
                                    <input type="text" name="response_order_referenceid" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->referenceid) ? $setting->referenceid : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Price</span>
                                    <input type="text" name="response_order_price" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->price) ? $setting->price : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Status</span>
                                    <input type="text" name="response_order_status" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->status) ? $setting->status : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Note</span>
                                    <input type="text" name="response_order_note" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->note) ? $setting->note : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">SN</span>
                                    <input type="text" name="response_order_sn" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->sn) ? $setting->sn : null ?>">
                                </div>
                                <!--end::Input group-->
                            </div>

                            <div id="display_status" class="<?= $detail->apitype != 'Status' ? 'd-none' : null ?>">
                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Reference ID</span>
                                    <input type="text" name="response_status_referenceid" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->referenceid) ? $setting->referenceid : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Start Count</span>
                                    <input type="text" name="response_status_startcount" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->startcount) ? $setting->startcount : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Remains</span>
                                    <input type="text" name="response_status_remains" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->remains) ? $setting->remains : null ?>">
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="input-group mb-5">
                                    <span class="input-group-text">Status</span>
                                    <input type="text" name="response_status_status" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Index Variable" value="<?= isset($setting->status) ? $setting->status : null ?>">
                                </div>
                                <!--end::Input group-->
                            </div>

                            <div id="display_refund" class="<?= $detail->apitype != 'Status' && $detail->apitype != 'Order' ? 'd-none' : null ?>">
                                <hr>

                                <!--begin::Input group-->
                                <div class="mb-5">
                                    <div class="input-group">
                                        <span class="input-group-text">Error Refund</span>
                                        <input type="text" name="response_status_errorrefund" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Parameter Error Refund" value="<?= isset($setting->errorrefund) ? $setting->errorrefund : null ?>">
                                    </div>
                                    <small>*Pisahkan text dengan kode <b>[#]</b></small>
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="mb-5 <?= $detail->apitype != 'Status' ? 'd-none' : null ?>">
                                    <div class="input-group">
                                        <span class="input-group-text">Partial Refund</span>
                                        <input type="text" name="response_status_partialrefund" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Parameter Partial Refund" value="<?= isset($setting->partialrefund) ? $setting->partialrefund : null ?>">
                                    </div>
                                    <small>*Pisahkan text dengan kode <b>[#]</b></small>
                                </div>
                                <!--end::Input group-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    $.AjaxRequest('#frmAddDetail', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success',
                }).then(function(result) {
                    return window.location.reload();

                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.MESSAGE,
                    icon: 'error',
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        }
    });

    function addParameter() {
        $('#parameter').append(`<div class="row">
                        <div class="col-md-6">
                            <div class="mb-7">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Parameter</label>
                                <input type="text" name="parameter[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Parameter" required>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-7">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Extends</label>
                                <select name="extends[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" onchange="changeExtends(this)" required>
                                    <?php foreach (json_decode($vendor->parameter) as $key => $value) : ?>
                                        <option value="<?= $value ?>">Main: <?= $value ?></option>
                                    <?php endforeach; ?>
                                    <option value="Service ID">Service ID</option>
                                    <option value="Target">Target</option>
                                    <option value="Quantity">Quantity</option>
                                    <option value="Additional Data">Additional Data</option>
                                    <option value="Transaction ID">Transaction ID</option>
                                    <option value="Server Transaction ID">Server Transaction ID</option>
                                    <option value="Signature">Signature</option>
                                    <option value="Custom">Custom</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-2 mt-10">
                            <button type="button" class="btn btn-danger btn-sm" onclick="removeParameter(this)">
                                <i class="fa fa-minus pe-0"></i>
                            </button>
                        </div>

                        <div class="mb-7 div-custom d-none">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Custom</label>
                            <input type="text" name="custom[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0 custom" placeholder="Masukkan Custom">
                        </div>

                        <div class="mb-7 div-signature d-none">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-7">
                                        <label class="col-form-label fw-semibold fs-6 pt-0">Encryption Type</label>
                                        <select name="encryptiontype[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                            <option value="md5">MD5</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-7">
                                        <label class="col-form-label fw-semibold fs-6 pt-0">Formula</label>
                                        <input type="text" name="formula[]" class="form-control form-control-lg form-control-solid mb-lg-0 formula" placeholder="Masukkan Formula">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>`);
    }

    function addHeaders() {
        $('#headers').append(`<div class="row">
            <div class="col-md-6">
                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Headers</label>
                    <input type="text" name="headers[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Headers">
                </div>
            </div>

            <div class="col-md-4">
                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Value</label>
                    <select name="headers_value[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" onchange="changeHeadersExtends(this)">
                        <?php foreach (json_decode($vendor->parameter) as $key => $value) : ?>
                            <option value="MAIN-<?= $value ?>">Main: <?= $value ?></option>
                        <?php endforeach; ?>
                        <option value="Custom">Custom</option>
                    </select>
                </div>
            </div>

            <div class="col-md-2 mt-10">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeHeaders(this)">
                    <i class="fa fa-minus pe-0"></i>
                </button>
            </div>

            <div class="col-md-12 mb-7 div-headers-custom d-none">
                <label class="col-form-label fw-semibold fs-6 pt-0">Custom</label>
                <input type="text" name="custom_headers[]" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Custom">
            </div>
        </div>`);
    }

    function removeHeaders(these) {
        $(these).parent().parent().remove();
    }

    function removeParameter(these) {
        $(these).parent().parent().remove();
    }

    function changeExtends(these) {
        if ($(these).val() == 'Custom') {
            $(these).parent().parent().parent().find('.div-custom').removeClass('d-none').find('input').attr('required', true);
            $(these).parent().parent().parent().find('.div-signature').addClass('d-none').find('input, select').removeAttr('required');
        } else if ($(these).val() == 'Signature') {
            $(these).parent().parent().parent().find('.div-custom').addClass('d-none').find('input').removeAttr('required');
            $(these).parent().parent().parent().find('.div-signature').removeClass('d-none').find('input, select').attr('required', true);
        } else {
            $(these).parent().parent().parent().find('.div-custom').addClass('d-none').find('input').removeAttr('required');
            $(these).parent().parent().parent().find('.div-signature').addClass('d-none').find('input, select').removeAttr('required');
        }
    }

    function changeHeadersExtends(these) {
        if ($(these).val() == 'Custom') {
            $(these).parent().parent().parent().find('.div-headers-custom').removeClass('d-none').find('input').attr('required', true);
        } else {
            $(these).parent().parent().parent().find('.div-headers-custom').addClass('d-none').find('input').removeAttr('required');
        }
    }

    function addIndexVar() {
        $('#display_minus_indexvar, #display_input_indexvar').removeClass('d-none');
        $('#display_input_indexvar').attr('required', true);
        $('#display_plus_indexvar').addClass('d-none');
    }

    function removeIndexVar() {
        $('#display_minus_indexvar, #display_input_indexvar').addClass('d-none');
        $('#display_input_indexvar').removeAttr('required').val(null);
        $('#display_plus_indexvar').removeClass('d-none');
    }

    function addResIndexVar() {
        $('#display_minus_resindexvar, #display_input_resindexvar').removeClass('d-none');
        $('#display_input_resindexvar').attr('required', true);
        $('#display_plus_resindexvar').addClass('d-none');
    }

    function removeResIndexVar() {
        $('#display_minus_resindexvar, #display_input_resindexvar').addClass('d-none');
        $('#display_input_resindexvar').removeAttr('required').val(null);
        $('#display_plus_resindexvar').removeClass('d-none');
    }

    $('#api').change(function() {
        if ($(this).val() == 'Profile') {
            $('#display_profile').removeClass('d-none');
            $('#display_service, #display_order, #display_status, #display_refund, #display_autoaddproduct').addClass('d-none');
        } else if ($(this).val() == 'Service') {
            $('#display_service, #display_autoaddproduct').removeClass('d-none');
            $('#display_profile, #display_order, #display_status, #display_refund').addClass('d-none');
        } else if ($(this).val() == 'Order') {
            $('#display_order, #display_refund').removeClass('d-none');
            $('#display_profile, #display_service, #display_status, #display_autoaddproduct').addClass('d-none');
            $('input[name=response_status_partialrefund]').parent().parent().addClass('d-none');
        } else if ($(this).val() == 'Status') {
            $('#display_status, #display_refund').removeClass('d-none');
            $('#display_profile, #display_service, #display_order, #display_autoaddproduct').addClass('d-none');
            $('input[name=response_status_partialrefund]').parent().parent().removeClass('d-none');
        }
    });

    $('#response_indicator_datatype').change(function() {
        if ($(this).val() == 'exists') {
            $('#response_indicator_valid_value').attr('readonly', true).val('true');
        } else if ($(this).val() == 'boolean') {
            $('#response_indicator_valid_value').attr('readonly', true).val('true');
        } else {
            $('#response_indicator_valid_value').removeAttr('readonly').val(null);
        }
    });
</script>