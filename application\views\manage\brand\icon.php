<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Ubah Icon Brand: <?= $tables == 'msproduct' ? $brand->brand : $brand->name ?></h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmEditIcon" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off" enctype="multipart/form-data">
            <input type="hidden" name="_token" value="<?= stringEncryption('encrypt', $tables == 'msproduct' ? $brand->brand : $brand->id) ?>">
            <input type="hidden" name="tb" value="<?= stringEncryption('encrypt', $tables) ?>">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <?php if (!empty($current_icon->assetid)): ?>
                    <div class="mb-3">
                        <label class="col-form-label fw-semibold fs-6 pt-0">Icon Saat Ini</label>
                        <div>
                            <?php
                            $icon_url = '';
                            foreach ($icon as $icon_item) {
                                if ($icon_item->id == $current_icon->assetid) {
                                    $icon_url = $icon_item->asseturl;
                                    break;
                                }
                            }
                            ?>
                            <?php if (!empty($icon_url)): ?>
                                <img src="<?= base_url('uploads/' . $icon_url) ?>" alt="Current Icon" style="max-width: 100px; max-height: 100px;" class="img-thumbnail">
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Icon</label>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Pilih dari Galeri</label>
                            <select name="asset" class="form-control form-control-lg form-control-solid">
                                <option value="">- Pilih -</option>
                                <?php foreach ($icon as $key => $value) : ?>
                                    <option value="<?= $value->id ?>" <?= ($current_icon->assetid ?? 0) == $value->id ? 'selected' : null ?>><?= $value->assetname ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Atau Upload Icon Baru</label>
                            <input type="file" name="icon_file" class="form-control form-control-lg form-control-solid" accept=".jpg,.jpeg,.png">
                            <small class="text-muted">Format: JPG, JPEG, PNG. Maks: 2MB</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    $.AjaxRequest('#frmEditIcon', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success',
                }).then(() => {
                    return window.location.reload();
                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.MESSAGE,
                    icon: 'error',
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        }
    });
</script>