<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsNewsType $msnewstype
 * @property MsNews $msnews
 * @property CI_Upload $upload
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver|CI_DB_query_builder $db
 */
class News extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsNewsType', 'msnewstype');
        $this->load->model('MsNews', 'msnews');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser() && !isAdmin()) {
            return redirect(base_url('dashboard'));
        } else if (!isAdmin() && getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Berita';
        $data['content'] = 'manage/news/index';

        return $this->load->view('master', $data);
    }

    public function datatables_news()
    {
        try {
            if (isLogin() && (isUser() && getCurrentUser()->licenseid != null) || isAdmin()) {
                $data = array();

                $datatables = $this->datatables->make('MsNews', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.userid' => getCurrentIdUser()
                );

                foreach ($datatables->getData($where) as $key => $value) {
                    $appsdisplay = "";

                    if ($value->apps == 1) {
                        $appsdisplay = "<i class=\"fa fa-check text-success\"></i>";
                    } else {
                        $appsdisplay = "<i class=\"fa fa-times text-danger\"></i>";
                    }

                    $detail = array();
                    $detail[] = tgl_indo(date('Y-m-d')) . ' ' . date('H:i:s', strtotime($value->createddate));
                    $detail[] = $value->typename;
                    $detail[] = $value->content;
                    $detail[] = $appsdisplay;
                    $detail[] = "<a href=\"" . base_url('manage/news/edit/' . $value->id) . "\" class=\"btn btn-icon btn-warning btn-sm mb-1\">
                        <i class=\"fa fa-edit\"></i>
                    </a>

                    <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"deleteNews('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-trash\"></i>
                    </a>";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser() && !isAdmin()) {
            return redirect(base_url('dashboard'));
        } else if (!isAdmin() && getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Berita';
        $data['content'] = 'manage/news/add';
        $data['newstype'] = $this->msnewstype->result();

        return $this->load->view('master', $data);
    }

    public function process_add_news()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser() && !isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (!isAdmin() && getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $type = getPost('type');
            $content = getPost('content');
            $apps = getPost('apps');
            $judul_berita = getPost('judul_berita');

            if ($type == null) {
                throw new Exception('Tipe berita tidak boleh kosong');
            } else if ($content == null) {
                throw new Exception('Konten berita tidak boleh kosong');
            } else {
                if ($apps) {
                    if ($judul_berita == null) {
                        throw new Exception('Judul berita tidak boleh kosong');
                    } else {
                        $judul_berita = removeSymbol($judul_berita);
                    }
                }

                $htmlpurifierconfig = HTMLPurifier_Config::createDefault();
                $purifier = new HTMLPurifier($htmlpurifierconfig);

                $content = $purifier->purify($content);
            }

            $get = $this->msnewstype->total(array(
                'id' => $type
            ));

            if ($get == 0) {
                throw new Exception('Tipe berita tidak ditemukan');
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['typeid'] = $type;
            $insert['content'] = $content;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();
            $insert['updateddate'] = getCurrentDate();
            $insert['updatedby'] = getCurrentIdUser();

            if (!isAdmin()) {
                $insert['apps'] = $apps ? 1 : 0;

                if ($apps) {
                    $insert['title'] = $judul_berita;

                    if (isset($_FILES['picture']['size']) && $_FILES['picture']['size'] > 0) {
                        $config = array(
                            'upload_path' => './uploads/',
                            'allowed_types' => 'jpg|jpeg|png',
                            'max_size' => 2048,
                            'encrypt_name' => true,
                        );

                        $this->load->library('upload', $config);

                        if (!$this->upload->do_upload('picture')) {
                            throw new Exception($this->upload->display_errors('', ''));
                        } else {
                            $upload = $this->upload->data();

                            $insert['picture'] = $upload['file_name'];
                        }
                    }
                }
            }

            $this->msnews->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan berita');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menambahkan berita');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser() && !isAdmin()) {
            return redirect(base_url('dashboard'));
        } else if (!isAdmin() && getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->msnews->get(array(
            'id' => $id,
            'userid' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/news'));
        }

        $data = array();
        $data['title'] = 'Ubah Berita';
        $data['content'] = 'manage/news/edit';
        $data['newstype'] = $this->msnewstype->result();
        $data['news'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit_news($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser() && !isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (!isAdmin() && getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $get = $this->msnews->get(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Berita tidak ditemukan');
            }

            $row = $get->row();

            $type = getPost('type');
            $content = getPost('content');
            $apps = getPost('apps');
            $judul_berita = getPost('judul_berita');
            $picture = isset($_FILES['picture']) ? $_FILES['picture'] : null;

            if ($type == null) {
                throw new Exception('Tipe berita tidak boleh kosong');
            } else if ($content == null) {
                throw new Exception('Konten berita tidak boleh kosong');
            } else {
                if ($apps) {
                    if ($judul_berita == null) {
                        throw new Exception('Judul berita tidak boleh kosong');
                    } else {
                        $judul_berita = removeSymbol($judul_berita);
                    }
                }

                $htmlpurifierconfig = HTMLPurifier_Config::createDefault();
                $purifier = new HTMLPurifier($htmlpurifierconfig);

                $content = $purifier->purify($content);
            }

            $get = $this->msnewstype->total(array(
                'id' => $type
            ));

            if ($get == 0) {
                throw new Exception('Tipe berita tidak ditemukan');
            }

            $update = array();
            $update['typeid'] = $type;
            $update['content'] = $content;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            if (!isAdmin()) {
                $update['apps'] = $apps ? 1 : 0;

                if ($apps) {
                    $update['title'] = $judul_berita;

                    if ($picture['name'] != null) {
                        $config = array(
                            'upload_path' => './uploads/',
                            'allowed_types' => 'jpg|jpeg|png',
                            'max_size' => 2048,
                            'encrypt_name' => true,
                        );

                        $this->load->library('upload', $config);

                        if (!$this->upload->do_upload('picture')) {
                            throw new Exception($this->upload->display_errors('', ''));
                        } else {
                            $upload = $this->upload->data();

                            $update['picture'] = $upload['file_name'];

                            if ($row->picture != null && file_exists('./uploads/' . $row->picture)) {
                                unlink('./uploads/' . $row->picture);
                            }
                        }
                    }
                } else {
                    if ($row->picture != null && file_exists('./uploads/' . $row->picture)) {
                        unlink('./uploads/' . $row->picture);
                    }

                    $update['title'] = null;
                    $update['picture'] = null;
                }
            }

            $this->msnews->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah berita');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil mengubah berita');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_news()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser() && !isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (!isAdmin() && getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            if ($id == null) {
                throw new Exception('ID berita tidak boleh kosong');
            }

            $get = $this->msnews->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Berita tidak ditemukan');
            }

            $this->msnews->delete(array(
                'id' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus berita');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menghapus berita');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
