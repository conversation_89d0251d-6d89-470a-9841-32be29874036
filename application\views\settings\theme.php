<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Tema</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Akun</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Tema</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="card mb-5">
        <div class="card-header">
            <div class="card-title m-0">
                <h3 class="m-0 fw-bold">Tema Dashboard</h3>
            </div>
        </div>

        <div class="card-body">
            <div class="row">
                <!-- Only keeping Able and Fin-App themes -->

                <div class="col-lg-3 col-md-4 col-sm-6 mb-5">
                    <div class="overlay">
                        <!--begin::Image-->
                        <div class="overlay-wrapper">
                            <img alt="img" class="w-100" src="<?= base_url('assets/media/demos/demo30.png') ?>" style="height: 170px; object-position: center; object-fit: cover;">
                        </div>
                        <!--end::Image-->

                        <!--begin::Link-->
                        <div class="overlay-layer bg-dark bg-opacity-10 rounded">
                            <?php if (getCurrentThemeConfiguration() == 'Able') : ?>
                                <a href="javascript:;" class="btn btn-sm btn-primary btn-shadow" onclick="configuration('Able')">Konfigurasi Tema</a>
                            <?php else : ?>
                                <a href="javascript:;" class="btn btn-sm btn-primary btn-shadow" onclick="useTheme('Able')">Gunakan Tema Ini</a>
                            <?php endif; ?>
                        </div>
                        <!--end::Link-->
                    </div>

                    <div class="text-center">
                        <h5 class="fw-bold mt-4 <?= getCurrentThemeConfiguration() == 'Able' ? 'text-primary' : null ?>">Able <?= getCurrentThemeConfiguration() == 'Able' ? '- Digunakan' : null ?></h5>
                    </div>
                </div>

                <div class="col-lg-3 col-md-4 col-sm-6 mb-5">
                    <div class="overlay">
                        <!--begin::Image-->
                        <div class="overlay-wrapper">
                            <img alt="img" class="w-100" src="<?= base_url('assets/media/demos/demo29.png') ?>" style="height: 170px; object-position: center; object-fit: cover;">
                        </div>
                        <!--end::Image-->

                        <!--begin::Link-->
                        <div class="overlay-layer bg-dark bg-opacity-10 rounded">
                            <?php if (getCurrentThemeConfiguration() == 'Fin-App') : ?>
                                <a href="javascript:;" class="btn btn-sm btn-primary btn-shadow" onclick="configuration('Fin-App')">Konfigurasi Tema</a>
                            <?php else : ?>
                                <a href="javascript:;" class="btn btn-sm btn-primary btn-shadow" onclick="useTheme('Fin-App')">Gunakan Tema Ini</a>
                            <?php endif; ?>
                        </div>
                        <!--end::Link-->
                    </div>

                    <div class="text-center">
                        <h5 class="fw-bold mt-4 <?= getCurrentThemeConfiguration() == 'Fin-App' ? 'text-primary' : null ?>">Fin-App (PWA & No Login Transaction) <?= getCurrentThemeConfiguration() == 'Fin-App' ? '- Digunakan' : null ?></h5>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function configuration(themename) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/configuration') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                themename: themename
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    }

    function useTheme(themename) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Apakah anda yakin ingin mengubah tema website anda?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/change') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        themename: themename
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();
                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }
</script>