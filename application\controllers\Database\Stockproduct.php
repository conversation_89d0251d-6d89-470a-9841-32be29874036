<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsStockproduct $msstockproduct
 * @property MsStockproductDetail $msstockproductdetail
 * @property Datatables $datatables
 * @property CI_DB_query_builder $db
 */
class Stockproduct extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsStockproduct', 'msstockproduct');
        $this->load->model('MsStockproductDetail', 'msstockproductdetail');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory != 'PPOB' && $this->user->companycategory != 'PPOB & SMM') {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Database Stok Produk';
        $data['content'] = 'database/stockproduct/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        try {
            if (isLogin() && (isUser()  && getCurrentUser()->licenseid != null && (getCurrentUser()->companycategory == 'PPOB' || getCurrentUser()->companycategory == 'PPOB & SMM'))) {
                $data = array();

                $datatables = $this->datatables->make('MsStockproduct', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.createdby' => getCurrentIdUser()
                );

                foreach ($datatables->getData($where) as $key => $value) {
                    $detail = array();
                    $detail[] = $value->name;
                    $detail[] = $value->category;
                    $detail[] = "<a href=\"" . base_url('database/stockproduct/edit/' . $value->id) . "\" class=\"btn btn-icon btn-warning btn-sm mb-1\">
                        <i class=\"fa fa-edit\"></i>
                    </a>

                    <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"deleteStockproduct('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-trash\"></i>
                    </a>

                    <a href=\"" . base_url('database/stockproduct/detail/' . $value->id) . "\" class=\"btn btn-icon btn-primary btn-sm mb-1\">
                        <i class=\"fa fa-eye\"></i>
                    </a>";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory != 'PPOB' && $this->user->companycategory != 'PPOB & SMM') {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Tambah Stok Produk';
        $data['content'] = 'database/stockproduct/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Access denied');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Access denied');
            } else if ($this->user->companycategory != 'PPOB' && $this->user->companycategory != 'PPOB & SMM') {
                throw new Exception('Access denied');
            }

            $name = getPost('name');
            $category = getPost('category');

            if ($name == null) {
                throw new Exception('Name Produk boleh kosong');
            } else if ($category == null) {
                throw new Exception('Kategori Layanan boleh kosong');
            }

            $insert = array();
            $insert['name'] = $name;
            $insert['category'] = 'PPOB';
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();
            $insert['updateddate'] = getCurrentDate();
            $insert['updatedby'] = getCurrentIdUser();

            $this->msstockproduct->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan stok produk');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menambahkan stok produk');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory != 'PPOB' && $this->user->companycategory != 'PPOB & SMM') {
            return redirect(base_url('dashboard'));
        }

        $get = $this->msstockproduct->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('database/stockproduct'));
        }

        $data = array();
        $data['title'] = 'Ubah Stok Produk';
        $data['content'] = 'database/stockproduct/edit';
        $data['stockproduct'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Access denied');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Access denied');
            } else if ($this->user->companycategory != 'PPOB' && $this->user->companycategory != 'PPOB & SMM') {
                throw new Exception('Access denied');
            }

            $get = $this->msstockproduct->total(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Stok produk tidak ditemukan');
            }

            $name = getPost('name');
            $category = getPost('category');

            if ($name == null) {
                throw new Exception('Nama Produk tidak boleh kosong');
            } else if ($category == null) {
                throw new Exception('Kategori Layanan tidak boleh kosong');
            }

            $update = array();
            $update['name'] = $name;
            $update['category'] = 'PPOB';
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msstockproduct->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data stok produk');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil mengubah data stok produk');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Access denied');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Access denied');
            } else if ($this->user->companycategory != 'PPOB' && $this->user->companycategory != 'PPOB & SMM') {
                throw new Exception('Access denied');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            if ($id == null) {
                throw new Exception('ID tidak boleh kosong');
            }

            $get = $this->msstockproduct->total(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Stok produk tidak ditemukan');
            }

            $this->msstockproduct->delete(array(
                'id' => $id
            ));

            $this->msstockproductdetail->delete(array(
                'stockproductid' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus stok produk');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menghapus stok produk');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function detail($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory != 'PPOB' && $this->user->companycategory != 'PPOB & SMM') {
            return redirect(base_url('dashboard'));
        }

        $cek = $this->msstockproduct->total(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($cek == 0) {
            return redirect(base_url('database/stockproduct'));
        }

        $data = array();
        $data['title'] = 'Database Stok Produk';
        $data['content'] = 'database/stockproduct/detail/index';

        return $this->load->view('master', $data);
    }

    public function datatables_detail($id)
    {
        try {
            if (isLogin() && (isUser() && getCurrentUser()->licenseid != null && (getCurrentUser()->companycategory == 'PPOB' || getCurrentUser()->companycategory == 'PPOB & SMM'))) {
                $data = array();

                $datatables = $this->datatables->make('MsStockproductDetail', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.createdby' => getCurrentIdUser(),
                    'a.stockproductid' => $id
                );

                foreach ($datatables->getData($where) as $key => $value) {
                    $status = "<span class=\"badge badge-success\">Tersedia</span>";

                    if ($value->status == 'Tidak Tersedia') {
                        $status = "<span class=\"badge badge-danger\">Tidak Tersedia</span>";
                    }

                    $detail = array();
                    $detail[] = stringEncryption('decrypt', $value->data);
                    $detail[] = $status;

                    if ($value->status == 'Tersedia') {
                        $detail[] = "<a href=\"javascript:;\" onclick=\"edit(" . $value->id . ")\" class=\"btn btn-icon btn-warning btn-sm mb-1\">
                            <i class=\"fa fa-edit\"></i>
                        </a>

                        <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"deleteDetailStockproduct('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-trash\"></i>
                        </a>";
                    } else {
                        $detail[] = "N/A";
                    }

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add_detail($id)
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Access denied');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Access denied');
            } else if ($this->user->companycategory != 'PPOB' && $this->user->companycategory != 'PPOB & SMM') {
                throw new Exception('Access denied');
            }

            $cek = $this->msstockproduct->total(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($cek == 0) {
                throw new Exception('Stok produk tidak ditemukan');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('database/stockproduct/detail/add', array(), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_add_detail($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Access denied');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Access denied');
            } else if ($this->user->companycategory != 'PPOB' && $this->user->companycategory != 'PPOB & SMM') {
                throw new Exception('Access denied');
            }

            $data = getPost('data');
            $status = getPost('status');

            if ($data == null) {
                throw new Exception('Data tidak boleh kosong');
            } else if ($status == null) {
                throw new Exception('Status tidak boleh kosong');
            }

            $cek = $this->msstockproduct->total(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($cek == 0) {
                throw new Exception('Stok produk tidak ditemukan');
            }

            $insert = array();
            $insert['stockproductid'] = $id;
            $insert['data'] = stringEncryption('encrypt', $data);
            $insert['status'] = 'Tersedia';
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();
            $insert['updateddate'] = getCurrentDate();
            $insert['updatedby'] = getCurrentIdUser();

            $this->msstockproductdetail->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan stok produk detail');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menambahkan stok produk detail');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit_detail($id, $detailid)
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Access denied');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Access denied');
            } else if ($this->user->companycategory != 'PPOB' && $this->user->companycategory != 'PPOB & SMM') {
                throw new Exception('Access denied');
            }

            $get = $this->msstockproduct->total(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Stok produk tidak ditemukan');
            }

            $getdetail = $this->msstockproductdetail->get(array(
                'id' => $detailid,
                'createdby' => getCurrentIdUser(),
                'status' => 'Tersedia'
            ));

            if ($getdetail->num_rows() == 0) {
                throw new Exception('Stok produk detail tidak ditemukan');
            }

            $data = array();
            $data['detailstockproduct'] = $getdetail->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('database/stockproduct/detail/edit', $data, true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_edit_detail($id, $detailid)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Access denied');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Access denied');
            } else if ($this->user->companycategory != 'PPOB' && $this->user->companycategory != 'PPOB & SMM') {
                throw new Exception('Access denied');
            }

            $get = $this->msstockproduct->total(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Stok produk tidak ditemukan');
            }

            $getdetail = $this->msstockproductdetail->total(array(
                'id' => $detailid,
                'createdby' => getCurrentIdUser(),
                'status' => 'Tersedia'
            ));

            if ($getdetail == 0) {
                throw new Exception('Stok produk detail tidak ditemukan');
            }

            $data = getPost('data');

            if ($data == null) {
                throw new Exception('Data tidak boleh kosong');
            }

            $update = array();
            $update['data'] = stringEncryption('encrypt', $data);
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msstockproductdetail->update(array(
                'id' => $detailid
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data stok produk detail');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil mengubah data stok produk detail');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_detail($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isUser()) {
                throw new Exception('Access denied');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Access denied');
            } else if ($this->user->companycategory != 'PPOB' && $this->user->companycategory != 'PPOB & SMM') {
                throw new Exception('Access denied');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            if ($id == null) {
                throw new Exception('ID tidak boleh kosong');
            }

            $get = $this->msstockproductdetail->total(array(
                'id' => $id,
                'createdby' => getCurrentIdUser(),
                'status' => 'Tersedia'
            ));

            if ($get == 0) {
                throw new Exception('Stok produk detail tidak ditemukan');
            }

            $this->msstockproductdetail->delete(array(
                'id' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus stok produk detail');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menghapus stok produk detail');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
