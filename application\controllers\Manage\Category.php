<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsProduct $msproduct
 * @property DisabledCategory $disabledcategory
 * @property DisabledBrand $disabledbrand
 * @property MsCategory $mscategory
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver $db
 * @property MsBrand $msbrand
 * @property MsIcons $msicons
 * @property CategoryImage $categoryimage
 * @property BrandImage $brandimage
 */
class Category extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('DisabledCategory', 'disabledcategory');
        $this->load->model('DisabledBrand', 'disabledbrand');
        $this->load->model('MsCategory', 'mscategory');
        $this->load->model('MsBrand', 'msbrand');
        $this->load->model('MsIcons', 'msicons');
        $this->load->model('CategoryImage', 'categoryimage');
        $this->load->model('BrandImage', 'brandimage');

        // Load upload library
        $this->load->library('upload');
    }

    public function product()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        }

        $currentuserid = getCurrentIdUser();

        if (getCurrentUser()->multivendor != 1) {
            $ppob = getCurrentVendor('PPOB', getCurrentIdUser());
            $smm = getCurrentVendor('SMM', getCurrentIdUser());

            if ($ppob != null && $smm != null) {
                $vendor = "( a.vendor = '$ppob' OR a.vendor = '$smm' )";
            } else if ($ppob != null && $smm == null) {
                $vendor = "a.vendor = '$ppob'";
            } else if ($ppob == null && $smm != null) {
                $vendor = "a.vendor = '$smm'";
            }

            if (isset($vendor)) {
                $vendor .= " AND a.vendorid IS NULL";
            } else {
                $vendor = "( a.vendorid IS NULL AND a.vendor IS NULL )";
            }
        } else {
            $vendor = "( a.vendorid IS NOT NULL AND a.vendorenabled = 1 )";
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Kategori Produk';
        $data['content'] = 'manage/category/product';
        $data['category'] = $this->db->query("SELECT a.*, ci.assetid, i.asseturl FROM (SELECT a.id, 'msproduct' AS tables, a.category, a.category_apikey, a.subcategory_apikey, b.id AS disabledid, ( CASE WHEN a.brand IS NOT NULL THEN '1' ELSE '0' END ) AS isbrand, a.oldcategoryname, ( CASE WHEN c.total > 0 THEN '1' ELSE '0' END ) AS dontupdatecategory FROM msproduct a LEFT JOIN disabledcategory b ON b.categoryname = a.category AND b.category_apikey = a.category_apikey AND b.userid = a.userid LEFT JOIN (select category, userid, count(*) as total from msproduct where dontupdatecategory = 1 group by category, userid) c ON c.category = a.category AND c.userid = a.userid WHERE a.category IS NOT NULL AND a.userid = $currentuserid AND $vendor GROUP BY a.category, a.category_apikey UNION ALL SELECT a.id, 'mscategory' AS tables, a. NAME AS category, a.servicetype AS category_apikey, a.servicesubtype AS subcategory_apikey, NULL AS disabledid, ( CASE WHEN b. NAME IS NOT NULL THEN '1' ELSE '0' END ) AS isbrand, NULL AS oldcategoryname, NULL AS dontupdatecategory FROM mscategory a LEFT JOIN msbrand b ON b.category = a. NAME AND b.userid = a.userid WHERE a.userid = $currentuserid GROUP BY a. NAME, a.servicetype) a LEFT JOIN categoryimage ci ON ci.categoryname = a.category AND ci.userid = $currentuserid LEFT JOIN msicons i ON i.id = ci.assetid ORDER BY a.category ASC")->result();

        return $this->load->view('master', $data);
    }

    public function process_disable_category_product()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $category = getPost('category');
            $api = getPost('api');

            if ($category == null) {
                throw new Exception('Kategori tidak boleh kosong');
            } else if ($api == null) {
                throw new Exception('API Key tidak boleh kosong');
            }

            $where = array(
                'a.category' => $category,
                'a.category_apikey' => $api,
                'a.userid' => getCurrentIdUser()
            );

            if (getCurrentUser()->multivendor != 1) {
                $apikeysppob = getCurrentAPIKeys('PPOB');
                $apikeyssmm = getCurrentAPIKeys('SMM');

                $where['a.vendorid'] = null;
                if ($apikeysppob != null && $apikeyssmm != null) {
                    $where["(a.vendor = '$apikeysppob->vendor' OR a.vendor = '$apikeyssmm->vendor') ="] = true;
                } else if ($apikeysppob != null && $apikeyssmm == null) {
                    $where['a.vendor'] = $apikeysppob->vendor;
                } else if ($apikeysppob == null && $apikeyssmm != null) {
                    $where['a.vendor'] = $apikeyssmm->vendor;
                }
            } else {
                $where["a.vendorid !="] = null;
                $where['a.vendorenabled'] = 1;
            }

            $get = $this->msproduct->total($where);

            if ($get == 0) {
                throw new Exception('Kategori tidak ditemukan');
            }

            $get = $this->disabledcategory->total(array(
                'categoryname' => $category,
                'category_apikey' => $api,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                $insert = array();
                $insert['userid'] = getCurrentIdUser();
                $insert['categoryname'] = $category;
                $insert['category_apikey'] = $api;

                $this->disabledcategory->insert($insert);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Kategori gagal dinonaktifkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Kategori berhasil dinonaktifkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_enable_category_product()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('ID Kategori tidak boleh kosong');
            }

            $get = $this->disabledcategory->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Kategori tidak ditemukan');
            }

            $this->disabledcategory->delete(array(
                'id' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Kategori gagal diaktifkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Kategori berhasil diaktifkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add_category_product()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/category/product/add', array(
                    'icons' => $this->msicons->order_by('assetname', 'ASC')->result(array(
                        'createdby' => getCurrentIdUser()
                    ))
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_add_category_product()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $name = getPost('name');
            $servicecategory = getPost('servicecategory');
            $asset = getPost('asset');
            $servicesubcategory = getPost('servicesubcategory');

            if ($name == null) {
                throw new Exception('Nama kategori tidak boleh kosong');
            } else if ($servicecategory == null) {
                throw new Exception('Kategori layanan tidak boleh kosong');
            } else if (!in_array($servicecategory, array('PPOB', 'SMM'))) {
                throw new Exception('Kategori layanan tidak valid');
            } else if ($servicecategory == 'PPOB') {
                if (!in_array($servicesubcategory, array('PRABAYAR', 'PASCABAYAR'))) {
                    throw new Exception('Sub kategori layanan tidak valid');
                }
            } else {
                $htmlpurifierconfig = HTMLPurifier_Config::createDefault();
                $purifier = new HTMLPurifier($htmlpurifierconfig);

                $name = $purifier->purify($name);
            }

            if (getCurrentUser(getCurrentIdUser())->licenseid == null) {
                $get = $this->mscategory->total(array(
                    'userid' => getCurrentIdUser(),
                ));

                if ($get >= 5) {
                    throw new Exception('Anda hanya dapat menambahkan maksimal 5 kategori');
                }
            }

            $get = $this->mscategory->total(array(
                'UPPER(name) =' => strtoupper($name),
                'userid' => getCurrentIdUser()
            ));

            if ($get > 0) {
                throw new Exception('Kategori sudah ada');
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['name'] = $name;
            $insert['servicetype'] = $servicecategory;

            if ($servicecategory == 'PPOB') {
                $insert['servicesubtype'] = $servicesubcategory;
            } else {
                $insert['servicesubtype'] = 'SMM';
            }

            $this->mscategory->insert($insert);

            // Check for icon file upload
            $uploaded_icon = $this->upload_icon_file();
            if ($uploaded_icon !== false) {
                // If file was uploaded, use the new icon ID
                $asset = $uploaded_icon['id'];
            }

            if ($asset == null) {
                $categoryimage = $this->categoryimage->get(array(
                    'userid' => getCurrentIdUser(),
                    'categoryname' => $name,
                ));

                if ($categoryimage->num_rows() > 0) {
                    $row = $categoryimage->row();

                    $this->categoryimage->delete(array(
                        'id' => $row->id
                    ));
                }
            } else {
                $categoryimage = $this->categoryimage->total(array(
                    'userid' => getCurrentIdUser(),
                    'categoryname' => $name,
                    'assetid' => $asset,
                ));

                if ($categoryimage == 0) {
                    $this->categoryimage->insert(array(
                        'userid' => getCurrentIdUser(),
                        'categoryname' => $name,
                        'assetid' => $asset,
                    ));
                }
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Kategori gagal ditambahkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Kategori berhasil ditambahkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit_category_product()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $category = getPost('category');
            $api = getPost('api');

            $where = array(
                'a.category' => $category,
                'a.category_apikey' => $api,
                'a.userid' => getCurrentIdUser()
            );

            if (getCurrentUser()->multivendor != 1) {
                $apikeysppob = getCurrentAPIKeys('PPOB');
                $apikeyssmm = getCurrentAPIKeys('SMM');

                $where['a.vendorid'] = null;
                if ($apikeysppob != null && $apikeyssmm != null) {
                    $where["(a.vendor = '$apikeysppob->vendor' OR a.vendor = '$apikeyssmm->vendor') ="] = true;
                } else if ($apikeysppob != null && $apikeyssmm == null) {
                    $where['a.vendor'] = $apikeysppob->vendor;
                } else if ($apikeysppob == null && $apikeyssmm != null) {
                    $where['a.vendor'] = $apikeyssmm->vendor;
                }
            } else {
                $where["a.vendorid !="] = null;
                $where['a.vendorenabled'] = 1;
            }

            $get = $this->msproduct->get($where);

            $tables = 'msproduct';
            if ($get->num_rows() == 0) {
                $get = $this->mscategory->get(array(
                    'name' => $category,
                    'servicetype' => $api,
                    'userid' => getCurrentIdUser()
                ));

                if ($get->num_rows() == 0) {
                    throw new Exception('Kategori tidak ditemukan');
                }

                $tables = 'mscategory';
            }

            $row = $get->row();

            $categoryimage = $this->categoryimage->get(array(
                'userid' => getCurrentIdUser(),
                'categoryname' => $category,
            ))->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/category/product/edit', array(
                    'row' => $row,
                    'found' => $tables,
                    'icons' => $this->msicons->order_by('assetname', 'ASC')->result(array(
                        'createdby' => getCurrentIdUser()
                    )),
                    'categoryimage' => $categoryimage
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_edit_category_product()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            $where = array(
                'a.id' => $id,
                'a.userid' => getCurrentIdUser()
            );

            if (getCurrentUser()->multivendor != 1) {
                $apikeysppob = getCurrentAPIKeys('PPOB');
                $apikeyssmm = getCurrentAPIKeys('SMM');

                $where['a.vendorid'] = null;
                if ($apikeysppob != null && $apikeyssmm == null) {
                    $where['a.vendor'] = $apikeysppob->vendor;
                } else if ($apikeyssmm != null && $apikeysppob == null) {
                    $where['a.vendor'] = $apikeyssmm->vendor;
                } else if ($apikeysppob != null && $apikeyssmm != null) {
                    $where["(a.vendor = '$apikeysppob->vendor' OR a.vendor = '$apikeyssmm->vendor') ="] = true;
                }
            } else {
                $where["a.vendorid !="] = null;
                $where['a.vendorenabled'] = 1;
            }

            $get = $this->msproduct->get($where);

            $tables = 'msproduct';
            if ($get->num_rows() == 0) {
                $get = $this->mscategory->get(array(
                    'id' => $id,
                    'userid' => getCurrentIdUser()
                ));

                if ($get->num_rows() == 0) {
                    throw new Exception('Kategori tidak ditemukan');
                }

                $tables = 'mscategory';
            }

            $row = $get->row();

            $name = getPost('name');
            $asset = getPost('asset');
            $servicecategory = getPost('servicecategory');
            $servicesubcategory = getPost('servicesubcategory');

            if ($name == null) {
                throw new Exception('Nama kategori tidak boleh kosong');
            } else if ($tables == 'mscategory') {
                if ($servicecategory == null) {
                    throw new Exception('Kategori layanan tidak boleh kosong');
                } else {
                    if ($servicecategory == 'PPOB') {
                        if ($servicesubcategory == null) {
                            throw new Exception('Sub kategori layanan tidak boleh kosong');
                        } else if (!in_array($servicesubcategory, array('PRABAYAR', 'PASCABAYAR'))) {
                            throw new Exception('Sub kategori layanan tidak valid');
                        }
                    }
                }
            } else {
                $htmlpurifierconfig = HTMLPurifier_Config::createDefault();
                $purifier = new HTMLPurifier($htmlpurifierconfig);

                $name = $purifier->purify($name);
            }

            if (($tables == 'msproduct' ? $row->category : $row->name) != $name) {
                $where = array(
                    'a.category' => $name,
                    'a.userid' => getCurrentIdUser(),
                );

                if (getCurrentUser()->multivendor != 1) {
                    if ($apikeysppob != null && $apikeyssmm != null) {
                        $where["(a.vendor = '$apikeysppob->vendor' OR a.vendor = '$apikeyssmm->vendor') ="] = true;
                    } else if ($apikeysppob != null && $apikeyssmm == null) {
                        $where['a.vendor'] = $apikeysppob->vendor;
                    } else if ($apikeysppob == null && $apikeyssmm != null) {
                        $where['a.vendor'] = $apikeyssmm->vendor;
                    }
                    $where['a.vendorid'] = null;
                } else {
                    $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
                }

                $get = $this->msproduct->total($where);

                if ($get > 0) {
                    throw new Exception('Kategori sudah ada');
                } else {
                    $where = array(
                        'UPPER(name) =' => strtoupper($name),
                        'userid' => getCurrentIdUser(),
                    );

                    $get = $this->mscategory->total($where);

                    if ($get > 0) {
                        throw new Exception('Kategori sudah ada');
                    }
                }
            }

            if ($tables == 'msproduct') {
                $update = array();
                $update['category'] = $name;

                if ($row->dontupdatecategory != 1 && empty($row->oldcategoryname) && $row->category != $name) {
                    $update['oldcategoryname'] = $row->category;
                    $update['dontupdatecategory'] = 1;
                }

                $where = array(
                    'userid' => getCurrentIdUser(),
                    'category' => $row->category,
                );

                if (getCurrentUser()->multivendor != 1) {
                    if ($apikeysppob != null && $apikeyssmm != null) {
                        $where["(vendor = '$apikeysppob->vendor' OR vendor = '$apikeyssmm->vendor') ="] = true;
                    } else if ($apikeysppob != null && $apikeyssmm == null) {
                        $where['vendor'] = $apikeysppob->vendor;
                    } else if ($apikeysppob == null && $apikeyssmm != null) {
                        $where['vendor'] = $apikeyssmm->vendor;
                    }
                    $where['vendorid'] = null;
                } else {
                    $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
                }

                $this->msproduct->update($where, $update);
            } else {
                $update = array();
                $update['name'] = $name;
                $update['servicetype'] = $servicecategory;

                if ($servicecategory == 'PPOB') {
                    $update['servicesubtype'] = $servicesubcategory;
                } else {
                    $update['servicesubtype'] = 'SMM';
                }

                $where = array(
                    'userid' => getCurrentIdUser(),
                    'id' => $id,
                );

                $this->mscategory->update($where, $update);
            }

            // Check for icon file upload
            $uploaded_icon = $this->upload_icon_file();
            if ($uploaded_icon !== false) {
                // If file was uploaded, use the new icon ID
                $asset = $uploaded_icon['id'];
            }

            if ($asset == null) {
                $categoryimage = $this->categoryimage->get(array(
                    'userid' => getCurrentIdUser(),
                    'categoryname' => $name,
                ));

                if ($categoryimage->num_rows() > 0) {
                    $row = $categoryimage->row();

                    $this->categoryimage->delete(array(
                        'id' => $row->id
                    ));
                }
            } else {
                if ($name != ($tables == 'msproduct' ? $row->category : $row->name)) {
                    $this->categoryimage->delete(array(
                        'userid' => getCurrentIdUser(),
                        'categoryname' => $tables == 'msproduct' ? $row->category : $row->name,
                    ));
                }

                $duplicateimage = $this->categoryimage->get(array(
                    'userid' => getCurrentIdUser(),
                    'categoryname' => $name,
                ));

                if ($duplicateimage->num_rows() > 0) {
                    $row = $duplicateimage->row();

                    $this->categoryimage->delete(array(
                        'id' => $row->id
                    ));
                }

                $categoryimage = $this->categoryimage->total(array(
                    'userid' => getCurrentIdUser(),
                    'categoryname' => $name,
                    'assetid' => $asset,
                ));

                if ($categoryimage == 0) {
                    $this->categoryimage->insert(array(
                        'userid' => getCurrentIdUser(),
                        'categoryname' => $name,
                        'assetid' => $asset,
                    ));
                }
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Kategori gagal diubah');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Kategori berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_manual_product()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $get = $this->mscategory->get(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Kategori tidak ditemukan');
            }

            $this->mscategory->delete(array(
                'id' => $id,
            ));

            $this->categoryimage->delete(array(
                'userid' => getCurrentIdUser(),
                'categoryname' => $get->row()->name,
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Kategori gagal dihapus');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Kategori berhasil dihapus');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function brand_category_product($category)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $category = stringEncryption('decrypt', $category);
        if ($category == null) {
            return redirect(base_url('manage/category/product'));
        }

        $where_vendor = "";
        if (getCurrentUser()->multivendor != 1) {
            $currentapikey = getCurrentVendor('PPOB');
            $where_vendor = "AND vendor = '$currentapikey' AND vendorid IS NULL";
        } else {
            $where_vendor = "AND vendorid IS NOT NULL AND vendorenabled = 1";
        }

        $currentuserid = getCurrentIdUser();

        $validate = $this->db->query("SELECT a.*, bi.assetid, i.asseturl FROM ( SELECT a.id, a.brand, 'msproduct' AS tables, b.id AS disabledid FROM msproduct a LEFT JOIN disabledbrand b ON b.brandname = a.brand AND b.userid = a.userid WHERE a.brand IS NOT NULL AND a.userid = $currentuserid AND category = '$category' $where_vendor AND category_apikey = 'PPOB' GROUP BY brand UNION ALL SELECT a.id, a.NAME AS brand, 'msbrand' AS tables, b.id AS disabledid FROM msbrand a LEFT JOIN disabledbrand b ON b.brandname = a.NAME AND b.userid = a.userid WHERE a.userid = $currentuserid AND a.category = '$category' GROUP BY NAME ) a LEFT JOIN brandimage bi ON bi.brandname = a.brand AND bi.categoryname = '$category' AND bi.userid = $currentuserid LEFT JOIN msicons i ON i.id = bi.assetid ORDER BY a.brand ASC");

        $data = array();
        $data['title'] = 'Brand';
        $data['content'] = 'manage/category/brand';
        $data['category'] = $category;
        $data['result'] = $validate->result();

        return $this->load->view('master', $data);
    }

    public function process_disable_brand_category_product($category)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $brand = getPost('brand');

            if ($brand == null) {
                throw new Exception('Brand tidak boleh kosong');
            }

            $get = $this->disabledbrand->total(array(
                'brandname' => $brand,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                $insert = array();
                $insert['userid'] = getCurrentIdUser();
                $insert['brandname'] = $brand;

                $this->disabledbrand->insert($insert);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Brand gagal dinonaktifkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Brand berhasil dinonaktifkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_enable_brand_category_product($category)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('ID Brand tidak boleh kosong');
            }

            $get = $this->disabledbrand->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Brand tidak ditemukan');
            }

            $this->disabledbrand->delete(array(
                'id' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Brand gagal diaktifkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Brand berhasil diaktifkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_brand_category_product($category)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $category = stringEncryption('decrypt', $category);
            if ($category == null) {
                throw new Exception('Kategori tidak ditemukan');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $get = $this->msbrand->total(array(
                'id' => $id,
                'category' => $category,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Brand tidak ditemukan');
            }

            $this->msbrand->delete(array(
                'id' => $id,
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Brand gagal dihapus');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Brand berhasil dihapus');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit_brand_category_product($category)
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $category = stringEncryption('decrypt', $category);
            if ($category == null) {
                throw new Exception('Kategori tidak ditemukan');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $get = $this->msbrand->get(array(
                'id' => $id,
                'category' => $category,
                'userid' => getCurrentIdUser()
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Brand tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/brand/edit', array(
                    'row' => $row,
                    'category' => $category
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_edit_brand_category_product($category)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $category = stringEncryption('decrypt', $category);
            if ($category == null) {
                throw new Exception('Kategori tidak ditemukan');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);
            $name = getPost('name');

            if ($name == null) {
                throw new Exception('Nama brand tidak boleh kosong');
            } else {
                $name = removeSymbol($name);
            }

            $get = $this->msbrand->total(array(
                'id' => $id,
                'category' => $category,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Brand tidak ditemukan');
            }

            $update = array();
            $update['name'] = $name;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msbrand->update(array(
                'id' => $id,
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Brand gagal diubah');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Brand berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function icon_brand_category_product($category)
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $tb = getPost('tb');

            if ($id == null) {
                throw new Exception('Brand tidak ditemukan');
            }

            if ($tb == null) {
                throw new Exception('Brand tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);
            $tb = stringEncryption('decrypt', $tb);
            $category = stringEncryption('decrypt', $category);

            if ($tb == 'msproduct') {
                if (getCurrentUser()->multivendor != 1) {
                    $currentvendor_ppob = getCurrentVendor('PPOB');
                    $currentvendor_smm = getCurrentVendor('SMM');

                    if ($currentvendor_ppob != null && $currentvendor_smm == null) {
                        $this->msproduct->where('vendor', $currentvendor_ppob);
                    } else if ($currentvendor_ppob == null && $currentvendor_smm != null) {
                        $this->msproduct->where('vendor', $currentvendor_smm);
                    } else if ($currentvendor_ppob != null && $currentvendor_smm != null) {
                        $this->msproduct->where(array(
                            "(vendor = '$currentvendor_ppob' OR vendor = '$currentvendor_smm') =" => true
                        ));
                    }

                    $this->msproduct->where('vendorid', null);
                } else {
                    $this->msproduct->where('vendorid IS NOT NULL');
                    $this->msproduct->where('vendorenabled', 1);
                }

                $get = $this->msproduct->get(array(
                    'brand' => $id,
                    'userid' => getCurrentIdUser(),
                    'category' => $category,
                ));

                if ($get->num_rows() == 0) {
                    throw new Exception('Brand tidak ditemukan');
                }
            } else if ($tb == 'msbrand') {
                $get = $this->msbrand->get(array(
                    'id' => $id,
                    'category' => $category,
                    'userid' => getCurrentIdUser()
                ));

                if ($get->num_rows() == 0) {
                    throw new Exception('Brand tidak ditemukan');
                }
            } else {
                throw new Exception('Brand tidak ditemukan');
            }

            $row = $get->row();

            $data = array();
            $data['brand'] = $row;
            $data['tables'] = $tb;
            $data['icon'] = $this->msicons->order_by('assetname', 'ASC')->result(array(
                'createdby' => getCurrentIdUser()
            ));
            $data['current_icon'] = $this->brandimage->get(array(
                'userid' => getCurrentIdUser(),
                'brandname' => $tb == 'msproduct' ? $row->brand : $row->name,
                'categoryname' => $category,
            ))->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/brand/icon', $data, true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_icon_brand_category_product($category)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('_token');
            $tb = getPost('tb');
            $icon = getPost('asset');

            if ($id == null) {
                throw new Exception('Brand tidak ditemukan');
            }

            if ($tb == null) {
                throw new Exception('Brand tidak ditemukan');
            }

            // Check for icon file upload
            $uploaded_icon = $this->upload_icon_file();
            if ($uploaded_icon !== false) {
                // If file was uploaded, use the new icon ID
                $icon = $uploaded_icon['id'];
            }

            if ($icon == null) {
                throw new Exception('Icon tidak ditemukan');
            }

            $get = $this->msicons->total(array(
                'id' => $icon,
                'createdby' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Icon tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);
            $tb = stringEncryption('decrypt', $tb);
            $category = stringEncryption('decrypt', $category);

            if ($tb == 'msproduct') {
                if (getCurrentUser()->multivendor != 1) {
                    $currentvendor_ppob = getCurrentVendor('PPOB');
                    $currentvendor_smm = getCurrentVendor('SMM');

                    if ($currentvendor_ppob != null && $currentvendor_smm == null) {
                        $this->msproduct->where('vendor', $currentvendor_ppob);
                    } else if ($currentvendor_ppob == null && $currentvendor_smm != null) {
                        $this->msproduct->where('vendor', $currentvendor_smm);
                    } else if ($currentvendor_ppob != null && $currentvendor_smm != null) {
                        $this->msproduct->where(array(
                            "(vendor = '$currentvendor_ppob' OR vendor = '$currentvendor_smm') =" => true
                        ));
                    }

                    $this->msproduct->where('vendorid', null);
                } else {
                    $this->msproduct->where('vendorid IS NOT NULL');
                    $this->msproduct->where('vendorenabled', 1);
                }

                $get = $this->msproduct->get(array(
                    'brand' => $id,
                    'userid' => getCurrentIdUser(),
                    'category' => $category,
                ));

                if ($get->num_rows() == 0) {
                    throw new Exception('Brand tidak ditemukan');
                }

                $brands = $get->row();
            } else if ($tb == 'msbrand') {
                $get = $this->msbrand->get(array(
                    'id' => $id,
                    'category' => $category,
                    'userid' => getCurrentIdUser()
                ));

                if ($get->num_rows() == 0) {
                    throw new Exception('Brand tidak ditemukan');
                }

                $brands = $get->row();
            }

            $get = $this->brandimage->get(array(
                'userid' => getCurrentIdUser(),
                'brandname' => $tb == 'msproduct' ? $brands->brand : $brands->name,
                'categoryname' => $category,
            ));

            $execute = array();
            $execute['userid'] = getCurrentIdUser();
            $execute['assetid'] = $icon;
            $execute['brandname'] = $tb == 'msproduct' ? $brands->brand : $brands->name;
            $execute['categoryname'] = $category;

            if ($get->num_rows() == 0) {
                $execute['createddate'] = getCurrentDate();
                $execute['createdby'] = getCurrentIdUser();

                $this->brandimage->insert($execute);
            } else {
                $execute['updateddate'] = getCurrentDate();
                $execute['updatedby'] = getCurrentIdUser();

                $row = $get->row();

                $this->brandimage->update(array(
                    'id' => $row->id
                ), $execute);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Icon gagal disimpan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Icon berhasil disimpan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    /**
     * Handle icon file upload and save to msicons table
     *
     * @return array|bool Array containing icon ID and URL on success, false on failure
     */
    private function upload_icon_file()
    {
        try {
            // Check if file is uploaded
            if (empty($_FILES['icon_file']['name'])) {
                return false;
            }

            // Set upload configuration
            $config['upload_path'] = './uploads/';
            $config['allowed_types'] = 'jpg|jpeg|png';
            $config['max_size'] = 2048; // 2MB
            $config['encrypt_name'] = true;

            // Create upload path if it doesn't exist
            if (!is_dir($config['upload_path'])) {
                mkdir($config['upload_path'], 0777, true);
            }

            $this->upload->initialize($config);

            // Perform upload
            if (!$this->upload->do_upload('icon_file')) {
                throw new Exception($this->upload->display_errors('', ''));
            }

            // Get uploaded file data
            $uploaded_data = $this->upload->data();

            // Save to msicons table
            $icon_data = array(
                'assetname' => pathinfo($uploaded_data['orig_name'], PATHINFO_FILENAME),
                'asseturl' => $uploaded_data['file_name'],
                'createddate' => getCurrentDate(),
                'createdby' => getCurrentIdUser()
            );

            $this->msicons->insert($icon_data);
            $icon_id = $this->db->insert_id();

            return array(
                'id' => $icon_id,
                'url' => $icon_data['asseturl']
            );
        } catch (Exception $ex) {
            log_message('error', 'Icon upload failed: ' . $ex->getMessage());
            return false;
        }
    }

    public function restore_category_product()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $category = getPost('category');

            if ($category == null) {
                throw new Exception('Kategori tidak boleh kosong');
            }

            $where = array(
                'a.category' => $category,
                'a.userid' => getCurrentIdUser(),
                'a.dontupdatecategory' => 1
            );

            if (getCurrentUser()->multivendor != 1) {
                $apikeysppob = getCurrentAPIKeys('PPOB');
                $apikeyssmm = getCurrentAPIKeys('SMM');

                $where['a.vendorid'] = null;
                if ($apikeysppob != null && $apikeyssmm != null) {
                    $where["(a.vendor = '$apikeysppob->vendor' OR a.vendor = '$apikeyssmm->vendor') ="] = true;
                } else if ($apikeysppob != null && $apikeyssmm == null) {
                    $where['a.vendor'] = $apikeysppob->vendor;
                } else if ($apikeysppob == null && $apikeyssmm != null) {
                    $where['a.vendor'] = $apikeyssmm->vendor;
                }
            } else {
                $where['a.vendorid !='] = null;
                $where['a.vendorenabled'] = 1;
            }

            $get = $this->msproduct->get($where);

            if ($get->num_rows() == 0) {
                throw new Exception('Kategori tidak ditemukan');
            }

            $row = $get->row();

            $update = array();
            $update['category'] = $row->oldcategoryname;
            $update['oldcategoryname'] = null;
            $update['dontupdatecategory'] = null;

            $this->msproduct->update(array(
                'category' => $row->category,
                'userid' => getCurrentIdUser(),
                'dontupdatecategory' => 1
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Kategori gagal dikembalikan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Kategori berhasil dikembalikan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
