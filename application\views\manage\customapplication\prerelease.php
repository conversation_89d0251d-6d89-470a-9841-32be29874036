<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<div class="modal-dialog modal-xl">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Data Email Pre-Release</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <div class="modal-body">
            <form id="frmPrerelease" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                <input type="hidden" name="id" value="<?= $data->id ?>">
                <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                <div class="row">
                    <div class="col-md-5">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Email</label>
                            <input type="email" name="email" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Email" required>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Aksi</label>

                            <div>
                                <button type="submit" class="btn btn-primary">Daftarkan Alamat Email</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <div class="table-responsive">
                <table class="table table-striped table-row-bordered gy-5 datatables-prerelease">
                    <thead>
                        <tr>
                            <th>Tanggal Ditambahkan</th>
                            <th>Email</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>

                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
        </div>
    </div>
</div>

<script>
    $('.datatables-prerelease').DataTable({
        responsive: true,
        processing: true,
        serverSide: true,
        stateSave: true,
        ordering: false,
        ajax: {
            url: '<?= base_url(uri_string() . '/datatables') ?>',
            method: 'POST',
            data: {
                id: '<?= $data->id ?>'
            }
        }
    });

    function deleteEmailPrerelease(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Apakah anda yakin ingin menghapus email ini?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/delete') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id,
                        queueuploadplaystoreid: '<?= $data->id ?>'
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                $('.datatables-prerelease').DataTable().ajax.reload();
                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    $.AjaxRequest('#frmPrerelease', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success',
                }).then(function(result) {
                    $('#frmPrerelease').trigger('reset');
                    $('.datatables-prerelease').DataTable().ajax.reload();
                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.MESSAGE,
                    icon: 'error',
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        }
    });
</script>