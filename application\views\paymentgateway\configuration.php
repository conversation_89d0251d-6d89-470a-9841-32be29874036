<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Konfigurasi: <?= $row->packagename ?></h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmConfiguration" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off" enctype='multipart/form-data'>
            <input type="hidden" name="key" value="<?= stringEncryption('encrypt', $row->id) ?>">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Atas Nama</label>
                    <input type="text" name="accountname" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Atas Nama" value="<?= $row->accountname ?>" required>
                </div>

                <?php if ($row->isqr == 1) : ?>
                    <div class="mb-7" id="display_accountnumber">
                        <label class="col-form-label fw-semibold fs-6 pt-0">QR Code</label>
                        <input type="file" name="qrimage" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" <?= $row->isqr == 1 && $row->qrimage == null ? 'required' : null ?>>
                    </div>
                <?php else : ?>
                    <div class="mb-7" id="display_accountnumber">
                        <label class="col-form-label fw-semibold fs-6 pt-0">Nomor Rekening</label>
                        <input type="number" name="accountnumber" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nomor Rekening" value="<?= $row->accountnumber ?>" <?= $row->isqr != 1 ? 'required' : null ?>>
                    </div>
                <?php endif; ?>
                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Gambar Pembayaran</label>
                    <input type="file" name="paymentimage" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" accept=".jpg,.jpeg,.png" <?= $row->paymentimage == null ? 'required' : null ?>>
                    <small><?= $row->paymentimage != null ? '*Kosongkan jika tidak ingin diubah' : null ?></small>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Minimal Topup</label>
                            <input type="number" name="minnominal" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Minimal Topup" value="<?= $row->minnominal ?>" required>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Maksimal Topup</label>
                            <input type="number" name="maxnominal" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Maksimal Topup" value="<?= $row->maxnominal ?>">
                            <small>*Isi '0' jika tidak ada batas maksimal topup</small>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-check form-check-custom form-check-solid mb-7">
                            <input class="form-check-input" type="checkbox" name="isbonusconfiguration" value="1" id="isbonusconfiguration" <?= $row->isbonus > 0 ? 'checked' : null ?> />

                            <label class="form-check-label" for="isbonusconfiguration">
                                Bonus Deposit
                            </label>
                        </div>
                    </div>
                    <div class="row" id="bonus_depositconfiguration">
                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="" class="col-form-label fw-semibold fs-6 pt-0">Tipe Bonus</label>
                                <select id="bonustypeconfiguration" name="bonustypeconfiguration" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                    <option value="Persentase" <?= $row->bonustype == 'Presentase' ? 'selected' : null ?>>Persentase</option>
                                    <option value="Nominal" <?= $row->bonustype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="" class="col-form-label fw-semibold fs-6 pt-0">Jumlah Bonus</label>
                                <input type="number" name="nominalbonusconfiguration" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah Bonus" value="<?= $row->nominalbonus ?>">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-check form-check-custom form-check-solid mb-7">
                            <input class="form-check-input" type="checkbox" name="isfeeconfiguration" value="1" id="isfeeconfiguration" <?= $row->isfee > 0 ? 'checked' : null ?> />

                            <label class="form-check-label" for="isfeeconfiguration">
                                Biaya Transaksi
                            </label>
                        </div>
                    </div>
                    <div class="row" id="fee_depositconfiguration">
                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="" class="col-form-label fw-semibold fs-6 pt-0">Tipe Biaya</label>
                                <select id="feetypeconfiguration" name="feetypeconfiguration" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                    <option value="Persentase" <?= $row->feetype == 'Presentase' ? 'selected' : null ?>>Persentase</option>
                                    <option value="Nominal" <?= $row->feetype == 'Nominal' ? 'selected' : null ?>>Nominal</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="" class="col-form-label fw-semibold fs-6 pt-0">Jumlah Biaya</label>
                                <input type="number" name="nominalfeeconfiguration" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Jumlah Bonus" value="<?= $row->nominalfee ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class=" modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    if (!$('#isbonusconfiguration').prop('checked')) {
        $('#bonus_depositconfiguration').addClass('d-none').find('input').removeAttr('required').find('select').removeAttr('required');
    }

    if (!$('#isfeeconfiguration').prop('checked')) {
        $('#fee_depositconfiguration').addClass('d-none').find('input').removeAttr('required').find('select').removeAttr('required');
    }
    $.AjaxRequest('#frmConfiguration', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success'
                }).then(function(result2) {
                    return window.location.reload();

                });
            } else {
                return Swal.fire({
                    icon: 'error',
                    title: 'Gagal',
                    text: response.MESSAGE
                });
            }
        },
        error: function() {
            return Swal.fire({
                icon: 'error',
                title: 'Gagal',
                text: 'Server sedang sibuk, silahkan coba beberapa saat lagi.'
            });
        }
    });

    $('#isbonusconfiguration').change(function() {
        if ($(this).prop('checked')) {
            $('#bonus_depositconfiguration').removeClass('d-none').find('input').attr('required', true).find('select').attr('required', true);
        } else {
            $('#bonus_depositconfiguration').addClass('d-none').find('input').removeAttr('required').find('select').removeAttr('required');
        }
    });

    $('#isfeeconfiguration').change(function() {
        if ($(this).prop('checked')) {
            $('#fee_depositconfiguration').removeClass('d-none').find('input').attr('required', true).find('select').attr('required', true);
        } else {
            $('#fee_depositconfiguration').addClass('d-none').find('input').removeAttr('required').find('select').removeAttr('required');
        }
    });
</script>