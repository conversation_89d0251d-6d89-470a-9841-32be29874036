<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MY_Controller extends CI_Controller
{
    public $user;

    public function __construct()
    {
        parent::__construct();

        if (readConfig('./ai9iSFg3aEdUMmFxb1hZOGJVK2pvZz09.cfg', 'maintenance') == 'true') {
            if (uri_string() != 'maintenance') {
                return redirect(base_url('maintenance'));
            }
        }

        if (isLogin()) {
            $currentuser = getCurrentUser();
            $this->user = $currentuser;
        }

        Midtrans\Config::$serverKey = 'Mid-server-wpb5BzZuNyZ51mEK_Mgeu_KO';
        Midtrans\Config::$isProduction = true;
    }
}
