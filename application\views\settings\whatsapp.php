<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">WhatsApp</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Akun</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">WhatsApp</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <!--begin::Alert-->
    <div class="alert alert-primary d-flex align-items-center p-5">
        <!--begin::Svg Icon -->
        <span class="svg-icon svg-icon-2hx svg-icon-primary me-3">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.3" d="M12 22C13.6569 22 15 20.6569 15 19C15 17.3431 13.6569 16 12 16C10.3431 16 9 17.3431 9 19C9 20.6569 10.3431 22 12 22Z" fill="currentColor" />
                <path d="M19 15V18C19 18.6 18.6 19 18 19H6C5.4 19 5 18.6 5 18V15C6.1 15 7 14.1 7 13V10C7 7.6 8.7 5.6 11 5.1V3C11 2.4 11.4 2 12 2C12.6 2 13 2.4 13 3V5.1C15.3 5.6 17 7.6 17 10V13C17 14.1 17.9 15 19 15ZM11 10C11 9.4 11.4 9 12 9C12.6 9 13 8.6 13 8C13 7.4 12.6 7 12 7C10.3 7 9 8.3 9 10C9 10.6 9.4 11 10 11C10.6 11 11 10.6 11 10Z" fill="currentColor" />
            </svg>
        </span>
        <!--end::Svg Icon-->

        <!--begin::Wrapper-->
        <div class="d-flex flex-column">
            <!--begin::Title-->
            <h4 class="mb-1 text-primary">Informasi</h4>
            <!--end::Title-->

            <!--begin::Content-->
            <span>Untuk saat ini WhatsApp anda akan digunakan sebagai Notifikasi kepada member yang telah melakukan pengisian field Nomor Handphone. Setiap transaksi member akan mendapatkan notifikasi untuk menginformasikan status transaksi.</span>
            <!--end::Content-->
        </div>
        <!--end::Wrapper-->
    </div>
    <!--end::Alert-->

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Konfigurasi WhatsApp</h3>
                    </div>
                </div>

                <form id="frmConfiguration" action="<?= base_url(uri_string() . '/configuration') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Vendor</label>
                            <select name="vendor" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                <option value="">- Pilih -</option>
                                <option value="WABlasinGateway" <?= $apikeys_whatsapp != null && $apikeys_whatsapp->vendor == 'WABlasinGateway' ? 'selected' : null ?>>WABlasinGateway</option>
                            </select>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">API Key</label>
                            <input type="text" name="apikey" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan API Key" value="<?= $apikeys_whatsapp != null ? stringEncryption('decrypt', $apikeys_whatsapp->apikey) : null ?>" required>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Device Code</label>
                            <input type="text" name="devicecode" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Device Code" value="<?= $apikeys_whatsapp != null ? stringEncryption('decrypt', $apikeys_whatsapp->devicecode) : null ?>" required>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Informasi Vendor</h3>
                    </div>
                </div>

                <div class="card-body">
                    <table class="table table-striped table-row-bordered gy-5 datatables-profit text-nowrap">
                        <thead>
                            <tr>
                                <th>Vendor</th>
                                <th>Website</th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr>
                                <td>WABlasinGateway</td>
                                <td>
                                    <a href="https://wablasingateway.karpeldevtech.com/" target="_blank">Visit</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Format Notifikasi Transaksi</h3>
                    </div>
                </div>

                <div class="card-body">
                    <form id="frmFormatNotification" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                        <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Notifikasi Pesan Transaksi Berhasil</label>
                            <textarea name="success_notification" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Terimakasih telah melakukan transaksi di KARPELDEVTECHPEDIA&#10;&#10;Produk: ${PRODUCT}&#10;Harga: ${PRICE}&#10;..." rows="10"><?= isset($whatsapp_notification->success) ? $whatsapp_notification->success : null ?></textarea>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Notifikasi Pesan Transaksi Pending</label>
                            <textarea name="pending_notification" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Terimakasih telah melakukan transaksi di KARPELDEVTECHPEDIA&#10;&#10;Transaksi anda sedang diproses, Berikut adalah informasi detail mengenai pesanan anda&#10;&#10;Produk: ${PRODUCT}&#10;Harga: ${PRICE}&#10;..." rows="10"><?= isset($whatsapp_notification->pending) ? $whatsapp_notification->pending : null ?></textarea>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Notifikasi Pesan Transaksi Gagal</label>
                            <textarea name="failed_notification" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Mohon maaf transaksi anda di KARPELDEVTECHPEDIA gagal!&#10;&#10;Berikut adalah informasi detail mengenai pesanan anda yang gagal&#10;&#10;Produk: ${PRODUCT}&#10;Harga: ${PRICE}&#10;..." rows="10"><?= isset($whatsapp_notification->failed) ? $whatsapp_notification->failed : null ?></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">Simpan</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Parameter Pesan Transaksi</h3>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-rounded table-striped border gy-7 gs-7">
                            <thead>
                                <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                                    <th>Parameter</th>
                                    <th>Penjelasan</th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr>
                                    <td class="fw-bold">${CODE}</td>
                                    <td>Menampilkan kode transaksi</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${TARGET}</td>
                                    <td>Menampilkan target/tujuan layanan dikirim</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${PRICE}</td>
                                    <td>Menampilkan harga produk yang dipesan</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${BALANCE_BEFORE}</td>
                                    <td>Menampilkan jumlah saldo sebelum dipotong transaksi</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${BALANCE_AFTER}</td>
                                    <td>Menampilkan jumlah saldo setelah dipotong transaksi</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${STATUS}</td>
                                    <td>Menampilkan status transaksi</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${SN}</td>
                                    <td>Menampilkan SN (Khusus untuk pembelian kategori PPOB)</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${PRODUCT}</td>
                                    <td>Menampilkan nama produk yang dipesan</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${QTY}</td>
                                    <td>Menampilkan jumlah pengisian (Khusus untuk pembelian kategori SMM)</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Parameter Pesan Deposit</h3>
                    </div>
                </div>

                <div class="card-body">
                    <form id="frmFormatDepositNotification" action="<?= base_url(uri_string() . '/process/deposit') ?>" method="POST" autocomplete="off">
                        <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                        <div class="mb-7">
                            <label for="success_notification" class="col-form-label fw-semibold fs-6 pt-0">Notifikasi Pesan Deposit Berhasil</label>
                            <textarea name="success_notification" id="success_notification" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Terimakasih telah melakukan deposit di KARPELDEVTECHPEDIA&#10;&#10;Jumlah Deposit: ${AMOUNT}&#10;..." rows="10"><?= $whatsapp_notification->deposit_success ?? null ?></textarea>
                        </div>

                        <div class="mb-7">
                            <label for="pending_notification" class="col-form-label fw-semibold fs-6 pt-0">Notifikasi Pesan Deposit Pending</label>
                            <textarea name="pending_notification" id="pending_notification" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Terimakasih telah melakukan deposit di KARPELDEVTECHPEDIA&#10;&#10;Segera lakukan pembayaran, Berikut adalah informasi detail mengenai deposit anda&#10;&#10;Jumlah Deposit: ${AMOUNT}&#10;..." rows="10"><?= $whatsapp_notification->deposit_pending ?? null ?></textarea>
                        </div>

                        <div class="mb-7">
                            <label for="failed_notification" class="col-form-label fw-semibold fs-6 pt-0">Notifikasi Pesan Deposit Kadaluarsa/Gagal</label>
                            <textarea name="failed_notification" id="failed_notification" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Mohon maaf deposit anda di KARPELDEVTECHPEDIA gagal!&#10;&#10;Berikut adalah informasi detail mengenai deposit anda yang gagal&#10;&#10;Jumlah Deposit: ${AMOUNT}&#10;..." rows="10"><?= $whatsapp_notification->deposit_failed ?? null ?></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">Simpan</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Parameter Pesan Deposit</h3>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-rounded table-striped border gy-7 gs-7">
                            <thead>
                                <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                                    <th>Parameter</th>
                                    <th>Penjelasan</th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr>
                                    <td class="fw-bold">${PAYMENT}</td>
                                    <td>Menampilkan metode pembayaran yang dipilih saat mengajukan deposit</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${AMOUNT}</td>
                                    <td>Menampilkan jumlah deposit yang diajukan</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${NOTE}</td>
                                    <td>Menampilkan catatan pembayaran</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${CODE}</td>
                                    <td>Menampilkan kode transaksi deposit</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${PAYMENTTYPE}</td>
                                    <td>Menampilkan tipe pembayaran yang dipilih saat mengajukan deposit</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${BONUS}</td>
                                    <td>Menampilkan nominal bonus yang didapatkan</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${FEE}</td>
                                    <td>Menampilkan biaya admin yang dikenakan</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${STATUS}</td>
                                    <td>Menampilkan status deposit</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.addEventListener('load', function() {
        $.AjaxRequest('#frmFormatNotification', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function() {
                        return window.location.reload();
                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error'
                });
            }
        });

        $.AjaxRequest('#frmFormatDepositNotification', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function() {
                        return window.location.reload();
                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error'
                });
            }
        });

        $.AjaxRequest('#frmConfiguration', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function() {
                        return window.location.reload();
                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error'
                });
            }
        });
    });
</script>