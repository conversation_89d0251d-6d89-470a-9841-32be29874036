<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Pembelian Paket Payment Otomatis: <?= $row->packagename ?></h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmBuy" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <input type="hidden" name="key" value="<?= stringEncryption('encrypt', $row->id) ?>">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <table class="table table-striped table-row-bordered gy-5">
                    <tr>
                        <th>Nama</th>
                        <td><?= $row->packagename ?></td>
                    </tr>

                    <tr>
                        <th>Untuk</th>
                        <td><?= getCurrentUser()->email ?></td>
                    </tr>

                    <tr>
                        <th>Saldo</th>
                        <td>Rp <?= IDR(getCurrentUser()->balance) ?></td>
                    </tr>

                    <tr>
                        <th>Harga</th>
                        <td>Rp <?= IDR($row->price) ?></td>
                    </tr>
                </table>

                <?php if (getCurrentBalance() < $row->price) : ?>
                    <!--begin::Alert-->
                    <div class="alert alert-danger d-flex align-items-center p-5">
                        <!--begin::Icon-->
                        <span class="svg-icon svg-icon-2hx svg-icon-danger me-3">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="5" fill="currentColor" />
                                <rect x="7" y="15.3137" width="12" height="2" rx="1" transform="rotate(-45 7 15.3137)" fill="currentColor" />
                                <rect x="8.41422" y="7" width="12" height="2" rx="1" transform="rotate(45 8.41422 7)" fill="currentColor" />
                            </svg>
                        </span>
                        <!--end::Icon-->

                        <!--begin::Wrapper-->
                        <div class="d-flex flex-column">
                            <!--begin::Content-->
                            <span>Saldo anda tidak mencukupi untuk membeli paket ini, Silahkan <b><a href="<?= base_url('deposit/topup') ?>">Topup Saldo</a></b> anda terlebih dahulu.</span>
                            <!--end::Content-->
                        </div>
                        <!--end::Wrapper-->
                    </div>
                    <!--end::Alert-->
                <?php endif; ?>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                <?php if (getCurrentBalance() >= $row->price) : ?>
                    <button type="submit" class="btn btn-primary">Beli</button>
                <?php endif; ?>
            </div>
        </form>
    </div>
</div>

<script>
    $.AjaxRequest('#frmBuy', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success'
                }).then(function(result2) {
                    return window.location.reload();

                });
            } else {
                return Swal.fire({
                    icon: 'error',
                    title: 'Gagal',
                    text: response.MESSAGE
                });
            }
        },
        error: function() {
            return Swal.fire({
                icon: 'error',
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti'
            });
        }
    });
</script>