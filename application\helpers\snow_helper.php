<?php
defined('BASEPATH') or die('No direct script access allowed!');

class SMMSnow
{
    private $_apikey;

    public function __construct($apikey)
    {
        $this->_apikey = $apikey;
    }

    public function services()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://smmsnow.in/api/v2");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_apikey&action=services");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }
}
