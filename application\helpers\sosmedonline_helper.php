<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SosmedOnline
{
    private $_apiid;
    private $_apikey;

    public function __construct($apiid, $apikey)
    {
        $this->_apiid = $apiid;
        $this->_apikey = $apikey;
    }

    public function service()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://api.sosmedonline.com/services");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_id=$this->_apiid&api_key=$this->_apikey");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function profile()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://api.sosmedonline.com/profile");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_id=$this->_apiid&api_key=$this->_apikey");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function order($service, $target, $quantity, $other = null)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://api.sosmedonline.com/order");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_id=$this->_apiid&api_key=$this->_apikey&service=$service&target=$target&quantity=$quantity&custom_comments=$other&custom_link=$other");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function status($id)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://api.sosmedonline.com/status");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_id=$this->_apiid&api_key=$this->_apikey&id=$id");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }
}
