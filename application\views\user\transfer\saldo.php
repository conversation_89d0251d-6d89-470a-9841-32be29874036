<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Transfer Saldo</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Pengguna</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Transfer Saldo</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Transfer Saldo</h3>
                    </div>
                </div>

                <form id="frmTransferSaldo" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Email Tujuan</label>
                            <input type="email" name="email" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Email Tujuan" required>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Nominal Saldo</label>
                            <input type="number" name="nominal" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nominal Saldo" required>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Transfer Saldo</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Riwayat Transfer Saldo</h3>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-row-bordered gy-5 datatables-saldo">
                            <thead>
                                <tr>
                                    <th>Tanggal Transfer</th>
                                    <th>Email Tujuan</th>
                                    <th>Nama</th>
                                    <th>Nominal</th>
                                </tr>
                            </thead>

                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('.datatables-saldo').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
            }
        });

        $('#frmTransferSaldo').submit(function(e) {
            e.preventDefault();

            let email = $('[name="email"]').val();
            let nominal = $('[name="nominal"]').val();

            if (email == null) {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Email tidak boleh kosong',
                    icon: 'error'
                });
            } else if (nominal == null) {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Nominal tidak boleh kosong',
                    icon: 'error'
                });
            }

            return Swal.fire({
                title: 'Konfirmasi',
                text: 'Apakah anda yakin ingin mentransfer saldo ' + nominal + ' ke email ' + email + ' ?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Ya',
                cancelButtonText: 'Tidak'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: $(this).attr('action'),
                        method: $(this).attr('method'),
                        dataType: 'json',
                        data: {
                            email: email,
                            nominal: nominal
                        },
                        success: function(response) {
                            if (response.RESULT == 'OK') {
                                $('#ModalGlobal').html(response.CONTENT);
                                $('#ModalGlobal').modal('show');
                            } else {
                                return Swal.fire({
                                    title: 'Gagal',
                                    text: response.MESSAGE,
                                    icon: 'error'
                                });
                            }
                        }
                    }).fail(function() {
                        return Swal.fire({
                            title: 'Gagal',
                            text: 'Server sedang sibuk silahkan coba lagi nanti',
                            icon: 'error'
                        });
                    })
                }
            });
        });
    };
</script>