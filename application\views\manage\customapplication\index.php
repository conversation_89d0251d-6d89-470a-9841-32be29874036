<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Custom Aplikasi</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Management</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Custom Aplikasi</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <?php if ($customapp != null && getCurrentUser()->unlock_playstore == 1 && $currentqueueuploadplaystore != null) : ?>
        <!--begin::Alert-->
        <div class="alert alert-info d-flex align-items-center p-5">
            <!--begin::Icon-->
            <span class="svg-icon svg-icon-2hx svg-icon-info me-3">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.3" d="M12 22C13.6569 22 15 20.6569 15 19C15 17.3431 13.6569 16 12 16C10.3431 16 9 17.3431 9 19C9 20.6569 10.3431 22 12 22Z" fill="currentColor" />
                    <path d="M19 15V18C19 18.6 18.6 19 18 19H6C5.4 19 5 18.6 5 18V15C6.1 15 7 14.1 7 13V10C7 7.6 8.7 5.6 11 5.1V3C11 2.4 11.4 2 12 2C12.6 2 13 2.4 13 3V5.1C15.3 5.6 17 7.6 17 10V13C17 14.1 17.9 15 19 15ZM11 10C11 9.4 11.4 9 12 9C12.6 9 13 8.6 13 8C13 7.4 12.6 7 12 7C10.3 7 9 8.3 9 10C9 10.6 9.4 11 10 11C10.6 11 11 10.6 11 10Z" fill="currentColor" />
                </svg>
            </span>
            <!--end::Icon-->

            <!--begin::Wrapper-->
            <div class="d-flex flex-column">
                <!--begin::Title-->
                <h4 class="mb-1 text-info">Informasi Penting!</h4>
                <!--end::Title-->

                <!--begin::Content-->
                <span>Sehubungan dengan kebijakan baru Google Play, semua member diwajibkan melakukan pendaftaran minimal 20 akun email tester sebelum mengajukan rilis ke publik. Klik <b>Daftarkan Email Pre-Release</b> pada riwayat permintaan upload playstore.</span>
                <!--end::Content-->
            </div>
            <!--end::Wrapper-->
        </div>
        <!--end::Alert-->
    <?php endif; ?>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-5">
                <form id="frmCustomAplikasi" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Nama Aplikasi</label>
                            <input type="text" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Nama Aplikasi" id="name" name="name" value="<?= $customapp->appname ?? null ?>" required>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Package Name</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">com.serverppobsmm.</span>
                                </div>

                                <input type="text" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Package Name" id="packagename" name="packagename" value="<?= $customapp->packagename ?? null ?>" required>
                            </div>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Color Picker</label>
                            <input type="color" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Color picker" id="color" name="color" value="<?= $customapp->color ?? null ?>" required>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <?php if ($customapp != null && getCurrentUser()->unlock_playstore == 1) : ?>
                            <button type="button" class="btn btn-success me-1" onclick="pengajuanUploadPlaystore()" <?= $currentqueueuploadplaystore != null ? 'disabled' : null ?>>Pengajuan Playstore</button>
                        <?php endif; ?>

                        <?php if ($customapp != null) : ?>
                            <button type="button" class="btn btn-warning me-1" onclick="modalBuildApp()" <?= $currentqueue != null ? 'disabled' : null ?>>Build Aplikasi</button>
                        <?php endif; ?>

                        <button type="submit" class="btn btn-primary" <?= $currentqueue != null ? 'disabled' : null ?>>Simpan Konfigurasi</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Informasi Aplikasi Anda</h3>
                    </div>
                </div>

                <div class="card-body">
                    <table class="table">
                        <tbody>
                            <tr>
                                <th>Versi Aplikasi Anda</th>
                                <th><?= $customapp->app_version ?? '<span class="badge bg-danger">Belum pernah Release</span>' ?></th>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Download Aplikasi</h3>
                    </div>
                </div>

                <div class="card-body">
                    <?php if (($customapp->apk_link ?? null) != null || ($customapp->appbundle_link ?? null) != null) : ?>
                        <table class="table">
                            <tbody>
                                <?php if ($customapp->apk_link) : ?>
                                    <tr>
                                        <th>Android APK</th>
                                        <td>
                                            <a href="https://<?= getCurrentUser()->domain ?>/uploads/<?= $customapp->apk_link ?>" target="_blank" class="btn btn-primary btn-sm">Download</a>
                                        </td>
                                    </tr>
                                <?php endif; ?>

                                <?php if ($customapp->appbundle_link != null) : ?>
                                    <tr>
                                        <th>Android App Bundle (untuk Google Play Console)</th>
                                        <td>
                                            <a href="https://<?= getCurrentUser()->domain ?>/uploads/<?= $customapp->appbundle_link ?>" class="btn btn-primary btn-sm">Download</a>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    <?php else : ?>
                        <div class="alert alert-warning d-flex align-items-center p-5">
                            <!--begin::Icon-->
                            <span class="svg-icon svg-icon-2hx svg-icon-warning me-3">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path opacity="0.3" d="M12 22C13.6569 22 15 20.6569 15 19C15 17.3431 13.6569 16 12 16C10.3431 16 9 17.3431 9 19C9 20.6569 10.3431 22 12 22Z" fill="currentColor" />
                                    <path d="M19 15V18C19 18.6 18.6 19 18 19H6C5.4 19 5 18.6 5 18V15C6.1 15 7 14.1 7 13V10C7 7.6 8.7 5.6 11 5.1V3C11 2.4 11.4 2 12 2C12.6 2 13 2.4 13 3V5.1C15.3 5.6 17 7.6 17 10V13C17 14.1 17.9 15 19 15ZM11 10C11 9.4 11.4 9 12 9C12.6 9 13 8.6 13 8C13 7.4 12.6 7 12 7C10.3 7 9 8.3 9 10C9 10.6 9.4 11 10 11C10.6 11 11 10.6 11 10Z" fill="currentColor" />
                                </svg>
                            </span>
                            <!--end::Icon-->

                            <!--begin::Wrapper-->
                            <div class="d-flex flex-column">
                                <!--begin::Title-->
                                <h4 class="mb-1 text-warning">Belum tersedia</h4>
                                <!--end::Title-->
                            </div>
                            <!--end::Wrapper-->
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Riwayat Permintaan Build Aplikasi</h3>
                    </div>
                </div>

                <div class="card-body">
                    <table class="table table-striped table-row-bordered gy-5 datatables">
                        <thead>
                            <tr>
                                <th>Tanggal Permintaan</th>
                                <th>Tanggal di Perbarui</th>
                                <th>Tipe Aplikasi</th>
                                <th>Status</th>
                            </tr>
                        </thead>

                        <tbody>
                            <?php foreach ($historyqueue as $key => $value) : ?>
                                <tr>
                                    <td><?= DateFormat($value->createddate, 'd F Y H:i:s') ?></td>
                                    <td><?= DateFormat($value->updateddate, 'd F Y H:i:s') ?></td>
                                    <td><?= $value->applicationtype ?? 'apk' ?></td>
                                    <td>
                                        <?php if ($value->status == 'Success') : ?>
                                            <span class="badge badge-success">Success</span>
                                        <?php elseif ($value->status == 'Pending') : ?>
                                            <span class="badge badge-warning">Pending</span>
                                        <?php elseif ($value->status == 'Failed') : ?>
                                            <span class="badge badge-danger">Failed</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Riwayat Permintaan Upload Playstore</h3>
                    </div>
                </div>

                <div class="card-body">
                    <table class="table table-striped table-row-bordered gy-5 datatables">
                        <thead>
                            <tr>
                                <th>Tanggal Permintaan</th>
                                <th>Tanggal di Perbarui</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>

                        <tbody>
                            <?php foreach ($historyqueueplaystore as $key => $value) : ?>
                                <tr>
                                    <td><?= DateFormat($value->createddate, 'd F Y H:i:s') ?></td>
                                    <td><?= $value->updateddate != null ? DateFormat($value->updateddate, 'd F Y H:i:s') : '-' ?></td>
                                    <td>
                                        <?php if ($value->status == 'Success') : ?>
                                            <span class="badge badge-success">Berhasil</span>
                                        <?php elseif ($value->status == 'Pending') : ?>
                                            <span class="badge badge-warning">Menunggu</span>
                                        <?php elseif ($value->status == 'Failed') : ?>
                                            <span class="badge badge-danger">Gagal</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($value->status == 'Failed') : ?>
                                            <button type="button" onclick="modalReason(<?= $value->id ?>)" class="btn btn-danger btn-sm">
                                                <i class="fa fa-book mr-2"></i> Alasan
                                            </button>
                                        <?php else : ?>
                                            <?php if ($value->status == 'Pending'): ?>
                                                <button type="button" class="btn btn-warning btn-sm" onclick="registerPreRelease(<?= $value->id ?>)">
                                                    <i class="fa fa-users"></i> Daftarkan Email Pre-Release
                                                </button>
                                            <?php else: ?>
                                                N/A
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('#frmCustomAplikasi').submit(function(e) {
            e.preventDefault();

            let formData = new FormData(this);
            let elementsForm = $(this).find('button, textarea, input');

            elementsForm.attr('disabled');

            $.ajax({
                url: $(this).attr('action'),
                method: $(this).attr('method'),
                dataType: 'json',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    elementsForm.removeAttr('disabled');

                    if (response.RESULT == 'OK') {
                        return Swal.fire({
                            title: 'Berhasil',
                            text: response.MESSAGE,
                            icon: 'success'
                        }).then(function(result) {
                            return window.location.reload();

                        });
                    } else {
                        return Swal.fire({
                            title: 'Gagal',
                            text: response.MESSAGE,
                            icon: 'error'
                        });
                    }
                }
            }).fail(function() {
                elementsForm.removeAttr('disabled');

                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error'
                });
            });
        });
    };

    function modalBuildApp() {
        $.ajax({
            url: '<?= base_url(uri_string() . '/request') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    }

    function modalReason(id) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/reason') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        })
    }

    function pengajuanUploadPlaystore() {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Apakah anda yakin ingin mengajukan upload aplikasi ke Playstore?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/requestuploadplaystore/process') ?>',
                    method: 'POST',
                    dataType: 'json',
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function registerPreRelease(id) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/prerelease') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    }
</script>