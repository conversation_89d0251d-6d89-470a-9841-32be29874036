<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3"><?= $category ?></h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Management</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Kategori Produk</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500"><?= $category ?></li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <div>
        <a href="<?= base_url('manage/category/product') ?>" class="btn btn-danger fw-bold">Kembali</a>
        <a href="javascript:;" onclick="addBrand()" class="btn btn-dark fw-bold">Tambah Brand</a>
    </div>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-body">
                    <table class="table table-striped table-row-bordered gy-5 datatables-brand">
                        <thead>
                            <tr class="fw-semibold fs-6 text-muted">
                                <th>Icon</th>
                                <th>Nama Brand</th>
                                <th>Brand From</th>
                                <th>Kategori Produk</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>

                        <tbody>
                            <?php foreach ($result as $key => $value) : ?>
                                <tr>
                                    <td>
                                        <?php if (!empty($value->asseturl)) : ?>
                                            <img src="<?= base_url('uploads/' . $value->asseturl) ?>" alt="<?= $value->brand ?>" class="img-fluid" style="max-width: 40px; max-height: 40px;">
                                        <?php else : ?>
                                            <span class="badge badge-light-primary">No Icon</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= $value->brand ?></td>
                                    <td>
                                        <?php if ($value->tables == 'msbrand') : ?>
                                            Manual
                                        <?php else : ?>
                                            Vendor
                                        <?php endif; ?>
                                    </td>
                                    <td><?= $category ?></td>
                                    <td>
                                        <a href="javascript:;" class="btn btn-icon btn-warning btn-sm mb-1" onclick="editIcon('<?= stringEncryption('encrypt', $value->tables == 'msbrand' ? $value->id : $value->brand) ?>', '<?= stringEncryption('encrypt', $value->tables) ?>')">
                                            <i class="fa fa-image"></i>
                                        </a>

                                        <?php if ($value->tables == 'msbrand') : ?>
                                            <a href="javascript:;" class="btn btn-icon btn-warning btn-sm mb-1" onclick="editBrand('<?= stringEncryption('encrypt', $value->id) ?>')">
                                                <i class="fa fa-edit"></i>
                                            </a>

                                            <a href="javascript:;" class="btn btn-icon btn-danger btn-sm mb-1" onclick="deleteBrand('<?= stringEncryption('encrypt', $value->id) ?>')">
                                                <i class="fa fa-trash"></i>
                                            </a>
                                        <?php endif; ?>

                                        <?php if ($value->disabledid == null) : ?>
                                            <button type="button" class="btn btn-danger btn-sm mb-1 me-1" onclick="disableBrand('<?= $value->brand ?>')">
                                                <i class="fa fa-times"></i>
                                                <span>Disable</span>
                                            </button>
                                        <?php elseif ($value->disabledid != null) : ?>
                                            <button type="button" class="btn btn-primary btn-sm mb-1 me-1" onclick="enableBrand('<?= $value->disabledid ?>', '<?= $value->brand ?>')">
                                                <i class="fa fa-check"></i>
                                                <span>Enable</span>
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('.datatables-brand').DataTable({
            responsive: true,
            stateSave: true,
            ordering: false,
        });
    };

    function editIcon(id, tb) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/icon') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id,
                tb: tb,
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    }

    function editBrand(id) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/edit') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        })
    }

    function deleteBrand(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Data yang dihapus tidak dapat dikembalikan',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/delete') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function disableBrand(brand) {
        return Swal.fire({
            title: 'Pernyataan',
            text: `Apakah anda yakin ingin menonaktifkan brand ${brand}?`,
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/disable') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        brand: brand
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function enableBrand(disabledid, brand) {
        return Swal.fire({
            title: 'Pernyataan',
            text: `Apakah anda yakin ingin mengaktifkan brand ${brand}?`,
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/enable') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: disabledid
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function addBrand() {
        $.ajax({
            url: '<?= base_url(uri_string() . '/add') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    }
</script>