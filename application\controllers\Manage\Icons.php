<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsIcons $icons
 * @property CI_Upload $upload
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver|CI_DB_query_builder $db
 */
class Icons extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsIcons', 'icons');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Asset Icon';
        $data['content'] = 'manage/icons/index';

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tambah Asset Icon';
        $data['content'] = 'manage/icons/add';

        return $this->load->view('master', $data);
    }

    public function process_add_icons()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $nama = getPost('nama');
            $icon = isset($_FILES['icon']) ? $_FILES['icon'] : null;

            if (empty(strlen($nama))) {
                throw new Exception('Nama Asset tidak boleh kosong.');
            }

            $config = array(
                'upload_path' => './uploads/',
                'allowed_types' => 'jpg|jpeg|png',
                'encrypt_name' => true,
            );

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('icon')) {
                throw new Exception($this->upload->display_errors('', ''));
            } else {
                $icon = $this->upload->data();

                $data = array(
                    'assetname' => $nama,
                    'asseturl' => $icon['file_name'],
                    'createddate' => getCurrentDate(),
                    'createdby' => getCurrentIdUser(),
                );

                $this->icons->insert($data);

                if ($this->db->trans_status() === FALSE) {
                    throw new Exception('Gagal menambahkan Icons!');
                }

                $this->db->trans_commit();

                return JSONResponseDefault('OK', 'Icons berhasil ditambahkan!');
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_icons()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();

                $datatables = $this->datatables->make('MsIcons', 'QueryDatatables', 'SearchDatatables');

                foreach (
                    $datatables->getData(array(
                        'createdby' => getCurrentIdUser()
                    )) as $key => $value
                ) {
                    $detail = array();
                    $detail[] = $value->assetname;
                    $detail[] = "<img src=\"" . base_url('uploads/' . $value->asseturl) . "\" width=\"25\">";
                    $detail[] = "<a href=\"" . base_url('manage/icons/edit/' . $value->id) . "\") class=\"btn btn-icon btn-warning btn-sm mb-1\">
                        <i class=\"fa fa-edit\"></i>
                    </a>

                    <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm  mb-1\" onclick=\"deleteIcons('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-trash\"></i>
                    </a>";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Anda tidak memiliki akses!');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->icons->get(array(
            'id = ' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/icons'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Ubah Asset Icon';
        $data['content'] = 'manage/icons/edit';
        $data['icons'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit_icons($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $get = $this->icons->get(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Icons tidak ditemukan.');
            }

            $row = $get->row();

            $nama = getPost('nama');
            $icon = isset($_FILES['icon']) ? $_FILES['icon'] : null;

            if (empty(strlen($nama))) {
                throw new Exception('Nama Asset tidak boleh kosong.');
            }

            $config = array(
                'upload_path' => './uploads/',
                'allowed_types' => 'jpg|jpeg|png',
                'encrypt_name' => true,
            );

            $this->load->library('upload', $config);

            $update = array();
            if ($this->upload->do_upload('icon')) {;
                $icon = $this->upload->data();
                $update['asseturl'] = $icon['file_name'];

                if (file_exists('./uploads/' . $row->asseturl)) {
                    @unlink('./uploads/' . $row->asseturl);
                }
            }

            $update['assetname'] = $nama;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->icons->update(array(
                'id = ' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah Icons!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Icons berhasil diubah!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_icons()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = stringEncryption('decrypt', getPost('id'));

            $icons = $this->icons->get(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($icons->num_rows() == 0) {
                throw new Exception('Icons tidak ditemukan!');
            }

            $row = $icons->row();

            if (file_exists('./uploads/' . $row->asseturl)) {
                @unlink('./uploads/' . $row->asseturl);
            }

            $this->icons->delete(array(
                'id' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus Icons!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Icons berhasil dihapus!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
