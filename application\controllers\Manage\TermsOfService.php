<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TermsOfService extends MY_Controller
{
    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $main = null;
        $member = null;

        if (file_exists('./application/templates/termsofservice_main.txt')) {
            $main = file_get_contents('./application/templates/termsofservice_main.txt');
        }

        if (file_exists('./application/templates/termsofservice_member.txt')) {
            $member = file_get_contents('./application/templates/termsofservice_member.txt');
        }

        $data = array();
        $data['title'] = 'Syarat dan Ketentuan';
        $data['content'] = 'manage/termsofservice/index';
        $data['main'] = $main;
        $data['member'] = $member;

        return $this->load->view('master', $data);
    }

    public function process()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isAdmin()) {
                throw new Exception('Access denied');
            }

            $main = getPost('termsofservice_main');
            $member = getPost('termsofservice_member');

            if ($main == null) {
                throw new Exception('Syarat dan Ketentuan Server PPOB & SMM tidak boleh kosong');
            }

            if ($member == null) {
                throw new Exception('Syarat dan Ketentuan Member tidak boleh kosong');
            }

            file_put_contents(APPPATH . 'templates/termsofservice_main.txt', $main);
            file_put_contents(APPPATH . 'templates/termsofservice_member.txt', $member);

            return JSONResponseDefault('OK', 'Berhasil mengubah Syarat dan Ketentuan');
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
