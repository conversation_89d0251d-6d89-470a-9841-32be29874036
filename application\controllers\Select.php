<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsProduct $msproduct
 * @property MsDatabaseAccount $msdatabaseaccount
 * @property MsStockproduct $msstockproduct
 * @property MsStockproductDetail $msstockproductdetail
 */
class Select extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('MsDatabaseAccount', 'msdatabaseaccount');
        $this->load->model('MsStockproduct', 'msstockproduct');
        $this->load->model('MsStockproductDetail', 'msstockproductdetail');
    }

    public function category()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Anda belum login');
            }

            $vendor = getPost('vendorid');
            $category_apikey = getPost('category_apikey', 'PPOB');

            if (empty($vendor)) {
                throw new Exception('Vendor tidak boleh kosong');
            }

            $category = $this->msproduct->select('a.category')
                ->where('a.userid', getCurrentIdUser())
                ->where('a.category_apikey', $category_apikey)
                ->where('a.category IS NOT NULL')
                ->where("(a.vendorid = '$vendor' OR a.vendor = '$vendor') = ", true)
                ->group_by('a.category')
                ->order_by('a.category', 'ASC')
                ->result();

            $categories = array();
            foreach ($category as $key => $value) {
                $categories[] = $value->category;
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'DATA' => $categories
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function brand()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Anda belum login');
            }

            $category = getPost('category');
            $vendorid = getPost('vendorid');
            $subcategory_apikey = getPost('subcategory_apikey');

            $where_vendor = "";
            if ($vendorid == null) {
                if (getCurrentUser()->multivendor != 1) {
                    $vendor = getCurrentVendor('PPOB', getCurrentIdUser());
                    $where_vendor = "AND vendor = '$vendor' AND vendorid IS NULL";
                } else {
                    $where_vendor = "AND vendorid IS NOT NULL AND vendorenabled = 1";
                }
            } else {
                $where_vendor = "AND vendor = '$vendorid'";
            }

            if ($category != 'All' && $category != null) {
                $brand = $this->db->query("SELECT * FROM (SELECT brand FROM msproduct WHERE userid = '" . getCurrentIdUser() . "' $where_vendor AND category = '$category' AND brand IS NOT NULL GROUP BY brand UNION SELECT name AS brand FROM msbrand WHERE userid = '" . getCurrentIdUser() . "' AND category = '$category') a ORDER BY a.brand ASC")->result();
            } else {
                if ($subcategory_apikey == null) {
                    $brand = $this->db->query("SELECT * FROM (SELECT brand FROM msproduct WHERE userid = '" . getCurrentIdUser() . "' $where_vendor AND brand IS NOT NULL GROUP BY brand UNION SELECT name AS brand FROM msbrand WHERE userid = '" . getCurrentIdUser() . "') a ORDER BY a.brand ASC")->result();
                } else {
                    $brand = $this->db->query("SELECT * FROM (SELECT brand FROM msproduct WHERE userid = '" . getCurrentIdUser() . "' $where_vendor AND subcategory_apikey = '$subcategory_apikey' AND brand IS NOT NULL GROUP BY brand UNION SELECT name AS brand FROM msbrand WHERE userid = '" . getCurrentIdUser() . "' AND category = '$subcategory_apikey') a ORDER BY a.brand ASC")->result();
                }
            }

            $brands = array();
            foreach ($brand as $key => $value) {
                $brands[] = $value->brand;
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'DATA' => $brands
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function product()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Anda belum login');
            }

            $category = getPost('category');
            $brand = getPost('brand');

            if (empty($category)) {
                throw new Exception('Kategori tidak boleh kosong');
            }

            if (empty($brand)) {
                throw new Exception('Brand tidak boleh kosong');
            }

            if (getCurrentUser()->multivendor != 1) {
                $vendor = getCurrentVendor('PPOB', getCurrentIdUser());
                $this->msproduct->where("(a.vendor = '$vendor' OR a.vendor IS NULL) =", true);
                $this->msproduct->where('a.vendorid', null);
            } else {
                $this->msproduct->where("((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) =", true);
            }

            $product = $this->msproduct->select('a.id, a.productname')
                ->where('a.userid', getCurrentIdUser())
                ->where('a.category_apikey', 'PPOB')
                ->where('a.category', $category)
                ->where('a.brand', $brand)
                ->order_by('a.productname', 'ASC')
                ->get()
                ->result();

            $products = array();
            foreach ($product as $key => $value) {
                $detail = [];
                $detail['name'] = $value->productname;
                $detail['value'] = $value->id;

                $products[] = $detail;
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'DATA' => $products
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function product_smm()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Anda belum login');
            }

            $category = getPost('category');

            if (empty($category)) {
                throw new Exception('Kategori tidak boleh kosong');
            }

            if (getCurrentUser()->multivendor != 1) {
                $vendor = getCurrentVendor('SMM', getCurrentIdUser());
                $this->msproduct->where("(a.vendor = '$vendor' OR a.vendor IS NULL) =", true);
                $this->msproduct->where('a.vendorid', null);
            } else {
                $this->msproduct->where("((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) =", true);
            }

            $product = $this->msproduct->select('a.id, a.productname')
                ->where('a.userid', getCurrentIdUser())
                ->where('a.category_apikey', 'SMM')
                ->where('a.subcategory_apikey', 'SMM')
                ->where('a.category', $category)
                ->order_by('a.productname', 'ASC')
                ->get()
                ->result();

            $products = array();
            foreach ($product as $key => $value) {
                $detail = [];
                $detail['name'] = $value->productname;
                $detail['value'] = $value->id;

                $products[] = $detail;
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'DATA' => $products
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function database()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Anda belum login');
            }

            $database = getPost('database');

            if (empty($database)) {
                throw new Exception('Database tidak boleh kosong');
            }

            if ($database == 'Stock Product') {
                $stockproduct = $this->msstockproduct->select('a.id, a.name')
                    ->where(array(
                        'a.createdby' => getCurrentIdUser(),
                        'a.category' => 'PPOB'
                    ))
                    ->order_by('a.name', 'ASC')
                    ->get()
                    ->result();

                $databses = array();
                foreach ($stockproduct as $key => $value) {
                    $detail = [];
                    $detail['name'] = $value->name;
                    $detail['value'] = $value->id;

                    $databses[] = $detail;
                }
            } else {
                throw new Exception('Database tidak ditemukan');
            }

            $option = '<option value="">- Pilih -</option>';
            foreach ($databses as $key => $value) {
                $option .= '<option value="' . $value['value'] . '">' . $value['name'] . '</option>';
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $option
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
