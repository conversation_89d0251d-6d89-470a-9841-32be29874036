<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Tagihan</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Member</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Tagihan</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="invoicedate" class="col-form-label fw-semibold fs-6 pt-0">Tanggal Tagihan</label>
                                <input type="text" name="invoicedate" id="invoicedate" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Tanggal" value="<?= date('m/01/Y') ?> - <?= date('m/t/Y') ?>">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="invoicestatus" class="col-form-label fw-semibold fs-6 pt-0">Status Tagihan</label>
                                <select name="invoicestatus" id="invoicestatus" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                    <option value="">Pilih Status</option>
                                    <option value="Pending">Menunggu</option>
                                    <option value="Success">Berhasil</option>
                                    <option value="Cancel">Gagal</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="invoicecode" class="col-form-label fw-semibolds fs-6 pt-0">Kode Tagihan</label>
                                <input type="text" name="invoicecode" id="invoicecode" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Kode Deposit">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="member" class="col-form-label fw-semibold fs-6 pt-0">Member</label>
                                <select name="member" id="member" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                    <option value="">Pilih Member</option>
                                    <?php foreach ($users as $val) : ?>
                                        <option value="<?= $val->userid ?>"><?= $val->name ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <button type="button" class="btn btn-dark w-100" onclick="filter()">Filter</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-5">
                <div class="card-body">
                    <table class="table table-striped table-row-bordered gy-5 datatables-history">
                        <thead>
                            <tr class="fw-semibold fs-6 text-muted">
                                <th>Tanggal</th>
                                <th>Kode Topup</th>
                                <th>Nama Member</th>
                                <th>Deskripsi</th>
                                <th>Catatan</th>
                                <th>Nominal</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>

                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('input[name="invoicedate"]').daterangepicker();

        $('.datatables-history').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
                method: 'POST'
            }
        });
    };

    function confirmPayment(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Data akan dikonfirmasi, Pengguna akan memperoleh lisensi',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/confirm') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();
                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function cancelPayment(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Permintaan deposit akan dibatalkan',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/cancel') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function filter() {
        let invoicedate = $('#invoicedate').val();
        let invoicestatus = $('#invoicestatus').val();
        let invoicecode = $('#invoicecode').val();
        let member = $('#member').val();

        $('.datatables-history').DataTable().destroy();
        $('.datatables-history').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            ordering: false,
            ajax: {
                url: '<?= base_url('admins/invoice/history/datatables') ?>',
                method: 'POST',
                data: {
                    date: invoicedate,
                    status: invoicestatus,
                    code: invoicecode,
                    users: member,
                }
            }
        });
    }
</script>