<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Dashboard</h1>
        <!--end::Title-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid" id="kt_content">
    <!--begin::Row-->
    <div class="row gy-5 g-xl-10">
        <!--begin::Col-->
        <div class="col-sm-6 col-xl-4">
            <!--begin::Card widget 2-->
            <div class="card h-lg-100">
                <!--begin::Body-->
                <div class="card-body d-flex justify-content-between align-items-start flex-column">
                    <!--begin::Icon-->
                    <div class="m-0">
                        <!--begin::Svg Icon | path: icons/duotune/maps/map004.svg-->
                        <span class="svg-icon svg-icon-2hx svg-icon-gray-600">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z" fill="currentColor" />
                                <rect opacity="0.3" x="8" y="3" width="8" height="8" rx="4" fill="currentColor" />
                            </svg>
                        </span>
                        <!--end::Svg Icon-->
                    </div>
                    <!--end::Icon-->

                    <!--begin::Section-->
                    <div class="d-flex flex-column my-4">
                        <!--begin::Number-->
                        <span class="fw-semibold fs-3x text-gray-800 lh-1 ls-n2"><?= IDR($member) ?></span>
                        <!--end::Number-->

                        <!--begin::Follower-->
                        <div class="mt-2">
                            <span class="fw-semibold fs-6 text-gray-400 me-1">Total Member</span>
                        </div>
                        <!--end::Follower-->
                    </div>
                    <!--end::Section-->
                </div>
                <!--end::Body-->
            </div>
            <!--end::Card widget 2-->
        </div>
        <!--end::Col-->

        <!--begin::Col-->
        <div class="col-sm-6 col-xl-4">
            <!--begin::Card widget 2-->
            <div class="card h-lg-100">
                <!--begin::Body-->
                <div class="card-body d-flex justify-content-between align-items-start flex-column">
                    <!--begin::Icon-->
                    <div class="m-0">
                        <!--begin::Svg Icon | path: icons/duotune/maps/map004.svg-->
                        <span class="svg-icon svg-icon-2hx svg-icon-gray-600">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z" fill="currentColor" />
                                <rect opacity="0.3" x="8" y="3" width="8" height="8" rx="4" fill="currentColor" />
                            </svg>
                        </span>
                        <!--end::Svg Icon-->
                    </div>
                    <!--end::Icon-->

                    <!--begin::Section-->
                    <div class="d-flex flex-column my-4">
                        <!--begin::Number-->
                        <span class="fw-semibold fs-3x text-gray-800 lh-1 ls-n2"><?= $active_member > 0 ? IDR($active_member) : 0 ?></span>
                        <!--end::Number-->

                        <!--begin::Follower-->
                        <div class="mt-2">
                            <span class="fw-semibold fs-6 text-gray-400 me-1">Total Member Aktif</span>
                        </div>
                        <!--end::Follower-->
                    </div>
                    <!--end::Section-->
                </div>
                <!--end::Body-->
            </div>
            <!--end::Card widget 2-->
        </div>
        <!--end::Col-->

        <!--begin::Col-->
        <div class="col-sm-12 col-xl-12">
            <!--begin::Card widget 2-->
            <div class="card">
                <!--begin::Body-->
                <div class="card-body">
                    <table class="table table-striped table-row-bordered gy-5 datatables-monitoring">
                        <thead>
                            <tr class="fw-semibold fs-6 text-muted">
                                <th>Nama Panel</th>
                                <th>Alamat Email</th>
                                <th>Total Transaksi</th>
                                <th>Total Transaksi (Bulan Ini)</th>
                                <th>Total Transaksi (Hari Ini)</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>

                        <tbody>
                        </tbody>
                    </table>
                </div>
                <!--end::Body-->
            </div>
            <!--end::Card widget 2-->
        </div>
        <!--end::Col-->
    </div>
</div>
<script>
    window.onload = function() {
        $('.datatables-monitoring').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/monitoringmember/datatables') ?>',
                method: 'POST'
            }
        });
    };

    function modalReport(id) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/reportprofitomset') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        })
    }

    function historyLogError(id) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/historyerror') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id,
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    }
</script>