<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3"><PERSON><PERSON><PERSON></h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600"><PERSON><PERSON><PERSON></li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Keuntungan Bulanan</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <div class="accordion" id="kt_accordion_1">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="kt_accordion_1_header_1">
                            <button class="accordion-button fs-4 fw-semibold" type="button" data-bs-toggle="collapse"
                                data-bs-target="#kt_accordion_1_body_1" aria-expanded="true"
                                aria-controls="kt_accordion_1_body_1">
                                Preview Grafik Keuntungan Bulanan
                            </button>
                        </h2>
                        <div id="kt_accordion_1_body_1" class="accordion-collapse collapse show"
                            aria-labelledby="kt_accordion_1_header_1" data-bs-parent="#kt_accordion_1">
                            <div class="accordion-body">
                                <div class="card card-xl-stretch mb-5 mb-xl-8">
                                    <!--begin::Header-->
                                    <div class="card-header border-0 pt-5">
                                        <h3 class="card-title align-items-start flex-column">
                                            <span class="card-label fw-bold fs-3 mb-1">Recent Orders</span>

                                            <span class="text-muted fw-semibold fs-7">More than 500+ new orders</span>
                                        </h3>

                                        <!--begin::Toolbar-->
                                        <div class="card-toolbar" data-kt-buttons="true">

                                        </div>
                                        <!--end::Toolbar-->
                                    </div>
                                    <!--end::Header-->

                                    <!--begin::Body-->
                                    <div class="card-body">
                                        <!--begin::Chart-->
                                        <div id="kt_charts_widget_6_chart" style="height: 350px"></div>
                                        <!--end::Chart-->
                                    </div>
                                    <!--end::Body-->
                                </div>
                                <!--end::Charts Widget 5-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card mb-5">
                <div class="accordion" id="kt_accordion_1">
                    <div class="accordion-item">
                        <div class="card-body">
                            <table class="table table-striped table-row-bordered gy-5 datatables-profit text-nowrap">
                                <thead>
                                    <tr class="fw-semibold fs-6 text-muted">
                                        <th>Bulan</th>
                                        <th>Omset</th>
                                        <th>Keuntungan (Terealisasi)</th>
                                        <th>Keuntungan (Belum Terealisasi)</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>

                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>





<!-- PATCH GLOBAL YANG AMAN -->
<script>
if (!Object.prototype.put) {
    Object.defineProperty(Object.prototype, 'put', {
        value: function(key, val) {
            this[key] = val;
            return this;
        },
        enumerable: false
    });
}
</script>

<!-- SCRIPT UTAMA -->
<script>
window.onload = function() {
    $('.datatables-profit').DataTable({
        responsive: true,
        processing: true,
        serverSide: true,
        stateSave: true,
        ordering: false,
        ajax: {
            url: '<?= base_url('report/profit/monthly/datatables') ?>'
        }
    });

    const month = new Date().getMonth() + 1;
    const year = new Date().getFullYear();

    fetch(`<?= base_url('report/profit/chart_data_monthly') ?>?month=${month}&year=${year}`)
        .then(r => r.json())
        .then(res => {
            if (res.status !== 'SUCCESS') {
                console.error('Chart data error:', res.message);
                return;
            }

            const labels = res.data.map(x => x.label);
            const realized = res.data.map(x => x.realized);
            const unrealized = res.data.map(x => x.unrealized);

            const options = {
                series: [{
                        name: 'Keuntungan Terealisasi',
                        data: realized
                    },
                    {
                        name: 'Keuntungan Belum Terealisasi',
                        data: unrealized
                    }
                ],
                chart: {
                    type: 'area',
                    height: 350,
                    toolbar: {
                        show: false
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth'
                },
                xaxis: {
                    categories: labels
                },
                tooltip: {
                    y: {
                        formatter: val => "Rp " + new Intl.NumberFormat('id-ID').format(val)
                    }
                }
            };

            const chart = new ApexCharts(
                document.querySelector("#kt_charts_widget_6_chart"),
                options
            );
            chart.render();
        })
        .catch(err => console.error("Chart Fetch Error:", err));
};
</script>