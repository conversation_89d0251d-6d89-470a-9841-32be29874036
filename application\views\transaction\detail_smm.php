<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Transaksi SMM</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Transaksi</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">SMM</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Detail</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <a href="<?= base_url('transaction/smm') ?>" class="btn btn-danger fw-bold">Kembali</a>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="transactiondate" class="col-form-label fw-bold fs-6 pt-0 pb-1">Tanggal Transaksi</label>
                                <p><?= tgl_indo(date('Y-m-d', strtotime($transaction->createddate))) ?> <?= date('H:i:s', strtotime($transaction->createddate)) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="updateddate" class="col-form-label fw-bold fs-6 pt-0 pb-1">Tanggal Update</label>
                                <p><?= tgl_indo(date('Y-m-d', strtotime($transaction->updateddate))) ?> <?= date('H:i:s', strtotime($transaction->updateddate)) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="productvendor" class="col-form-label fw-bold fs-6 pt-0 pb-1">Vendor</label>
                                <p><?= $transaction->vendor_product ?? $transaction->vendor ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="buyername" class="col-form-label fw-bold fs-6 pt-0 pb-1">Pembeli</label>
                                <p><?= $transaction->buyer_name ?? 'Tamu' ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="transactioncode" class="col-form-label fw-bold fs-6 pt-0 pb-1">Kode Transaksi</label>
                                <p><?= $transaction->clientcode ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="refid" class="col-form-label fw-bold fs-6 pt-0 pb-1">Kode Referensi</label>
                                <p><?= $transaction->servercode ?? '-' ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="productcode" class="col-form-label fw-bold fs-6 pt-0 pb-1">Kode Produk</label>
                                <p><?= $transaction->product_code ?? $transaction->productcode ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="productname" class="col-form-label fw-bold fs-6 pt-0 pb-1">Nama Produk</label>
                                <p><?= $transaction->productname ?? $transaction->productname_order ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="target" class="col-form-label fw-bold fs-6 pt-0 pb-1">Tujuan</label>
                                <p><?= $transaction->target ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="paymenttype" class="col-form-label fw-bold fs-6 pt-0 pb-1">Pembayaran</label>
                                <p>
                                    <?php if ($transaction->paymenttype == null || $transaction->paymenttype == 'Saldo'): ?>
                                        Saldo
                                    <?php elseif ($transaction->paymenttype == 'Otomatis'): ?>
                                        <?= $transaction->payment ?> - Otomatis
                                    <?php elseif ($transaction->paymenttype == 'Manual'): ?>
                                        <?= $transaction->payment ?> - Manual
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="qty" class="col-form-label fw-bold fs-6 pt-0 pb-1">Quantity</label>
                                <p><?= IDR($transaction->qty) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="startcount" class="col-form-label fw-bold fs-6 pt-0 pb-1">Start Count</label>
                                <p><?= IDR($transaction->startcount ?? 0) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="remain" class="col-form-label fw-bold fs-6 pt-0 pb-1">Remain</label>
                                <p><?= IDR($transaction->remain ?? 0) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="pricevendor" class="col-form-label fw-bold fs-6 pt-0 pb-1">Harga Modal</label>
                                <p>Rp <?= IDR($transaction->price - $transaction->profit) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="sellingprice" class="col-form-label fw-bold fs-6 pt-0 pb-1">Harga Jual</label>
                                <p>Rp <?= IDR($transaction->price) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="profit" class="col-form-label fw-bold fs-6 pt-0 pb-1">Profit</label>
                                <p class="text-success">Rp <?= IDR($transaction->profit) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="orderplatform" class="col-form-label fw-bold fs-6 pt-0 pb-1">Pembelian Melalui</label>
                                <p><?= strtoupper($transaction->orderplatform ?? 'WEB') ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="balancebefore" class="col-form-label fw-bold fs-6 pt-0 pb-1">Saldo Sebelum Transaksi</label>
                                <p>Rp <?= IDR($transaction->currentsaldo) ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-7">
                                <label for="balanceafter" class="col-form-label fw-bold fs-6 pt-0 pb-1">Saldo Sesudah Transaksi</label>
                                <p>Rp <?= IDR($transaction->currentsaldo - $transaction->price) ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer text-center fw-bold fs-4 <?= (in_array(strtolower($transaction->status), ['pending'])) ? 'bg-warning text-white' : ((in_array(strtolower($transaction->status), ['success', 'completed'])) ? 'bg-success text-white' : ((in_array($transaction->status, ['in progress', 'processing'])) ? 'bg-info text-white' : 'bg-danger text-white')) ?>">
                    <?php if (in_array(strtolower($transaction->status), ['pending'])): ?>
                        PENDING
                    <?php elseif (in_array(strtolower($transaction->status), ['success', 'completed'])): ?>
                        SUCCESS
                    <?php elseif ($transaction->status == 'in progress'): ?>
                        IN PROGRESS
                    <?php elseif ($transaction->status == 'processing'): ?>
                        PROCESSING
                    <?php else: ?>
                        <?= strtoupper($transaction->status) ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>