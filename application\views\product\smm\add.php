<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Tambah Produk Media Sosial</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Produk</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Media Sosial</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Tambah Produk Media Sosial</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <a href="<?= base_url('product/smm') ?>" class="btn btn-danger fw-bold">Kembali</a>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <form id="frmAddProductSMM" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">ID Layanan</label>
                                    <input type="text" class="form-control form-control-solid" name="id_layanan" id="id_layanan" placeholder="ID Layanan" disabled />
                                </div>
                                <div class="mb-7">
                                    <div class="form-check form-check-custom form-check-solid mb-7">
                                        <input class="form-check-input" type="checkbox" name="isvendor" value="1" id="isvendor" onchange="changeIsvendor(this)" />

                                        <label class="form-check-label" for="isvendor">
                                            Dari Vendor
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Nama Produk</label>
                                    <input type="text" class="form-control form-control-solid" name="nama_produk" placeholder="Nama Produk" required />
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0 d-flex justify-content-between">
                                        <span>
                                            Kategori Produk
                                        </span>
                                    </label>

                                    <select class="form-select form-select-solid" name="kategori_produk" required>
                                        <option value="" selected>- Pilih -</option>
                                        <?php foreach ($category as $key => $value) : ?>
                                            <option value="<?= $value->category ?>"><?= $value->category ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0 d-flex justify-content-between">
                                        <span>
                                            Status Produk
                                        </span>
                                    </label>

                                    <select class="form-select form-select-solid" name="status_produk" required>
                                        <option value="" selected>- Pilih -</option>
                                        <option value="1">Normal</option>
                                        <option value="0">Gangguan</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Deskripsi</label>
                                    <textarea class="form-control form-control-solid" name="deskripsi" placeholder="Deskripsi"></textarea>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Harga Jual</label>
                                    <input type="text" class="form-control form-control-solid" name="harga_jual" placeholder="Harga Jual" required />
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Harga Vendor</label>
                                    <input type="text" class="form-control form-control-solid" name="harga_vendor" placeholder="Harga Vendor" required />
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Min Order</label>
                                    <input type="number" class="form-control form-control-solid" name="min_order" placeholder="Min Order" required />
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Max Order</label>
                                    <input type="number" class="form-control form-control-solid" name="max_order" placeholder="Max Order" required />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $.AjaxRequest('#frmAddProductSMM', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success',
                    }).then(function(result) {
                        window.location.href = '<?= base_url('product/smm') ?>';

                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            }
        });
    };

    function changeIsvendor(element) {
        if (!element.checked) {
            $('input[name="id_layanan"]').prop('disabled', true);
            $('input[name="id_layanan"]').val('');
            $('input[name="id_layanan"]').removeAttr('required');
        } else {
            $('input[name="id_layanan"]').prop('disabled', false);
            $('input[name="id_layanan"]').attr('required', true);
        }
    }
</script>