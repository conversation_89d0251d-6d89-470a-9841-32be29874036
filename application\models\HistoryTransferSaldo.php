<?php
defined('BASEPATH') or die('No direct script access allowed!');

class HistoryTransferSaldo extends MY_Model
{
    protected $table = 'historytransfersaldo';
    public $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.*, b.name, b.email')
            ->from($this->table . ' a')
            ->join('msusers b', 'b.id = a.userid');

        return $this;
    }
}
