<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsUsersKyc extends MY_Model
{
    protected $table = 'msuserskyc';
    public $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.*')
            ->from($this->table . ' a')
            ->join('msusers b', 'b.id = a.userid')
            ->order_by('a.createddate', 'DESC');

        return $this;
    }
}
