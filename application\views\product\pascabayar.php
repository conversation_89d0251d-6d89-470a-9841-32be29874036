<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Produk <PERSON></h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Produk</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Pascabayar</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <div>
        <a href="<?= base_url(uri_string() . '/bulk/delete') ?>" class="btn btn-danger fw-bold">Bulk Delete</a>
        <a href="<?= base_url(uri_string() . '/bulk/add') ?>" class="btn btn-dark fw-bold">Tambah Produk (Bulk)</a>
    </div>
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="productcategory" class="col-form-label fw-semibold fs-6 pt-0">Kategori Produk</label>
                                <select class="form-select form-select-solid" id="productcategory" name="productcategory">
                                    <option value="">Pilih Kategori</option>
                                    <?php foreach ($category as $key => $value) : ?>
                                        <option value="<?= $value->category ?>"><?= $value->category ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="productoperator" class="col-form-label fw-semibold fs-6 pt-0">Operator</label>
                                <select class="form-select form-select-solid" id="productoperator" name="productoperator">
                                    <option value="">Pilih Operator</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="productstatus" class="col-form-label fw-semibold fs-6 pt-0">Status</label>
                                <select class="form-select form-select-solid" id="productstatus" name="productstatus">
                                    <option value="">Pilih Status</option>
                                    <option value="Normal">Normal</option>
                                    <option value="Gangguan">Gangguan</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <button type="button" class="btn btn-dark w-100" onclick="filter()">Filter</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-5">
                <div class="card-body">
                    <?php if ($last_update != null) : ?>
                        <div class="text-end">
                            <small>Sinkronisasi terakhir: <?= date('d F Y H:i:s', strtotime($last_update)) ?></small>
                        </div>
                    <?php endif; ?>

                    <table class="table table-striped table-row-bordered gy-5 datatables-product text-nowrap">
                        <thead>
                            <tr class="fw-semibold fs-6 text-muted">
                                <th>Kode Produk</th>
                                <th>Nama Produk</th>
                                <th>Brand</th>
                                <th>Deskripsi</th>
                                <th>Admin (Vendor)</th>
                                <th>Admin</th>
                                <th>Komisi (Vendor)</th>
                                <th>Profit</th>
                                <th>Tanggal Ditambahkan</th>
                                <th>Terakhir Diupdate</th>
                                <th>Status</th>
                                <th>Rata Rata Waktu Proses</th>
                            </tr>
                        </thead>

                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('.datatables-product').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
                method: 'POST'
            }
        });

        $('#productcategory').change(function() {
            $('#productoperator').html('<option value="">Pilih Operator</option>');

            $.ajax({
                url: '<?= base_url('select/brand') ?>',
                method: 'POST',
                dataType: 'json',
                data: {
                    category: $(this).val()
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        for (let i = 0; i < response.DATA.length; i++) {
                            $('#productoperator').append('<option value="' + response.DATA[i] + '">' + response.DATA[i] + '</option>');
                        }
                    }
                }
            }).fail(function() {
                return Swal.fire('Gagal', 'Server sedang sibuk! Silahkan coba lagi nanti');
            });
        });
    };

    function filter() {
        let productcategory = $('#productcategory').val();
        let productoperator = $('#productoperator').val();
        let productstatus = $('#productstatus').val();

        $('.datatables-product').DataTable().destroy();
        $('.datatables-product').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url('product/pascabayar/datatables') ?>',
                method: 'POST',
                data: {
                    kategori: productcategory,
                    brand: productoperator,
                    status: productstatus
                }
            }
        });
    }
</script>