<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Datatables $datatables
 * @property MsPlatformSosmed $msplatformsosmed
 * @property MsIcons $msicons
 * @property MsDetailPlatform $msdetailplatform
 * @property MsProduct $msproduct
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 */
class PlatformSosmed extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsPlatformSosmed', 'msplatformsosmed');
        $this->load->model('MsIcons', 'msicons');
        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('MsDetailPlatform', 'msdetailplatform');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'PPOB') {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Platform Sosmed';
        $data['content'] = 'manage/platformsosmed/index';

        return $this->load->view('master', $data);
    }

    public function datatables_platformsosmed()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if ($this->user->companycategory == 'PPOB') {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $data = array();

            $datatables = $this->datatables->make('MsPlatformSosmed', 'QueryDatatables', 'SearchDatatables');

            foreach (
                $datatables->getData(array(
                    'userid' => getCurrentIdUser()
                )) as $key => $value
            ) {
                $detail = array();
                $detail[] = $value->name;
                $detail[] = "<img src=\"" . base_url('uploads/' . $value->asseturl) . "\" width=\"25\">";
                $detail[] = "<a href=\"" . base_url('manage/platformsosmed/detail/' . $value->id) . "\" class=\"btn btn-icon btn-primary btn-sm mb-1\">
                    <i class=\"fa fa-eye\"></i>
                </a>
                
                <a href=\"" . base_url('manage/platformsosmed/edit/' . $value->id) . "\" class=\"btn btn-icon btn-warning btn-sm mb-1\">
                    <i class=\"fa fa-edit\"></i>
                </a>

                <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"deletePlatformSosmed('" . stringEncryption('encrypt', $value->id) . "')\">
                    <i class=\"fa fa-trash\"></i>
                </a>";


                $data[] = $detail;
            }

            return $datatables->json($data);
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'PPOB') {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tambah Platform Sosmed';
        $data['content'] = 'manage/platformsosmed/add';
        $data['icons'] = $this->msicons->result(array(
            'createdby' => getCurrentIdUser()
        ));

        return $this->load->view('master', $data);
    }

    public function process_add_platformsosmed()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if ($this->user->companycategory == 'PPOB') {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $nama = getPost('nama');
            $asset = getPost('asset');

            if ($nama == null) {
                throw new Exception('Nama tidak boleh kosong');
            } else if ($asset == null) {
                throw new Exception('Pilih salah satu icon');
            } else {
                $nama = removeSymbol($nama);
            }

            $get_asset = $this->msicons->total(array(
                'id' => $asset,
                'createdby' => getCurrentIdUser()
            ));

            if ($get_asset == 0) {
                throw new Exception('Icon tidak ditemukan');
            }

            $insert = array();
            $insert['name'] = $nama;
            $insert['assetid'] = $asset;
            $insert['userid'] = getCurrentIdUser();
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();
            $insert['updateddate'] = getCurrentDate();
            $insert['updatedby'] = getCurrentIdUser();

            $this->msplatformsosmed->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan platform sosmed');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menambahkan platform sosmed');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'PPOB') {
            return redirect(base_url('dashboard'));
        }

        $get = $this->msplatformsosmed->get(array(
            'id' => $id,
            'userid' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/platformsosmed'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Ubah Platform Sosmed';
        $data['content'] = 'manage/platformsosmed/edit';
        $data['platformsosmed'] = $get->row();
        $data['icons'] = $this->msicons->result(array(
            'createdby' => getCurrentIdUser()
        ));

        return $this->load->view('master', $data);
    }

    public function process_edit_platformsosmed($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if ($this->user->companycategory == 'PPOB') {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $get = $this->msplatformsosmed->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Platform sosmed tidak ditemukan');
            }

            $nama = getPost('nama');
            $asset = getPost('asset');

            if ($nama == null) {
                throw new Exception('Nama tidak boleh kosong');
            } else if ($asset == null) {
                throw new Exception('Pilih salah satu icon');
            } else {
                $nama = removeSymbol($nama);
            }

            $get_asset = $this->msicons->total(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($get_asset == 0) {
                throw new Exception('Icon tidak ditemukan');
            }

            $update = array();
            $update['name'] = $nama;
            $update['assetid'] = $asset;
            $update['userid'] = getCurrentIdUser();
            $insert['updateddate'] = getCurrentDate();
            $insert['updatedby'] = getCurrentIdUser();

            $this->msplatformsosmed->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah Platform sosmed');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil mengubah Platform sosmed');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_platformsosmed()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if ($this->user->companycategory == 'PPOB') {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            if ($id == null) {
                throw new Exception('ID Platform sosmed tidak boleh kosong');
            }

            $get = $this->msplatformsosmed->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Platform sosmed tidak ditemukan');
            }

            $this->msplatformsosmed->delete(array(
                'id' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus Platform sosmed');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menghapus Platform sosmed');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function detail($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'PPOB') {
            return redirect(base_url('dashboard'));
        }

        $get = $this->msplatformsosmed->total(array(
            'id' => $id,
            'userid' => getCurrentIdUser()
        ));

        if ($get == 0) {
            return redirect(base_url('manage/platformsosmed'));
        }

        $getdt = $this->msdetailplatform->get(array(
            'platformid' => $id
        ));

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Detail Platform Sosmed';
        $data['content'] = 'manage/platformsosmed/detail';
        $data['detailplatform'] = $getdt->result();

        if (getCurrentUser()->multivendor != 1) {
            $vendorid = getCurrentVendor('SMM');
            $this->msproduct->where('vendor', $vendorid);
            $this->msproduct->where('vendorid', null);
        } else {
            $this->msproduct->where('vendorid !=', null);
            $this->msproduct->where('vendorenabled', 1);
        }

        $data['product'] = $this->msproduct->select('category')
            ->where(array('category_apikey' => 'SMM'))
            ->where(array('userid' => getCurrentIdUser()))
            ->group_by('category')
            ->get()
            ->result();

        return $this->load->view('master', $data);
    }

    public function process_detail_platformsosmed($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if ($this->user->companycategory == 'PPOB') {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $get = $this->msplatformsosmed->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Platform sosmed tidak ditemukan');
            }

            $category = getPost('category', array());

            if (!is_array($category)) {
                throw new Exception('Pilih salah satu kategori');
            } else if (count($category) == 0) {
                throw new Exception('Pilih salah satu kategori');
            }

            $getdt = $this->msdetailplatform->get(array(
                'platformid' => $id
            ));

            $exceptCategory = array();
            if ($getdt->num_rows() > 0) {
                $availableCategory = array();

                foreach ($category as $key => $value) {
                    $availableCategory[$value] = 'available';
                }

                foreach ($getdt->result() as $key => $val) {
                    if (!array_key_exists($val->category, $availableCategory)) {
                        $this->msdetailplatform->delete(array(
                            'id' => $val->id
                        ));
                    } else {
                        $exceptCategory[$val->category] = 'except';
                    }
                }
            }

            foreach ($category as $key => $value) {
                if (array_key_exists($value, $exceptCategory)) {
                    continue;
                }

                $insert = array();
                $insert['category'] = $value;
                $insert['platformid'] = $id;
                $insert['createddate'] = getCurrentDate();
                $insert['createdby'] = getCurrentIdUser();
                $insert['updateddate'] = getCurrentDate();
                $insert['updatedby'] = getCurrentIdUser();

                $this->msdetailplatform->insert($insert);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah detail Platform sosmed');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil mengubah detail Platform sosmed');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
