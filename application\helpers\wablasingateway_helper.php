<?php
defined('BASEPATH') or die('No direct script access allowed!');

class WABlasinGateway
{
    private $_apikey;
    private $_devicecode;

    public function __construct($apikey, $devicecode)
    {
        $this->_apikey = $apikey;
        $this->_devicecode = $devicecode;
    }

    public function sendMessage($phonenumber, $message)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://wablasingateway.com/api',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => 'token=' . $this->_devicecode . '&inquiry=send-messages&receiptnumber=' . $phonenumber . '&message=' . $message . '&apikey=' . $this->_apikey,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded',
            ),
            CURLOPT_TIMEOUT => 10,
        ));

        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response);
    }
}
