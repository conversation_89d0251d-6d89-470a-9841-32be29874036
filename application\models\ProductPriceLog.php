<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ProductPriceLog extends MY_Model
{
    protected $table = 'productpricelog';
    public $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.type, a.oldprice, a.newprice, b.productname, a.createddate')
            ->from($this->table . ' a')
            ->join('msproduct b', 'b.id = a.productid')
            ->order_by('a.createddate', 'DESC');

        return $this;
    }
}
