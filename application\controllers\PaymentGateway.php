<?php

use Duitku\Api;
use Duitku\Config;
use <PERSON><PERSON><PERSON>\GojekPay;
use <PERSON><PERSON><PERSON>\Ovo;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsPaymentGateway $mspaymentgateway
 * @property MsPaymentMethod $mspaymentmethod
 * @property FeePaymentGateway $feepaymentgateway
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver $db
 * @property CI_Upload $upload
 * @property MsNotificationHandlerPackage $msnotificationhandlerpackage
 * @property UserBuyNotificationHandler $userbuynotificationhandler
 * @property HistoryBalance $historybalance
 * @property MsUsers $msusers
 * @property CI_Form_validation $form_validation
 */
class PaymentGateway extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsPaymentGateway', 'mspaymentgateway');
        $this->load->model('MsPaymentMethod', 'mspaymentmethod');
        $this->load->model('FeePaymentGateway', 'feepaymentgateway');
        $this->load->model('MsNotificationHandlerPackage', 'msnotificationhandlerpackage');
        $this->load->model('UserBuyNotificationHandler', 'userbuynotificationhandler');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('HistoryBalance', 'historybalance');
    }

    public function manual()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Payment Gateway Manual';
        $data['content'] = 'paymentgateway/manual';

        return $this->load->view('master', $data);
    }

    public function datatables_manual()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();

                $datatable = $this->datatables->make('MsPaymentMethod', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'userid' => getCurrentIdUser()
                );

                foreach ($datatable->getData($where) as $key => $value) {
                    $detail = array();

                    if ($value->maxnominal == 0) {
                        $maxnominal = "<span class=\"badge badge-danger\">Unlimited</span>";
                    } else {
                        $maxnominal = IDR($value->maxnominal);
                    }

                    if ($value->isunique == 1) {
                        $isunique = "<span class=\"badge badge-success\">Ya</span>";
                    } else {
                        $isunique = "<span class=\"badge badge-danger\">Tidak</span>";
                    }

                    if ($value->uniqueadmin == 1) {
                        $uniqueadmin = "<span class=\"badge badge-success\">Ya</span>";
                    } else {
                        $uniqueadmin = "<span class=\"badge badge-danger\">Tidak</span>";
                    }

                    if ($value->isbonus == 1) {
                        $isbonus = "<span class=\"badge badge-success\">Ya</span>";
                    } else {
                        $isbonus = "<span class=\"badge badge-danger\">Tidak</span>";
                    }

                    if ($value->feetype != null) {
                        if ($value->feetype == 'Persentase') {
                            $nominalfee = $value->nominalfee . '%';
                        } else {
                            $nominalfee = 'Rp ' . IDR($value->nominalfee);
                        }

                        $isfee = "<span class=\"badge badge-success\">Ya - $nominalfee</span>";
                    } else {
                        $isfee = "<span class=\"badge badge-danger\">Tidak</span>";
                    }

                    $disabled = "";
                    if ($value->isdisabled == 1) {
                        $disabled = "<button type=\"button\" class=\"btn btn-success btn-sm mb-1\" onclick=\"enable('" . stringEncryption('encrypt', $value->id) . "')\">
                            Enable
                        </button>";
                    } else {
                        $disabled = "<button type=\"button\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"disable('" . stringEncryption('encrypt', $value->id) . "')\">
                            Disable
                        </button>";
                    }

                    $actions = "$disabled
                
                    <a href=\"javascript:;\" class=\"btn btn-icon btn-warning btn-sm mb-1\" onclick=\"editPaymentMethod('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-edit\"></i>
                    </a>

                    <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"deletePaymentMethod('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-trash\"></i>
                    </a>";

                    $image = null;

                    if ($value->paymentimage != null && file_exists('./uploads/' . $value->paymentimage)) {
                        $image = "<img src=\"" . base_url('uploads/' . $value->paymentimage) . "\" class=\"img-fluid\" style=\"width: 100px;\">";
                    }

                    $detail[] = $image;
                    $detail[] = $value->image == null ? 'Normal' : "<a href=\"" . base_url('uploads/' . $value->image) . "\" target=\"_blank\">Scan</a>";
                    $detail[] = $value->paymentmethod;
                    $detail[] = "<b>$value->accountname</b><br>" . ($value->accountnumber ?? '-');
                    $detail[] = IDR($value->minnominal);
                    $detail[] = $maxnominal;
                    $detail[] = $isunique;
                    $detail[] = $uniqueadmin;
                    $detail[] = $isbonus;
                    $detail[] = $isfee;
                    $detail[] = $actions;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function automatic()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $currentiduser = getCurrentIdUser();

        $paymentgateway = $this->mspaymentgateway->get(array(
            'userid' => $currentiduser,
            'type' => 'Payment Gateway'
        ))->row();

        $bankbca = $this->mspaymentgateway->select('a.*,b.bca_bot')
            ->join('msusers b', 'b.id = a.userid')
            ->where(array(
                'a.userid' => $currentiduser,
                'a.type' => 'Bank BCA'
            ))->row();

        $gopay = $this->mspaymentgateway->get(array(
            'userid' => $currentiduser,
            'type' => 'GOPAY'
        ))->row();

        $ovo = $this->mspaymentgateway->get(array(
            'userid' => $currentiduser,
            'type' => 'OVO'
        ))->row();

        $gopaypgstatus = false;
        if ($gopay != null) {
            $detail = json_decode(stringEncryption('decrypt', $gopay->detail));
            $gopayinstance = new GojekPay($detail->token);

            $profile = json_decode($gopayinstance->getProfile());

            if (isset($profile->status) && $profile->status == 'OK' && isset($profile->customer->name)) {
                $gopaypgstatus = true;
                $data['gopayname'] = $profile->customer->name;
            }
        }

        $ovostatus = false;
        if ($ovo != null) {
            $detail = json_decode(stringEncryption('decrypt', $ovo->detail));
            $ovoinstance = new Ovo($detail->token);

            $transactionlist = array();
            $transaction = $ovoinstance->transactionHistory();

            if ($transaction) {
                $transaction = json_decode($transaction);

                if (isset($transaction->response_message) && $transaction->response_message == 'Success') {
                    if ($transaction->data->status == 200) {
                        $firstpage = $transaction->data->orders[0];

                        if (isset($firstpage->complete)) {
                            $completetransaction = $firstpage->complete;

                            foreach ($completetransaction as $key => $value) {
                                if ($value->transaction_type == 'INCOMING TRANSFER') {
                                    $transactionlist[] = $value;
                                }
                            }
                        }
                    }
                }
            }

            $email = $ovoinstance->getEmail();

            if ($email) {
                $email = json_decode($email);

                if (isset($email->data->email)) {
                    $ovostatus = true;
                    $data['ovoemail'] = $email->data->email;
                }
            }
        }

        $data['title'] = 'Server PPOB & SMM - Payment Gateway Otomatis';
        $data['content'] = 'paymentgateway/automatic';
        $data['paymentgateway'] = $paymentgateway;
        $data['bankbca'] = $bankbca;
        $data['gopay'] = $gopay;
        $data['ovo'] = $ovo;
        $data['gopaypgstatus'] = $gopaypgstatus;
        $data['ovostatus'] = $ovostatus;
        $data['notificationhandler'] = $this->msnotificationhandlerpackage->select('a.*, b.id AS buyid, b.isdisabled')
            ->join('userbuynotificationhandler b', 'b.notificationid = a.id AND b.userid = ' . $currentiduser, 'LEFT')
            ->result(array(
                'a.isactive' => 1
            ));

        return $this->load->view('master', $data);
    }

    public function process_change_paymentgateway()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $vendor = getPost('vendor');
            $serverkey = getPost('serverkey');
            $clientkey = getPost('clientkey');
            $merchantcode = getPost('merchantcode');
            $apikey = getPost('apikey');
            $privatekey = getPost('privatekey');
            $minnominal = getPost('minnominal', 0);
            $maxnominal = getPost('maxnominal', 0);
            $isbonus = getPost('isbonus', 0);
            $bonustype = getPost('bonustype');
            $nominalbonus = getPost('nominalbonus', 0);
            $virtualaccount = getPost('virtualaccount');

            if ($vendor == null) {
                throw new Exception('Vendor wajib diisi');
            } else if ($vendor != 'Midtrans' && $vendor != 'Tripay' && $vendor != 'iPaymu' && $vendor != 'Duitku' && $vendor != 'Okeconnect' && $vendor != 'PayDisini') {
                throw new Exception('Vendor tidak ditemukan');
            } else if ($vendor == 'Midtrans') {
                if ($serverkey == null) {
                    throw new Exception('Server Key wajib diisi');
                } else if ($clientkey == null) {
                    throw new Exception('Client Key wajib diisi');
                }
            } else if ($vendor == 'Tripay') {
                if ($merchantcode == null) {
                    throw new Exception('Kode Merchant wajib diisi');
                } else if ($apikey == null) {
                    throw new Exception('API Key wajib diisi');
                } else if ($privatekey == null) {
                    throw new Exception('Private Key wajib diisi');
                }
            } else if ($vendor == 'iPaymu') {
                if ($virtualaccount == null) {
                    throw new Exception('Virtual Account wajib diisi');
                } else if ($apikey == null) {
                    throw new Exception('API Key wajib diisi');
                }
            } else if ($vendor == 'Duitku') {
                if ($merchantcode == null) {
                    throw new Exception('Kode Merchant wajib diisi');
                } else if ($apikey == null) {
                    throw new Exception('API Key wajib diisi');
                }
            } else if ($vendor == 'Okeconnect') {
                if ($merchantcode == null) {
                    throw new Exception('Kode Merchant wajib diisi');
                } else if ($apikey == null) {
                    throw new Exception('API Key wajib diisi');
                }
            } else if ($vendor == 'PayDisini') {
                if ($apikey == null) {
                    throw new Exception('API Key wajib diisi');
                }
            } else if (!is_numeric($minnominal)) {
                throw new Exception('Minimal Nominal harus berupa angka');
            } else if (!is_numeric($maxnominal)) {
                throw new Exception('Maksimal Nominal harus berupa angka');
            } else if ($minnominal < 0 || $maxnominal < 0) {
                throw new Exception('Minimal nominal adalah 0');
            } else {
                $serverkey = removeSymbol($serverkey);
                $clientkey = removeSymbol($clientkey);
                $merchantcode = removeSymbol($merchantcode);
                $apikey = removeSymbol($apikey);
                $privatekey = removeSymbol($privatekey);
                $virtualaccount = removeSymbol($virtualaccount);
            }

            if ($isbonus) {
                if (!in_array($bonustype, array('Persentase', 'Nominal'))) {
                    throw new Exception('Tipe Bonus tidak ditemukan');
                } else if (!is_numeric($nominalbonus)) {
                    throw new Exception('Nominal Bonus harus berupa angka');
                } else if ($nominalbonus < 0) {
                    throw new Exception('Nominal Bonus harus lebih besar dari 0');
                } else {
                    if ($bonustype == 'Persentase') {
                        if ($nominalbonus > 100) {
                            throw new Exception('Nominal Bonus tidak boleh lebih dari 100');
                        }
                    }
                }
            }

            if ($vendor == 'Midtrans') {
                $detail = array(
                    'serverkey' => $serverkey,
                    'clientkey' => $clientkey
                );
            } else if ($vendor == 'Tripay') {
                $detail = array(
                    'merchantcode' => $merchantcode,
                    'apikey' => $apikey,
                    'privatekey' => $privatekey
                );
            } else if ($vendor == 'iPaymu') {
                $detail = array(
                    'virtualaccount' => $virtualaccount,
                    'apikey' => $apikey
                );
            } else if ($vendor == 'Duitku') {
                $detail = array(
                    'merchantcode' => $merchantcode,
                    'apikey' => $apikey
                );
            } else if ($vendor == 'Okeconnect') {
                $detail = array(
                    'merchantcode' => $merchantcode,
                    'apikey' => $apikey
                );
            } else if ($vendor == 'PayDisini') {
                $detail = array(
                    'apikey' => $apikey
                );
            }

            $detail = json_encode($detail);

            $execute = array();
            $execute['userid'] = getCurrentIdUser();
            $execute['type'] = 'Payment Gateway';
            $execute['vendor'] = $vendor;
            $execute['detail'] = stringEncryption('encrypt', $detail);
            $execute['minnominal'] = $minnominal;
            $execute['maxnominal'] = $maxnominal;
            $execute['isbonus'] = $isbonus ? 1 : 0;

            if ($isbonus) {
                $execute['bonustype'] = $bonustype;
                $execute['nominalbonus'] = $nominalbonus;
            } else {
                $execute['bonustype'] = null;
                $execute['nominalbonus'] = 0;
            }

            $get = $this->mspaymentgateway->get(array('userid' => getCurrentIdUser(), 'type' => 'Payment Gateway'))->row();

            if ($get == null) {
                $this->mspaymentgateway->insert($execute);
            } else {
                $this->mspaymentgateway->update(array(
                    'id' => $get->id
                ), $execute);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_change_ovo()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $minnominal = getPost('minnominal', 0);
            $maxnominal = getPost('maxnominal', 0);
            $isbonus = getPost('isbonus', 0);
            $bonustype = getPost('bonustype');
            $nominalbonus = getPost('nominalbonus', 0);
            $isfee = getPost('isfee', 0);
            $feetype = getPost('feetype');
            $nominalfee = getPost('nominalfee', 0);
            $paymentimage = isset($_FILES['paymentimage']) ? $_FILES['paymentimage'] : null;

            if (!is_numeric($minnominal)) {
                throw new Exception('Minimal Nominal harus berupa angka');
            } else if (!is_numeric($maxnominal)) {
                throw new Exception('Maksimal Nominal harus berupa angka');
            } else if ($minnominal < 0 || $maxnominal < 0) {
                throw new Exception('Minimal nominal adalah 0');
            }

            if ($isbonus) {
                if (!in_array($bonustype, array('Persentase', 'Nominal'))) {
                    throw new Exception('Tipe Bonus tidak ditemukan');
                } else if (!is_numeric($nominalbonus)) {
                    throw new Exception('Nominal Bonus harus berupa angka');
                } else if ($nominalbonus < 0) {
                    throw new Exception('Nominal Bonus harus lebih besar dari 0');
                } else {
                    if ($bonustype == 'Persentase') {
                        if ($nominalbonus > 100) {
                            throw new Exception('Nominal Bonus tidak boleh lebih dari 100');
                        }
                    }
                }
            }

            if ($isfee) {
                if (!in_array($feetype, array('Persentase', 'Nominal'))) {
                    throw new Exception('Tipe Fee tidak ditemukan');
                } else if (!is_numeric($nominalfee)) {
                    throw new Exception('Nominal Fee harus berupa angka');
                } else if ($nominalfee < 0) {
                    throw new Exception('Nominal Fee harus lebih besar dari 0');
                } else {
                    if ($feetype == 'Persentase') {
                        if ($nominalfee > 100) {
                            throw new Exception('Nominal Fee tidak boleh lebih dari 100');
                        }
                    }
                }
            }

            $get = $this->mspaymentgateway->get(array('userid' => getCurrentIdUser(), 'type' => 'OVO'))->row();

            if ($get == null) {
                throw new Exception('Silahkan login kedalam akun OVO anda terlebih dahulu');
            }

            $update = array();
            if ($paymentimage['size'] > 0) {
                $config = array(
                    'upload_path' => './uploads/',
                    'allowed_types' => 'jpg|jpeg|png',
                    'encrypt_name' => true,
                );

                $this->load->library('upload', $config);

                if (!$this->upload->do_upload('paymentimage')) {
                    throw new Exception($this->upload->display_errors('', ''));
                } else {
                    $upload = $this->upload->data();

                    $update['paymentimage'] = $upload['file_name'];
                }
            } else if ($get->paymentimage == null) {
                throw new Exception('Gambar pembayaran wajib diisi');
            }

            $update['minnominal'] = $minnominal;
            $update['maxnominal'] = $maxnominal;
            $update['isbonus'] = $isbonus ? 1 : 0;

            if ($isbonus) {
                $update['bonustype'] = $bonustype;
                $update['nominalbonus'] = $nominalbonus;
            } else {
                $update['bonustype'] = null;
                $update['nominalbonus'] = 0;
            }

            if ($isfee) {
                $update['feetype'] = $feetype;
                $update['nominalfee'] = $nominalfee;
            } else {
                $update['feetype'] = null;
                $update['nominalfee'] = null;
            }

            $this->mspaymentgateway->update(array(
                'id' => $get->id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_change_gopay()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $minnominal = getPost('minnominal', 0);
            $maxnominal = getPost('maxnominal', 0);
            $isbonus = getPost('isbonus', 0);
            $bonustype = getPost('bonustype');
            $nominalbonus = getPost('nominalbonus', 0);
            $isfee = getPost('isfee', 0);
            $feetype = getPost('feetype');
            $nominalfee = getPost('nominalfee', 0);
            $paymentimage = isset($_FILES['paymentimage']) ? $_FILES['paymentimage'] : null;

            if (!is_numeric($minnominal)) {
                throw new Exception('Minimal Nominal harus berupa angka');
            } else if (!is_numeric($maxnominal)) {
                throw new Exception('Maksimal Nominal harus berupa angka');
            } else if ($minnominal < 0 || $maxnominal < 0) {
                throw new Exception('Minimal nominal adalah 0');
            }

            if ($isbonus) {
                if (!in_array($bonustype, array('Persentase', 'Nominal'))) {
                    throw new Exception('Tipe Bonus tidak ditemukan');
                } else if (!is_numeric($nominalbonus)) {
                    throw new Exception('Nominal Bonus harus berupa angka');
                } else if ($nominalbonus < 0) {
                    throw new Exception('Nominal Bonus harus lebih besar dari 0');
                } else {
                    if ($bonustype == 'Persentase') {
                        if ($nominalbonus > 100) {
                            throw new Exception('Nominal Bonus tidak boleh lebih dari 100');
                        }
                    }
                }
            }

            if ($isfee) {
                if (!in_array($feetype, array('Persentase', 'Nominal'))) {
                    throw new Exception('Tipe Fee tidak ditemukan');
                } else if (!is_numeric($nominalfee)) {
                    throw new Exception('Nominal Fee harus berupa angka');
                } else if ($nominalfee < 0) {
                    throw new Exception('Nominal Fee harus lebih besar dari 0');
                } else {
                    if ($feetype == 'Persentase') {
                        if ($nominalfee > 100) {
                            throw new Exception('Nominal Fee tidak boleh lebih dari 100');
                        }
                    }
                }
            }

            $get = $this->mspaymentgateway->get(array('userid' => getCurrentIdUser(), 'type' => 'GOPAY'))->row();

            if ($get == null) {
                throw new Exception('Silahkan login kedalam akun GOPAY anda terlebih dahulu');
            }

            $update = array();
            if ($paymentimage['size'] > 0) {
                $config = array(
                    'upload_path' => './uploads/',
                    'allowed_types' => 'jpg|jpeg|png',
                    'encrypt_name' => true,
                );

                $this->load->library('upload', $config);

                if (!$this->upload->do_upload('paymentimage')) {
                    throw new Exception($this->upload->display_errors('', ''));
                } else {
                    $upload = $this->upload->data();

                    $update['paymentimage'] = $upload['file_name'];
                }
            } elseif ($get->paymentimage == null) {
                throw new Exception('Gambar pembayaran wajib diisi');
            }

            $update['minnominal'] = $minnominal;
            $update['maxnominal'] = $maxnominal;
            $update['isbonus'] = $isbonus ? 1 : 0;

            if ($isbonus) {
                $update['bonustype'] = $bonustype;
                $update['nominalbonus'] = $nominalbonus;
            } else {
                $update['bonustype'] = null;
                $update['nominalbonus'] = 0;
            }

            if ($isfee) {
                $update['feetype'] = $feetype;
                $update['nominalfee'] = $nominalfee;
            } else {
                $update['feetype'] = null;
                $update['nominalfee'] = null;
            }

            $this->mspaymentgateway->update(array(
                'id' => $get->id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_change_bca()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $accountname = getPost('accountname');
            $accountnumber = getPost('accountnumber');
            $username = getPost('username');
            $password = getPost('password');
            $minnominal = getPost('minnominal', 0);
            $maxnominal = getPost('maxnominal', 0);
            $isbonus = getPost('isbonus', 0);
            $bonustype = getPost('bonustype');
            $nominalbonus = getPost('nominalbonus', 0);
            $isfee = getPost('isfee', 0);
            $feetype = getPost('feetype');
            $nominalfee = getPost('nominalfee', 0);
            $paymentimage = isset($_FILES['paymentimage']) ? $_FILES['paymentimage'] : null;

            if ($accountname == null) {
                throw new Exception('Nama Pemilik Rekening wajib diisi');
            } else if ($accountnumber == null) {
                throw new Exception('Nomor Rekening wajib diisi');
            } else if (!is_numeric($accountnumber)) {
                throw new Exception('Nomor Rekening harus berupa angka');
            } else if ($username == null) {
                throw new Exception('Username wajib diisi');
            } else if ($password == null) {
                throw new Exception('Password wajib diisi');
            } else if (!is_numeric($minnominal)) {
                throw new Exception('Minimal Nominal harus berupa angka');
            } else if (!is_numeric($maxnominal)) {
                throw new Exception('Maksimal Nominal harus berupa angka');
            } else if ($minnominal < 0 || $maxnominal < 0) {
                throw new Exception('Minimal nominal adalah 0');
            } else {
                $accountname = removeSymbol($accountname);
                $username = removeSymbol($username);
                $password = removeSymbol($password);
            }

            if ($isbonus) {
                if (!in_array($bonustype, array('Persentase', 'Nominal'))) {
                    throw new Exception('Tipe Bonus tidak ditemukan');
                } else if (!is_numeric($nominalbonus)) {
                    throw new Exception('Nominal Bonus harus berupa angka');
                } else if ($nominalbonus < 0) {
                    throw new Exception('Nominal Bonus harus lebih besar dari 0');
                } else {
                    if ($bonustype == 'Persentase') {
                        if ($nominalbonus > 100) {
                            throw new Exception('Nominal Bonus tidak boleh lebih dari 100');
                        }
                    }
                }
            }

            if ($isfee) {
                if (!in_array($feetype, array('Persentase', 'Nominal'))) {
                    throw new Exception('Tipe Fee tidak ditemukan');
                } else if (!is_numeric($nominalfee)) {
                    throw new Exception('Nominal Fee harus berupa angka');
                } else if ($nominalfee < 0) {
                    throw new Exception('Nominal Fee harus lebih besar dari 0');
                } else {
                    if ($feetype == 'Persentase') {
                        if ($nominalfee > 100) {
                            throw new Exception('Nominal Fee tidak boleh lebih dari 100');
                        }
                    }
                }
            }

            $detail = array(
                'accountname' => $accountname,
                'accountnumber' => $accountnumber,
                'username' => $username,
                'password' => $password
            );

            $detail = json_encode($detail);

            $execute = array();
            $execute['userid'] = getCurrentIdUser();
            $execute['type'] = 'Bank BCA';
            $execute['vendor'] = 'BCA';
            $execute['detail'] = stringEncryption('encrypt', $detail);
            $execute['minnominal'] = $minnominal;
            $execute['maxnominal'] = $maxnominal;
            $execute['isbonus'] = $isbonus ? 1 : 0;

            if ($isbonus) {
                $execute['bonustype'] = $bonustype;
                $execute['nominalbonus'] = $nominalbonus;
            } else {
                $execute['bonustype'] = null;
                $execute['nominalbonus'] = 0;
            }

            if ($isfee) {
                $execute['feetype'] = $feetype;
                $execute['nominalfee'] = $nominalfee;
            } else {
                $execute['feetype'] = null;
                $execute['nominalfee'] = null;
            }

            $get = $this->mspaymentgateway->get(array('userid' => getCurrentIdUser(), 'type' => 'Bank BCA'))->row();

            if ($get == null) {
                if ($paymentimage['size'] > 0) {
                    $config = array(
                        'upload_path' => './uploads/',
                        'allowed_types' => 'jpg|jpeg|png',
                        'encrypt_name' => true,
                    );

                    $this->load->library('upload', $config);

                    if (!$this->upload->do_upload('paymentimage')) {
                        throw new Exception($this->upload->display_errors('', ''));
                    } else {
                        $upload = $this->upload->data();

                        $execute['paymentimage'] = $upload['file_name'];
                    }
                } else {
                    throw new Exception('Gambar pembayaran wajib diisi');
                }
                $this->mspaymentgateway->insert($execute);
            } else {
                if ($paymentimage['size'] > 0) {
                    $config = array(
                        'upload_path' => './uploads/',
                        'allowed_types' => 'jpg|jpeg|png',
                        'encrypt_name' => true,
                    );

                    $this->load->library('upload', $config);

                    if (!$this->upload->do_upload('paymentimage')) {
                        throw new Exception($this->upload->display_errors('', ''));
                    } else {
                        $upload = $this->upload->data();

                        $execute['paymentimage'] = $upload['file_name'];
                    }
                }
                $this->mspaymentgateway->update(array(
                    'id' => $get->id
                ), $execute);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda belum memiliki lisensi, silahkan beli lisensi terlebih dahulu');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('paymentgateway/manual/add', array(), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_add()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda belum memiliki lisensi, silahkan beli lisensi terlebih dahulu');
            }

            $paymentmethod = getPost('paymentmethod');
            $accountname = getPost('accountname');
            $accountnumber = getPost('accountnumber');
            $minnominal = getPost('minnominal', 0);
            $maxnominal = getPost('maxnominal', 0);
            $isunique = getPost('isunique');
            $uniqueadmin = getPost('uniqueadmin');
            $paymenttype = getPost('paymenttype');
            $isbonus = getPost('isbonus');
            $bonustype = getPost('bonustype');
            $nominalbonus = getPost('nominalbonus', 0);
            $isfee = getPost('isfee');
            $feetype = getPost('feetype');
            $nominalfee = getPost('nominalfee', 0);
            $description = getPost('description', null);

            if ($paymentmethod == null) {
                throw new Exception('Nama pembayaran wajib diisi');
            } else if ($accountname == null) {
                throw new Exception('Atas nama wajib diisi');
            } else if ($accountnumber == null) {
                if ($paymenttype == 'Normal') {
                    throw new Exception('Nomor rekening wajib diisi');
                }
            } else if (!is_numeric($minnominal)) {
                throw new Exception('Minimal Topup harus berupa angka');
            } else if (!is_numeric($maxnominal)) {
                throw new Exception('Maksimal Topup harus berupa angka');
            } else if ($minnominal < 0) {
                throw new Exception('Minimal Topup tidak boleh kurang dari 0');
            } else if ($maxnominal < 0) {
                throw new Exception('Maksimal Topup tidak boleh kurang dari 0');
            } else if ($paymenttype != 'Normal' && $paymenttype != 'Scan') {
                throw new Exception('Tipe Pembayaran tidak ditemukan');
            } else if ($isbonus) {
                if ($bonustype != 'Persentase' && $bonustype != 'Nominal') {
                    throw new Exception('Tipe Bonus tidak ditemukan');
                } else if (!is_numeric($nominalbonus)) {
                    throw new Exception('Nominal Bonus harus berupa angka');
                } else if ($nominalbonus < 0) {
                    throw new Exception('Nominal Bonus tidak boleh kurang dari 0');
                } else if ($bonustype == 'Persentase' && $nominalbonus > 100) {
                    throw new Exception('Nominal Bonus tidak boleh lebih dari 100');
                }
            } else if ($isfee) {
                if ($feetype != 'Persentase' && $feetype != 'Nominal') {
                    throw new Exception('Tipe Fee tidak ditemukan');
                } else if (!is_numeric($nominalfee)) {
                    throw new Exception('Nominal Fee harus berupa angka');
                } else if ($nominalfee < 0) {
                    throw new Exception('Nominal Fee tidak boleh kurang dari 0');
                } else if ($feetype == 'Persentase' && $nominalfee > 100) {
                    throw new Exception('Nominal Fee tidak boleh lebih dari 100');
                }
            } else {
                $paymentmethod = removeSymbol($paymentmethod);
                $accountname = removeSymbol($accountname);
                $accountnumber = removeSymbol($accountnumber);
            }

            if (getCurrentUser(getCurrentIdUser())->licenseid == null) {
                $get = $this->mspaymentmethod->total(array(
                    'userid' => getCurrentIdUser()
                ));

                if ($get >= 2) {
                    throw new Exception('Akun gratis hanya dapat menambahkan 2 metode pembayaran manual');
                }
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['paymentmethod'] = $paymentmethod;
            $insert['accountname'] = $accountname;
            $insert['accountnumber'] = $paymenttype == 'Normal' ? $accountnumber : null;
            $insert['minnominal'] = $minnominal;
            $insert['maxnominal'] = $maxnominal;
            $insert['isunique'] = $isunique ? 1 : 0;
            $insert['uniqueadmin'] = $isunique ? ($uniqueadmin == 1) : 0;
            $insert['description'] = $description;

            $config = array();
            $config['allowed_types'] = 'jpeg|jpg|png';
            $config['encrypt_name'] = true;
            $config['upload_path'] = './uploads';

            $this->load->library('upload', $config);

            if (isset($_FILES['paymentimage']['size']) && $_FILES['paymentimage']['size'] > 0) {
                if ($this->upload->do_upload('paymentimage')) {
                    $insert['paymentimage'] = $this->upload->data('file_name');
                } else {
                    throw new Exception($this->upload->display_errors('', ''));
                }
            } else {
                throw new Exception('Gambar pembayaran wajib diisi');
            }

            if ($paymenttype == 'Scan') {
                $config = array();
                $config['allowed_types'] = 'jpeg|jpg|png';
                $config['encrypt_name'] = true;
                $config['upload_path'] = './uploads';

                $this->load->library('upload', $config);

                if ($this->upload->do_upload('image')) {
                    $insert['image'] = $this->upload->data('file_name');
                } else {
                    throw new Exception($this->upload->display_errors('', ''));
                }
            }

            if ($isbonus) {
                $insert['isbonus'] = 1;
                $insert['bonustype'] = $bonustype;
                $insert['nominalbonus'] = $nominalbonus;
            } else {
                $insert['isbonus'] = 0;
                $insert['bonustype'] = null;
                $insert['nominalbonus'] = 0;
            }

            if ($isfee) {
                $insert['feetype'] = $feetype;
                $insert['nominalfee'] = $nominalfee;
            } else {
                $insert['feetype'] = null;
                $insert['nominalfee'] = 0;
            }

            $this->mspaymentmethod->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->mspaymentmethod->get(array(
                'userid' => getCurrentIdUser(),
                'id' => $id
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('paymentgateway/manual/edit', array(
                    'paymentmethod' => $row
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_edit()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = stringEncryption('decrypt', getPost('id'));
            $paymentmethod = getPost('paymentmethod');
            $accountname = getPost('accountname');
            $accountnumber = getPost('accountnumber');
            $minnominal = getPost('minnominal', 0);
            $maxnominal = getPost('maxnominal', 0);
            $isunique = getPost('isunique');
            $uniqueadmin = getPost('uniqueadmin');
            $paymenttype = getPost('paymenttype');
            $isbonus = getPost('isbonus');
            $bonustype = getPost('bonustype');
            $nominalbonus = getPost('nominalbonus', 0);
            $isfee = getPost('isfee');
            $feetype = getPost('feetype');
            $nominalfee = getPost('nominalfee', 0);
            $description = getPost('description', null);

            if ($paymentmethod == null) {
                throw new Exception('Nama pembayaran wajib diisi');
            } else if ($accountname == null) {
                throw new Exception('Atas nama wajib diisi');
            } else if ($accountnumber == null) {
                if ($paymenttype == 'Normal') {
                    throw new Exception('Nomor rekening wajib diisi');
                }
            } else if (!is_numeric($minnominal)) {
                throw new Exception('Minimal Topup harus berupa angka');
            } else if (!is_numeric($maxnominal)) {
                throw new Exception('Maksimal Topup harus berupa angka');
            } else if ($minnominal < 0) {
                throw new Exception('Minimal Topup tidak boleh kurang dari 0');
            } else if ($maxnominal < 0) {
                throw new Exception('Maksimal Topup tidak boleh kurang dari 0');
            } else if ($paymenttype != 'Normal' && $paymenttype != 'Scan') {
                throw new Exception('Tipe Pembayaran tidak ditemukan');
            } else if ($isbonus) {
                if ($bonustype != 'Persentase' && $bonustype != 'Nominal') {
                    throw new Exception('Tipe Bonus tidak ditemukan');
                } else if (!is_numeric($nominalbonus)) {
                    throw new Exception('Nominal Bonus harus berupa angka');
                } else if ($nominalbonus < 0) {
                    throw new Exception('Nominal Bonus tidak boleh kurang dari 0');
                } else if ($bonustype == 'Persentase' && $nominalbonus > 100) {
                    throw new Exception('Nominal Bonus tidak boleh lebih dari 100');
                }
            } else if ($isfee) {
                if ($feetype != 'Persentase' && $feetype != 'Nominal') {
                    throw new Exception('Tipe Fee tidak ditemukan');
                } else if (!is_numeric($nominalfee)) {
                    throw new Exception('Nominal Fee harus berupa angka');
                } else if ($nominalfee < 0) {
                    throw new Exception('Nominal Fee tidak boleh kurang dari 0');
                } else if ($feetype == 'Persentase' && $nominalfee > 100) {
                    throw new Exception('Nominal Fee tidak boleh lebih dari 100');
                }
            } else {
                $paymentmethod = removeSymbol($paymentmethod);
                $accountname = removeSymbol($accountname);
                $accountnumber = removeSymbol($accountnumber);
            }

            $get = $this->mspaymentmethod->total(array(
                'userid' => getCurrentIdUser(),
                'id' => $id
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $update = array();
            $update['paymentmethod'] = $paymentmethod;
            $update['accountname'] = $accountname;
            $update['accountnumber'] = $paymenttype == 'Normal' ? $accountnumber : null;
            $update['minnominal'] = $minnominal;
            $update['maxnominal'] = $maxnominal;
            $update['isunique'] = $isunique ? 1 : 0;
            $update['uniqueadmin'] = $isunique ? ($uniqueadmin == 1) : 0;
            $update['description'] = $description;

            if (isset($_FILES['paymentimage']['size']) && $_FILES['paymentimage']['size'] > 0) {
                $config = array();
                $config['allowed_types'] = 'jpeg|jpg|png';
                $config['encrypt_name'] = true;
                $config['upload_path'] = './uploads';

                $this->load->library('upload', $config);

                if ($this->upload->do_upload('paymentimage')) {
                    $update['paymentimage'] = $this->upload->data('file_name');
                } else {
                    throw new Exception($this->upload->display_errors('', ''));
                }
            }

            if ($paymenttype == 'Scan' && isset($_FILES['image']['size']) && $_FILES['image']['size'] > 0) {
                $config = array();
                $config['allowed_types'] = 'jpeg|jpg|png';
                $config['encrypt_name'] = true;
                $config['upload_path'] = './uploads';

                $this->load->library('upload', $config);

                if ($this->upload->do_upload('image')) {
                    $update['image'] = $this->upload->data('file_name');
                } else {
                    throw new Exception($this->upload->display_errors('', ''));
                }
            } else if ($paymenttype == 'Normal') {
                $update['image'] = null;
            }

            if ($isbonus) {
                $update['isbonus'] = 1;
                $update['bonustype'] = $bonustype;
                $update['nominalbonus'] = $nominalbonus;
            } else {
                $update['isbonus'] = 0;
                $update['bonustype'] = null;
                $update['nominalbonus'] = 0;
            }

            if ($isfee) {
                $update['feetype'] = $feetype;
                $update['nominalfee'] = $nominalfee;
            } else {
                $update['feetype'] = null;
                $update['nominalfee'] = 0;
            }

            $this->mspaymentmethod->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda belum memiliki lisensi');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->mspaymentmethod->total(array(
                'userid' => getCurrentIdUser(),
                'id' => $id
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $this->mspaymentmethod->delete(array(
                'id' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function channel_payments_duitku()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $paymentgateway = $this->mspaymentgateway->get(array(
                'userid' => getCurrentIdUser(),
                'type' => 'Payment Gateway',
            ));

            if ($paymentgateway->num_rows() == 0) {
                throw new Exception('Payment Gateway tidak ditemukan');
            }

            $row = $paymentgateway->row();

            if ($row->vendor != 'Duitku') {
                throw new Exception('Fitur ini hanya dapat diakses ketika payment gateway yang anda pilih adalah dari vendor Duitku');
            }

            $detail = json_decode(stringEncryption('decrypt', $row->detail));
            $addons = json_decode(stringEncryption('decrypt', $row->addons));

            $apikey = $detail->apikey;
            $merchantcode = $detail->merchantcode;

            $duitku_config = new Config($apikey, $merchantcode, ENVIRONMENT == 'development', true, false);

            $paymentAmount = "50000";
            $paymentMethod = Api::getPaymentMethod($paymentAmount, $duitku_config);

            $data = array();
            $data['payment_method'] = $paymentMethod;

            if (isset($addons->channel_payments)) {
                $data['channel_payments'] = $addons->channel_payments;
            } else {
                $data['channel_payments'] = array();
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('paymentgateway/duitku/channelpayments', $data, true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function channel_payments()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $paymentgateway = $this->mspaymentgateway->get(array(
                'userid' => getCurrentIdUser(),
                'type' => 'Payment Gateway',
            ));

            if ($paymentgateway->num_rows() == 0) {
                throw new Exception('Payment Gateway tidak ditemukan');
            }

            $row = $paymentgateway->row();

            if ($row->vendor != 'Tripay' && $row->vendor != 'iPaymu' && $row->vendor != 'Okeconnect' && $row->vendor != 'PayDisini') {
                throw new Exception('Fitur ini hanya dapat diakses ketika payment gateway yang anda pilih adalah dari vendor Tripay, vendor iPaymu, vendor Okeconnect atau PayDisini');
            }

            $addons = json_decode(stringEncryption('decrypt', $row->addons));

            $data = array();
            if (isset($addons->channel_payments)) {
                $data['channel_payments'] = $addons->channel_payments;
            } else {
                $data['channel_payments'] = array();
            }

            if (isset($addons->alias_payments)) {
                $data['alias_payments'] = $addons->alias_payments;
            } else {
                $data['alias_payments'] = array();
            }

            $content = '';

            if ($row->vendor == 'Tripay') {
                $content = 'paymentgateway/tripay/channelpayments';
            } else if ($row->vendor == 'iPaymu') {
                $content = 'paymentgateway/ipaymu/channelpayments';
            } else if ($row->vendor == 'Okeconnect') {
                $content = 'paymentgateway/okeconnect/channelpayments';
            } else if ($row->vendor == 'PayDisini') {
                $detail = json_decode(stringEncryption('decrypt', $row->detail));
                $paydisini = new PayDisini($detail->apikey);
                $paymentchannel = $paydisini->paymentChannel();

                if (isset($paymentchannel->success) && $paymentchannel->success == true) {
                    $data['channelpayments'] = $paymentchannel->data;
                    $content = 'paymentgateway/paydisini/channelpayments';
                } else {
                    throw new Exception($paymentchannel->msg ?? 'Gagal mengambil data');
                }
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view($content, $data, true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function enabled_payments()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $paymentgateway = $this->mspaymentgateway->get(array(
                'userid' => getCurrentIdUser(),
                'type' => 'Payment Gateway',
            ));

            if ($paymentgateway->num_rows() == 0) {
                throw new Exception('Payment Gateway tidak ditemukan');
            }

            $row = $paymentgateway->row();

            if ($row->vendor != 'Midtrans') {
                throw new Exception('Fitur ini hanya dapat diakses ketika payment gateway yang anda pilih adalah dari vendor Midtrans');
            }

            $addons = json_decode(stringEncryption('decrypt', $row->addons));

            $data = array();
            if (isset($addons->enabled_payments)) {
                $data['channel_payments'] = $addons->enabled_payments;
            } else {
                $data['channel_payments'] = array();
            }

            if (isset($addons->alias_payments)) {
                $data['alias_payments'] = $addons->alias_payments;
            } else {
                $data['alias_payments'] = array();
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('paymentgateway/midtrans/enabledpayments', $data, true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_channel_payments()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $paymentgateway = $this->mspaymentgateway->get(array(
                'userid' => getCurrentIdUser(),
                'type' => 'Payment Gateway',
            ));

            if ($paymentgateway->num_rows() == 0) {
                throw new Exception('Payment Gateway tidak ditemukan');
            }

            $row = $paymentgateway->row();

            if ($row->vendor != 'Tripay' && $row->vendor != 'iPaymu' && $row->vendor != 'Duitku' && $row->vendor != 'Okeconnect' && $row->vendor != 'PayDisini') {
                throw new Exception('Fitur ini hanya dapat diakses ketika payment gateway yang anda pilih adalah dari vendor Tripay, vendor iPaymu, vendor Duitku, vendor Okeconnect atau PayDisini');
            }

            $enabledpayments = getPost('channelpayments', []);
            $keypayments = getPost('keypayments', []);
            $valuepayments = getPost('valuepayments', []);
            $aliaspayments = getPost('aliaspayments', []);

            // set alias payments to array while enabled payments is exists by valuepayments
            $aliaspayments = array_map(function ($value) use ($keypayments, $aliaspayments) {
                return $aliaspayments[array_search($value, $keypayments)];
            }, $enabledpayments);

            $namepayments = array();
            $typepayments = array();

            if ($row->vendor == 'Tripay') {
                $tripaypayment = getTripayPayments();

                foreach ($enabledpayments as $key => $value) {
                    if (!in_array($value, array_keys($tripaypayment))) {
                        throw new Exception('Channel Payments tidak ditemukan');
                    }
                }
            } elseif ($row->vendor == 'iPaymu') {
                $ipaymupayment = getIpaymuPayments();

                foreach ($enabledpayments as $key => $value) {
                    if (!in_array($value, array_keys($ipaymupayment))) {
                        throw new Exception('Channel Payments tidak ditemukan');
                    }
                }
            } elseif ($row->vendor == 'Duitku') {
                $detail = json_decode(stringEncryption('decrypt', $row->detail));

                $apikey = $detail->apikey;
                $merchantcode = $detail->merchantcode;

                $duitku_config = new Config($apikey, $merchantcode, ENVIRONMENT == 'development', true, false);

                $paymentAmount = "50000";
                $paymentMethod = Api::getPaymentMethod($paymentAmount, $duitku_config);

                $paymentMethodKey = array();
                foreach (json_decode($paymentMethod)->paymentFee as $key => $value) {
                    $paymentMethodKey[$value->paymentMethod] = $value->paymentName;
                }

                foreach ($enabledpayments as $key => $value) {
                    if (!in_array($value, array_keys($paymentMethodKey))) {
                        throw new Exception('Channel Payments tidak ditemukan');
                    }

                    $namepayments[] = $paymentMethodKey[$value];
                }
            } elseif ($row->vendor == 'Okeconnect') {
                $okeconnect = getOkeconnectPayments();

                foreach ($enabledpayments as $key => $value) {
                    if (!in_array($value, array_keys($okeconnect))) {
                        throw new Exception('Channel Payments tidak ditemukan');
                    }
                }
            } else if ($row->vendor == 'PayDisini') {
                $detail = json_decode(stringEncryption('decrypt', $row->detail));
                $paydisini = new PayDisini($detail->apikey);
                $paymentchannel = $paydisini->paymentChannel();

                if (isset($paymentchannel->success) && $paymentchannel->success == true) {
                    $paymentchannel = $paymentchannel->data;

                    foreach ($enabledpayments as $key => $value) {
                        if (!in_array($value, array_column($paymentchannel, 'id'))) {
                            throw new Exception('Channel Payments tidak ditemukan');
                        }

                        $namepayments[] = $paymentchannel[array_search($value, array_column($paymentchannel, 'id'))]->name;
                        $typepayments[] = $paymentchannel[array_search($value, array_column($paymentchannel, 'id'))]->type;
                    }
                }
            }

            if ($row->vendor != 'PayDisini') {
                $addons = array(
                    'channel_payments' => $enabledpayments,
                    'channel_payments_name' => $namepayments,
                    'alias_payments' => $aliaspayments
                );
            } else {
                $addons = array(
                    'channel_payments' => $enabledpayments,
                    'channel_payments_name' => $namepayments,
                    'channel_payments_type' => $typepayments,
                    'alias_payments' => $aliaspayments
                );
            }


            $update = array();
            $update['addons'] = stringEncryption('encrypt', json_encode($addons));

            $this->mspaymentgateway->update(array(
                'id' => $row->id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil disimpan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_enabled_payments()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $paymentgateway = $this->mspaymentgateway->get(array(
                'userid' => getCurrentIdUser(),
                'type' => 'Payment Gateway',
            ));

            if ($paymentgateway->num_rows() == 0) {
                throw new Exception('Payment Gateway tidak ditemukan');
            }

            $row = $paymentgateway->row();

            if ($row->vendor != 'Midtrans') {
                throw new Exception('Fitur ini hanya dapat diakses ketika payment gateway yang anda pilih adalah dari vendor Midtrans');
            }

            $enabledpayments = getPost('enabledpayments', []);
            $keypayments = getPost('keypayments', []);
            $valuepayments = getPost('valuepayments', []);
            $aliaspayments = getPost('aliaspayments', []);

            // set alias payments to array while enabled payments is exists by valuepayments
            $aliaspayments = array_map(function ($value) use ($keypayments, $aliaspayments) {
                return $aliaspayments[array_search($value, $keypayments)];
            }, $enabledpayments);

            $addons = array(
                'enabled_payments' => $enabledpayments,
                'alias_payments' => $aliaspayments
            );

            $update = array();
            $update['addons'] = stringEncryption('encrypt', json_encode($addons));

            $this->mspaymentgateway->update(array(
                'id' => $row->id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil disimpan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_disable()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->mspaymentmethod->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser(),
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $update = array();
            $update['isdisabled'] = 1;

            $this->mspaymentmethod->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menonaktifkan data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil dinonaktifkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_enable()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->mspaymentmethod->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser(),
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $update = array();
            $update['isdisabled'] = 0;

            $this->mspaymentmethod->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengaktifkan data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil diaktifkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_get_otp_code_ovo()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $phonenumber = getPost('phonenumber');

            if ($phonenumber == null) {
                throw new Exception('Nomor handphone wajib diisi');
            } else if (!is_numeric($phonenumber)) {
                throw new Exception('Nomor handphone harus berupa angka');
            }

            // convert phonenumber with prefix +62
            if (substr($phonenumber, 0, 1) == '0') {
                $phonenumber = '+62' . substr($phonenumber, 1);
            } else if (substr($phonenumber, 0, 1) == '8') {
                $phonenumber = '+62' . $phonenumber;
            }

            $ovo = new Ovo();
            $login = json_decode($ovo->sendOtp($phonenumber));

            if (isset($login->response_message) && $login->response_message == 'Success') {
                $execute = array();
                $execute['userid'] = getCurrentIdUser();
                $execute['type'] = 'OVO';
                $execute['vendor'] = 'OVO';
                $execute['detail'] = stringEncryption('encrypt', json_encode(array(
                    'phonenumber' => $phonenumber,
                    'token' => null,
                )));
                $execute['addons'] = stringEncryption('encrypt', json_encode(array(
                    'otp-ref-id' => $login->data->otp->otp_ref_id
                )));

                $get = $this->mspaymentgateway->get(array(
                    'userid' => getCurrentIdUser(),
                    'type' => 'OVO',
                    'vendor' => 'OVO',
                ));

                if ($get->num_rows() == 0) {
                    $this->mspaymentgateway->insert($execute);
                } else {
                    $rowPayment = $get->row();

                    $this->mspaymentgateway->update(array(
                        'id' => $rowPayment->id
                    ), $execute);
                }

                if ($this->db->trans_status() === FALSE) {
                    throw new Exception('Gagal mengirim kode otp');
                }

                $this->db->trans_commit();

                return JSONResponse(array(
                    'RESULT' => 'OK',
                    'MESSAGE' => 'Kode OTP berhasil dikirim',
                    'OTPREFID' => stringEncryption('encrypt', $login->data->otp->otp_ref_id)
                ));
            } else {
                log_message('error', '[OVO OTP] Response: ' . json_encode($login));

                throw new Exception('Gagal mengirim Kode OTP');
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_get_otp_code()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $phonenumber = getPost('phonenumber');

            if ($phonenumber == null) {
                throw new Exception('Nomor handphone wajib diisi');
            } else if (!is_numeric($phonenumber)) {
                throw new Exception('Nomor handphone harus berupa angka');
            }

            $gopay = new GojekPay();
            $login = json_decode($gopay->loginRequest($phonenumber));

            if (isset($login->success) && $login->success) {
                $execute = array();
                $execute['userid'] = getCurrentIdUser();
                $execute['type'] = 'GOPAY';
                $execute['vendor'] = 'GOPAY';
                $execute['detail'] = stringEncryption('encrypt', json_encode(array(
                    'phonenumber' => $phonenumber,
                    'token' => null
                )));
                $execute['addons'] = stringEncryption('encrypt', json_encode(array(
                    'otp-token' => $login->data->otp_token
                )));

                $get = $this->mspaymentgateway->get(array(
                    'userid' => getCurrentIdUser(),
                    'type' => 'GOPAY',
                    'vendor' => 'GOPAY'
                ));

                if ($get->num_rows() == 0) {
                    $this->mspaymentgateway->insert($execute);
                } else {
                    $rowPayment = $get->row();

                    $this->mspaymentgateway->update(array(
                        'id' => $rowPayment->id
                    ), $execute);
                }

                if ($this->db->trans_status() === FALSE) {
                    throw new Exception('Gagal mengirim kode otp');
                }

                $this->db->trans_commit();

                return JSONResponse(array(
                    'RESULT' => 'OK',
                    'MESSAGE' => 'Kode OTP berhasil dikirim',
                    'OTPTOKEN' => stringEncryption('encrypt', $login->data->otp_token)
                ));
            } elseif (isset($login->success) && $login->success == false) {
                throw new Exception($login->errors[0]->message_title . ' - ' . $login->errors[0]->message);
            } else {
                log_message('error', '[GOPAY OTP] Response: ' . json_encode($login));

                throw new Exception('Gagal mengirim Kode OTP');
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_login_ovo()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $otpcode = getPost('otpcode');
            $otprefid = getPost('otprefid');

            if ($otpcode == null) {
                throw new Exception('Kode OTP wajib diisi');
            } else if ($otprefid == null) {
                throw new Exception('Kode OTP wajib diisi');
            } else {
                $otprefid = stringEncryption('decrypt', $otprefid);
            }

            $get = $this->mspaymentgateway->get(array(
                'userid' => getCurrentIdUser(),
                'type' => 'OVO',
                'vendor' => 'OVO'
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Payment gateway tidak ditemukan');
            }

            $row = $get->row();

            // phonenumber, token
            $detail = json_decode(stringEncryption('decrypt', $row->detail));
            // otp-ref-id
            $addons = json_decode(stringEncryption('decrypt', $row->addons), true);

            if (isset($addons['otp-ref-id'])) {
                $ovo = new Ovo();
                $otptoken = json_decode($ovo->OTPVerify($detail->phonenumber, $addons['otp-ref-id'], $otpcode));

                if (isset($otptoken->response_message) && $otptoken->response_message == 'Success') {
                    $update_addons = array(
                        'otp-ref-id' => $otptoken->data->otp->otp_ref_id,
                        'otp-token' => $otptoken->data->otp->otp_token
                    );

                    $update = array();
                    $update['addons'] = stringEncryption('encrypt', json_encode($update_addons));

                    $this->mspaymentgateway->update(array(
                        'id' => $row->id
                    ), $update);

                    if ($this->db->trans_status() === FALSE) {
                        throw new Exception('Login gagal');
                    }

                    $this->db->trans_commit();

                    return JSONResponse(array(
                        'RESULT' => 'OK',
                        'MESSAGE' => 'Kode OTP berhasil diverifikasi',
                        'OTPREFID' => stringEncryption('encrypt', $otptoken->data->otp->otp_ref_id),
                        'OTPTOKEN' => stringEncryption('encrypt', $otptoken->data->otp->otp_token),
                    ));
                } else {
                    if ($otptoken != null) {
                        log_message_user('error', '[OVO LOGIN] Response: ' . json_encode($otptoken));
                    }

                    throw new Exception('Kode OTP tidak valid');
                }
            } else {
                throw new Exception('Kode OTP tidak ditemukan');
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_verify_ovo()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $phonenumber = getPost('phonenumber');
            $otprefid = getPost('otprefid');
            $otptoken = getPost('otptoken');
            $securitycode = getPost('securitycode');

            if ($phonenumber == null) {
                throw new Exception('Nomor handphone wajib diisi');
            } else if ($otprefid == null) {
                throw new Exception('Kode OTP wajib diisi');
            } else if ($otptoken == null) {
                throw new Exception('Kode OTP wajib diisi');
            } else if ($securitycode == null) {
                throw new Exception('Kode keamanan wajib diisi');
            } else if (!is_numeric($phonenumber)) {
                throw new Exception('Nomor handphone harus berupa angka');
            } else if (!is_numeric($securitycode)) {
                throw new Exception('Kode keamanan harus berupa angka');
            } else {
                $otprefid = stringEncryption('decrypt', $otprefid);
                $otptoken = stringEncryption('decrypt', $otptoken);
            }

            if (substr($phonenumber, 0, 1) == '0') {
                $phonenumber = '+62' . substr($phonenumber, 1);
            } else if (substr($phonenumber, 0, 1) == '8') {
                $phonenumber = '+62' . $phonenumber;
            }

            $get = $this->mspaymentgateway->get(array(
                'userid' => getCurrentIdUser(),
                'type' => 'OVO',
                'vendor' => 'OVO'
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Payment gateway tidak ditemukan');
            }

            $row = $get->row();

            $ovo = new Ovo();
            $authtoken = json_decode($ovo->getAuthToken($phonenumber, $otprefid, $otptoken, $securitycode));

            if (isset($authtoken->response_message) && $authtoken->response_message == 'Success') {
                $update_detail = array(
                    'phonenumber' => $phonenumber,
                    'token' => $authtoken->data->auth->access_token,
                    'refresh_token' => $authtoken->data->auth->refresh_token,
                );

                $update = array();
                $update['detail'] = stringEncryption('encrypt', json_encode($update_detail));

                $this->mspaymentgateway->update(array(
                    'id' => $row->id
                ), $update);

                if ($this->db->trans_status() === FALSE) {
                    throw new Exception('Login gagal');
                }

                $this->db->trans_commit();

                return JSONResponseDefault('OK', 'Login berhasil');
            } else {
                if ($authtoken != null) {
                    log_message_user('error', '[OVO LOGIN] Response: ' . json_encode($authtoken));
                }

                throw new Exception('Login gagal');
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_login_gopay()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $otpcode = getPost('otpcode');
            $otptoken = getPost('otptoken');

            if ($otpcode == null) {
                throw new Exception('Kode OTP wajib diisi');
            } else if (!is_numeric($otpcode)) {
                throw new Exception('Kode OTP harus berupa angka');
            } else if ($otptoken == null) {
                throw new Exception('Token OTP wajib diisi');
            } else {
                $otptoken = stringEncryption('decrypt', $otptoken);
            }

            $get = $this->mspaymentgateway->get(array(
                'userid' => getCurrentIdUser(),
                'type' => 'GOPAY',
                'vendor' => 'GOPAY'
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Payment gateway tidak ditemukan');
            }

            $row = $get->row();

            $detail = json_decode(stringEncryption('decrypt', $row->detail));
            $addons = json_decode(stringEncryption('decrypt', $row->addons), true);

            if (isset($addons['otp-token']) && $addons['otp-token'] == $otptoken) {
                $gopay = new GojekPay();
                $authtoken = json_decode($gopay->getAuthToken($otptoken, $otpcode));

                if (isset($authtoken->access_token)) {
                    $update_detail = array(
                        'phonenumber' => $detail->phonenumber,
                        'token' => $authtoken->access_token
                    );

                    $update = array();
                    $update['detail'] = stringEncryption('encrypt', json_encode($update_detail));

                    $this->mspaymentgateway->update(array(
                        'id' => $row->id
                    ), $update);

                    if ($this->db->trans_status() === FALSE) {
                        throw new Exception('Login gagal');
                    }

                    $this->db->trans_commit();

                    return JSONResponseDefault('OK', 'Login berhasil');
                } else {
                    if ($authtoken != null) {
                        log_message_user('error', '[GOPAY LOGIN] Response: ' . json_encode($authtoken));
                    }

                    throw new Exception('Login gagal');
                }
            } else {
                throw new Exception('Kode OTP tidak valid');
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_disable_paymentgateway()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $method = getPost('method');

            $get = $this->mspaymentgateway->total(array(
                'userid' => getCurrentIdUser(),
                'type' => $method
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $update = array();
            $update['isdisabled'] = 1;

            $this->mspaymentgateway->update(array(
                'userid' => getCurrentIdUser(),
                'type' => $method
            ), $update);

            if ($method == 'Bank BCA') {
                $update2 = array();
                $update2['bca_bot'] = null;

                $this->msusers->update(array('id' => getCurrentIdUser()), $update2);
            }

            if ($this->db->trans_status() === false) {
                throw new Exception('Gagal dinonaktifkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil dinonaktifkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_enable_paymentgateway()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $method = getPost('method');

            $get = $this->mspaymentgateway->total(array(
                'userid' => getCurrentIdUser(),
                'type' => $method
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $update = array();
            $update['isdisabled'] = 0;

            $this->mspaymentgateway->update(array(
                'userid' => getCurrentIdUser(),
                'type' => $method
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal diaktifkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil diaktifkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_disable_bcabot()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $get = $this->mspaymentgateway->total(array(
                'userid' => getCurrentIdUser(),
                'type' => 'Bank BCA',
                "(isdisabled = 0 OR isdisabled IS NULL) =" => true,
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $getusers = $this->msusers->get(array(
                'id' => getCurrentIdUser()
            ));

            if ($getusers->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $getusers->row();

            if ($row->bca_bot != 1) {
                throw new Exception('Fitur ini tidak dapat dinonaktifkan karena anda telah mengnonaktifkan fitur BCA BOT');
            }

            $update = array();
            $update['bca_bot'] = null;

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal dinonaktifkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil dinonaktifkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_enable_bcabot()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $get = $this->mspaymentgateway->total(array(
                'userid' => getCurrentIdUser(),
                'type' => 'Bank BCA',
                "(isdisabled = 0 OR isdisabled IS NULL) =" => true,
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $getusers = $this->msusers->get(array(
                'id' => getCurrentIdUser()
            ));

            if ($getusers->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $getusers->row();

            if ($row->bca_bot == 1) {
                throw new Exception('Fitur ini tidak dapat diaktifkan karena anda telah mengaktifkan fitur BCA BOT');
            }

            $update = array();
            $update['bca_bot'] = 1;

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal diaktifkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil diaktifkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function fee_payments()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $paymentgateway = $this->mspaymentgateway->get(array(
                'userid' => getCurrentIdUser(),
                'type' => 'Payment Gateway'
            ));

            if ($paymentgateway->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $paymentgateway->row();

            if ($row->vendor != 'Midtrans' && $row->vendor != 'Tripay' && $row->vendor != 'iPaymu' && $row->vendor != 'Duitku' && $row->vendor != 'Okeconnect' && $row->vendor != 'PayDisini') {
                throw new Exception('Data tidak ditemukan');
            }

            $detail = json_decode(stringEncryption('decrypt', $row->detail));
            $addons = json_decode(stringEncryption('decrypt', $row->addons));

            $data = array();
            if ($row->vendor == 'Midtrans') {
                if (isset($addons->enabled_payments)) {
                    $data['channel_payments'] = $addons->enabled_payments;
                } else {
                    $data['channel_payments'] = array();
                }
            } elseif ($row->vendor == 'Tripay' || $row->vendor == 'iPaymu' || $row->vendor == 'Duitku' || $row->vendor == 'Okeconnect' || $row->vendor == 'PayDisini') {
                $apikey = $detail->apikey;

                if ($row->vendor == 'Duitku') {
                    $merchantcode = $detail->merchantcode;

                    $duitku_config = new Config($apikey, $merchantcode, ENVIRONMENT == 'development', true, false);

                    $paymentAmount = "50000";
                    $paymentMethod = Api::getPaymentMethod($paymentAmount, $duitku_config);

                    $data['payment_method'] = $paymentMethod;
                } else if ($row->vendor == 'PayDisini') {
                    $paydisini = new Paydisini($apikey);
                    $paymentchannel = $paydisini->paymentChannel();

                    if (isset($paymentchannel->success) && $paymentchannel->success == true) {
                        $data['payment_channel'] = $paymentchannel->data;
                    } else {
                        $data['payment_channel'] = array();
                    }
                }

                if (isset($addons->channel_payments)) {
                    $data['channel_payments'] = $addons->channel_payments;
                } else {
                    $data['channel_payments'] = array();
                }
            }
            $data['row'] = $row;

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('paymentgateway/fee_payments', $data, true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_fee_payments()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $paymentgateway = $this->mspaymentgateway->get(array(
                'userid' => getCurrentIdUser(),
                'type' => 'Payment Gateway'
            ));

            if ($paymentgateway->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $paymentgateway->row();

            if ($row->vendor != 'Midtrans' && $row->vendor != 'Tripay' && $row->vendor != 'iPaymu' && $row->vendor != 'Duitku' && $row->vendor != 'Okeconnect' && $row->vendor != 'PayDisini') {
                throw new Exception('Data tidak ditemukan');
            }

            $duitkupayment = array();
            if ($row->vendor == 'Duitku') {
                $detail = json_decode(stringEncryption('decrypt', $row->detail));

                $apikey = $detail->apikey;
                $merchantcode = $detail->merchantcode;

                $duitku_config = new Config($apikey, $merchantcode, ENVIRONMENT == 'development', true, false);

                $paymentAmount = "50000";
                $paymentMethod = Api::getPaymentMethod($paymentAmount, $duitku_config);

                foreach (json_decode($paymentMethod)->paymentFee as $key => $value) {
                    $duitkupayment[$value->paymentMethod] = $value->paymentName;
                }
            }

            $paymentname = getPost('paymentname', []);
            $fee = getPost('fee', []);
            $feetype = getPost('feetype', []);
            $paymentimage = isset($_FILES['paymentimage']) ? $_FILES['paymentimage'] : [];

            $midtranspayment = getMidtransPayments();
            $tripaypayment = getTripayPayments();
            $ipaymupayment = getiPaymuPayments();
            $okeconnectpayment = getOkeconnectPayments();
            $detailapi = json_decode(stringEncryption('decrypt', $row->detail));
            $paydisini = new Paydisini($detailapi->apikey);
            $paymentchannel = $paydisini->paymentChannel();

            foreach ($paymentname as $key => $value) {
                if ($fee[$key] != null && !is_numeric($fee[$key])) {
                    throw new Exception('Fee harus berupa angka');
                } else if (!in_array(($feetype[$key] != null ? $feetype[$key] : 'Nominal'), ['Nominal', 'Persentase'])) {
                    throw new Exception('Tipe fee tidak valid');
                } else if ($feetype[$key] == 'Persentase' && ($fee[$key] ?? 0) > 100) {
                    throw new Exception('Fee tidak boleh lebih dari 100%');
                } else {
                    if ($row->vendor == 'Midtrans' && !array_key_exists($value, $midtranspayment)) {
                        throw new Exception('Nama pembayaran tidak valid');
                    } else if ($row->vendor == 'Tripay' && !array_key_exists($value, $tripaypayment)) {
                        throw new Exception('Nama pembayaran tidak valid');
                    } else if ($row->vendor == 'iPaymu' && !array_key_exists($value, $ipaymupayment)) {
                        throw new Exception('Nama pembayaran tidak valid');
                    } else if ($row->vendor == 'Duitku' && !array_key_exists($value, $duitkupayment)) {
                        throw new Exception('Nama pembayaran tidak valid');
                    } else if ($row->vendor == 'Okeconnect' && !array_key_exists($value, $okeconnectpayment)) {
                        throw new Exception('Nama pembayaran tidak valid');
                    } else if ($row->vendor == 'PayDisini') {
                        if (isset($paymentchannel->success) && $paymentchannel->success == true) {
                            $paymentchannel = $paymentchannel->data;
                            if (!in_array($value, array_column($paymentchannel, 'id'))) {
                                throw new Exception('Nama pembayaran tidak valid');
                            }
                        }
                    }
                }

                $get = $this->feepaymentgateway->get(array(
                    'paymentgatewayid' => $row->id,
                    'paymentname' => $value
                ));

                $execute = array();
                $execute['paymentgatewayid'] = $row->id;
                $execute['paymentname'] = $value;
                $execute['fee'] = $fee[$key] ?? 0;
                $execute['feetype'] = $feetype[$key] != null ? $feetype[$key] : 'Nominal';
                $execute['vendor'] = $row->vendor;

                $config = array();
                $config['allowed_types'] = 'jpeg|jpg|png';
                $config['encrypt_name'] = true;
                $config['upload_path'] = './uploads';

                $this->load->library('upload', $config);
                if ($get->num_rows() == 0) {
                    $execute['createddate'] = getCurrentDate();
                    $execute['createdby'] = getCurrentIdUser();

                    if (isset($paymentimage['size'][$key]) && $paymentimage['size'][$key] > 0) {
                        $_FILES['paymentimage[]'] = array(
                            'name' => $paymentimage['name'][$key],
                            'type' => $paymentimage['type'][$key],
                            'tmp_name' => $paymentimage['tmp_name'][$key],
                            'error' => $paymentimage['error'][$key],
                            'size' => $paymentimage['size'][$key]
                        );

                        if ($this->upload->do_upload('paymentimage[]')) {
                            $upload = $this->upload->data();

                            $execute['paymentimage'] = $upload['file_name'];
                        } else {
                            throw new Exception($this->upload->display_errors('', ''));
                        }
                    } else {
                        throw new Exception('Gambar pembayaran wajib diisi');
                    }

                    $this->feepaymentgateway->insert($execute);
                } else {
                    $execute['updateddate'] = getCurrentDate();
                    $execute['updatedby'] = getCurrentIdUser();

                    if (isset($paymentimage['size'][$key]) && $paymentimage['size'][$key] > 0) {
                        $_FILES['paymentimage[]'] = array(
                            'name' => $paymentimage['name'][$key],
                            'type' => $paymentimage['type'][$key],
                            'tmp_name' => $paymentimage['tmp_name'][$key],
                            'error' => $paymentimage['error'][$key],
                            'size' => $paymentimage['size'][$key]
                        );

                        if ($this->upload->do_upload('paymentimage[]')) {
                            $upload = $this->upload->data();
                            $execute['paymentimage'] = $upload['file_name'];
                        } else {
                            throw new Exception($this->upload->display_errors('', ''));
                        }
                    } else if ($get->row()->paymentimage == null) {
                        throw new Exception('Gambar pembayaran wajib diisi');
                    }

                    $this->feepaymentgateway->update(array(
                        'paymentgatewayid' => $row->id,
                        'paymentname' => $value
                    ), $execute);
                }
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menyimpan data');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function buy_notification_handler()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $key = getPost('key');
            $key = stringEncryption('decrypt', $key);

            $get = $this->msnotificationhandlerpackage->get(array(
                'id' => $key,
                'isactive' => 1
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('paymentgateway/buy_notification_handler', array(
                    'row' => $row
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_buy_notification_handler()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $key = getPost('key');
            $key = stringEncryption('decrypt', $key);

            $get = $this->msnotificationhandlerpackage->get(array(
                'id' => $key,
                'isactive' => 1
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $currentbalance = getCurrentBalance(getCurrentIdUser(), true);

            if ($currentbalance < $row->price) {
                throw new Exception('Saldo anda tidak mencukupi');
            }

            $userbuy = $this->userbuynotificationhandler->total(array(
                'notificationid' => $row->id,
                'userid' => getCurrentIdUser()
            ));

            if ($userbuy > 0) {
                throw new Exception('Anda sudah membeli paket ini');
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['notificationid'] = $row->id;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->userbuynotificationhandler->insert($insert);

            $inserthistorybalance = array();
            $inserthistorybalance['userid'] = getCurrentIdUser();
            $inserthistorybalance['type'] = 'OUT';
            $inserthistorybalance['nominal'] = $row->price;
            $inserthistorybalance['currentbalance'] = $currentbalance;
            $inserthistorybalance['description'] = 'Pembelian Notification Handler';
            $inserthistorybalance['createdby'] = getCurrentIdUser();
            $inserthistorybalance['createddate'] = getCurrentDate();

            $this->historybalance->insert($inserthistorybalance);

            $update = array();
            $update['balance'] = $currentbalance - $row->price;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal membeli paket');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil membeli paket');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function configuration()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $key = getPost('key');
            $key = stringEncryption('decrypt', $key);

            $get = $this->userbuynotificationhandler->select('a.*, b.packagename, b.isqr')
                ->join('msnotificationhandlerpackage b', 'b.id = a.notificationid')
                ->get(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.id' => $key,
                    'b.isactive' => 1
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('paymentgateway/configuration', array(
                    'row' => $row
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_configuration()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $key = getPost('key');
            $key = stringEncryption('decrypt', $key);
            $accountname = getPost('accountname');
            $accountnumber = getPost('accountnumber');
            $qrcode = isset($_FILES['qrimage']) ? $_FILES['qrimage'] : null;
            $paymentimage = isset($_FILES['paymentimage']) ? $_FILES['paymentimage'] : null;
            $minnominal = getPost('minnominal', 0);
            $maxnominal = getPost('maxnominal', 0);
            $isbonusconfiguration = getPost('isbonusconfiguration', 0);
            $bonustypeconfiguration = getPost('bonustypeconfiguration');
            $nominalbonusconfiguration = getPost('nominalbonusconfiguration', 0);
            $isfeeconfiguration = getPost('isfeeconfiguration', 0);
            $feetypeconfiguration = getPost('feetypeconfiguration');
            $nominalfeeconfiguration = getPost('nominalfeeconfiguration', 0);

            if ($accountname == null) {
                throw new Exception('Nama akun wajib diisi');
            } else if (!is_numeric($minnominal)) {
                throw new Exception('Nominal minimal harus berupa angka');
            } else if (!is_numeric($maxnominal)) {
                throw new Exception('Nominal maksimal harus berupa angka');
            } else if ($minnominal < 0) {
                throw new Exception('Nominal minimal tidak boleh kurang dari 0');
            } else if ($maxnominal < 0) {
                throw new Exception('Nominal maksimal tidak boleh kurang dari 0');
            } else if ($minnominal > $maxnominal && $maxnominal != 0) {
                throw new Exception('Nominal minimal tidak boleh lebih besar dari nominal maksimal');
            } else if ($isbonusconfiguration == 1 && $bonustypeconfiguration == null) {
                throw new Exception('Tipe bonus wajib diisi');
            } else if ($isbonusconfiguration == 1 && !in_array($bonustypeconfiguration, ['Nominal', 'Persentase'])) {
                throw new Exception('Tipe bonus tidak valid');
            } else if ($isbonusconfiguration == 1 && !is_numeric($nominalbonusconfiguration)) {
                throw new Exception('Jumlah bonus harus berupa angka');
            } else if ($isbonusconfiguration == 1 && $bonustypeconfiguration == 'Persentase' && $nominalbonusconfiguration > 100) {
                throw new Exception('Jumlah bonus tidak boleh lebih dari 100%');
            } else if ($isfeeconfiguration == 1 && $feetypeconfiguration == null) {
                throw new Exception('Tipe Biaya wajib diisi');
            } else if ($isfeeconfiguration == 1 && !in_array($feetypeconfiguration, ['Nominal', 'Persentase'])) {
                throw new Exception('Tipe Biaya tidak valid');
            } else if ($isfeeconfiguration == 1 && !is_numeric($nominalfeeconfiguration)) {
                throw new Exception('Jumlah biaya harus berupa angka');
            } else if ($isfeeconfiguration == 1 && $feetypeconfiguration == 'Persentase' && $nominalfeeconfiguration > 100) {
                throw new Exception('Jumlah biaya tidak boleh lebih dari 100%');
            }

            $get = $this->userbuynotificationhandler->select('a.*, b.packagename, b.isqr')
                ->join('msnotificationhandlerpackage b', 'b.id = a.notificationid')
                ->get(array(
                    'a.userid' => getCurrentIdUser(),
                    'a.id' => $key,
                    'b.isactive' => 1
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $update = array();
            $update['accountname'] = $accountname;

            if ($row->isqr == 1) {
                if ($row->qrimage == null && $qrcode['size'] == 0) {
                    throw new Exception('QR Code wajib diisi');
                }

                if ($qrcode['size'] > 0) {
                    $config = array(
                        'upload_path' => './uploads/',
                        'allowed_types' => 'jpg|jpeg|png',
                        'encrypt_name' => true,
                    );

                    $this->load->library('upload', $config);

                    if (!$this->upload->do_upload('qrimage')) {
                        throw new Exception($this->upload->display_errors('', ''));
                    } else {
                        $upload = $this->upload->data();

                        $update['qrimage'] = $upload['file_name'];
                        $update['accountnumber'] = null;
                    }
                }
            } else {
                if ($accountnumber == null) {
                    throw new Exception('Nomor Rekening wajib diisi');
                }

                $update['accountnumber'] = $accountnumber;
                $update['qrimage'] = null;
            }

            if ($paymentimage['size'] > 0) {
                $config = array(
                    'upload_path' => './uploads/',
                    'allowed_types' => 'jpg|jpeg|png',
                    'encrypt_name' => true,
                );

                $this->load->library('upload', $config);

                if (!$this->upload->do_upload('paymentimage')) {
                    throw new Exception($this->upload->display_errors('', ''));
                } else {
                    $upload = $this->upload->data();

                    $update['paymentimage'] = $upload['file_name'];
                }
            } else {
                throw new Exception('Gambar pembayaran wajib diisi');
            }

            $update['minnominal'] = $minnominal;
            $update['maxnominal'] = $maxnominal;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            if ($isbonusconfiguration == 1) {
                $update['isbonus'] = $isbonusconfiguration;
                $update['bonustype'] = $bonustypeconfiguration;
                $update['nominalbonus'] = $nominalbonusconfiguration;
            } else {
                $update['isbonus'] = null;
                $update['bonustype'] = null;
                $update['nominalbonus'] = null;
            }

            if ($isfeeconfiguration == 1) {
                $update['isfee'] = $isfeeconfiguration;
                $update['feetype'] = $feetypeconfiguration;
                $update['nominalfee'] = $nominalfeeconfiguration;
            } else {
                $update['isfee'] = null;
                $update['feetype'] = null;
                $update['nominalfee'] = null;
            }

            $this->userbuynotificationhandler->update(array(
                'id' => $row->id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menyimpan data');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function disable_notification_handler()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $key = getPost('key');
            $key = stringEncryption('decrypt', $key);
            $isdisabled = getPost('isdisabled');

            $get = $this->userbuynotificationhandler->get(array(
                'a.id' => $key,
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $update = array();
            $update['isdisabled'] = $isdisabled;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->userbuynotificationhandler->update(array(
                'id' => $row->id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal dinonaktifkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menyimpan data');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function enable_notification_handler()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $key = getPost('key');
            $key = stringEncryption('decrypt', $key);
            $isdisabled = getPost('isdisabled');

            $get = $this->userbuynotificationhandler->get(array(
                'a.id' => $key,
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $update = array();
            $update['isdisabled'] = $isdisabled;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->userbuynotificationhandler->update(array(
                'id' => $row->id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal diaktifkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menyimpan data');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
