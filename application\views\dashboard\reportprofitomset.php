<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Laporan Profit dan Omset: <?= $report->companyname ?></h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <div class="modal-body">
            <input type="hidden" name="id" value="<?= stringEncryption('encrypt', $report->id) ?>">

            <div class="mb-7">
                <label class="col-form-label fw-semibold fs-6 pt-0">Bulan</label>
                <input type="month" name="month" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
            </div>

            <div class="mb-7" id="headerdatatables" style="display:none;">
                <table class="table table-striped table-row-bordered gy-5 datatables-reportprofitomset">
                    <thead>
                        <tr class="fw-semibold fs-6 text-muted">
                            <th>Tanggal</th>
                            <th>Omset</th>
                            <th>Profit</th>
                        </tr>
                    </thead>

                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
            <button type="button" class="btn btn-primary" onclick="filterProfitOmset()">Filter</button>
        </div>
    </div>
</div>

<script>
    function filterProfitOmset() {
        if ($('input[name="month"]').val() == '') {
            return Swal.fire({
                title: 'Gagal',
                text: 'Bulan tidak boleh kosong',
                icon: 'error',
            });
        }

        $('#headerdatatables').show();
        $('.datatables-reportprofitomset').DataTable().destroy();
        $('.datatables-reportprofitomset').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
                method: 'POST',
                data: {
                    id: $('input[name="id"]').val(),
                    date: $('input[name="month"]').val()
                }
            }
        });
    }
</script>