<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Ubah Kategori Produk</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmEditCategory" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <input type="hidden" name="id" value="<?= stringEncryption('encrypt', $row->id) ?>">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Nama Kategori</label>
                    <input type="text" name="name" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Kategori" value="<?= $row->name ?>" required>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Kategori Layanan</label>
                    <select name="servicecategory" class="form-select form-select-solid" required>
                        <option value="">- Pilih -</option>
                        <option value="PPOB" <?= $row->servicetype == 'PPOB' ? 'selected' : null ?>>PPOB</option>
                        <option value="SMM" <?= $row->servicetype == 'SMM' ? 'selected' : null ?>>SMM</option>
                    </select>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    $.AjaxRequest('#frmEditCategory', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success',
                }).then(function(result) {
                    return window.location.reload();
                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.MESSAGE,
                    icon: 'error',
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        }
    });
</script>