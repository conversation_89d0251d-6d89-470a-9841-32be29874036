<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Produk Media Sosial</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Produk</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Media Sosial</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <div>
        <a href="<?= base_url(uri_string() . '/bulk/delete') ?>" class="btn btn-danger fw-bold">Bulk Delete</a>
        <a href="<?= base_url(uri_string() . '/add') ?>" class="btn btn-dark fw-bold">Tambah Produk</a>
        <a href="<?= base_url(uri_string() . '/bulk/add') ?>" class="btn btn-dark fw-bold">Tambah Produk (Bulk)</a>
    </div>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="productcategory" class="col-form-label fw-semibold fs-6 pt-0">Kategori Produk</label>
                                <select class="form-select form-select-solid" id="productcategory" name="productcategory">
                                    <option value="">Pilih Kategori</option>
                                    <?php foreach ($category as $key => $value) : ?>
                                        <option value="<?= $value->category ?>"><?= $value->category ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="productstatus" class="col-form-label fw-semibold fs-6 pt-0">Status</label>
                                <select class="form-select form-select-solid" id="productstatus" name="productstatus">
                                    <option value="">Pilih Status</option>
                                    <option value="Normal">Normal</option>
                                    <option value="Gangguan">Gangguan</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <button type="button" class="btn btn-dark w-100" onclick="filter()">Filter</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-5">
                <div class="card-body">
                    <?php if ($last_update != null) : ?>
                        <div class="text-end">
                            <small>Sinkronisasi terakhir: <?= date('d F Y H:i:s', strtotime($last_update)) ?></small>
                        </div>
                    <?php endif; ?>

                    <table class="table table-striped table-row-bordered gy-5 datatables-product text-nowrap">
                        <thead>
                            <tr class="fw-semibold fs-6 text-muted">
                                <th>Vendor</th>
                                <th>Nama Produk</th>
                                <th>Kategori Produk</th>
                                <th>Harga (Vendor)</th>
                                <th>Harga</th>
                                <th>Profit</th>
                                <th>Tanggal Ditambahkan</th>
                                <th>Terakhir Diupdate</th>
                                <th>Status</th>
                                <th>Rata Rata Waktu Proses</th>
                                <th>Data Tambahan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>

                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('.datatables-product').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
                method: 'POST'
            }
        });
    };

    function addAdditional(name, id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: `${name} akan diberikan tambahan data pada saat pelanggan ingin melakukan pembelian`,
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/custom/add') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function removeAdditional(name, id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: `${name} akan dikembalikan ke status normal pada saat pelanggan ingin melakukan pembelian`,
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/custom/remove') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function filter() {
        let productcategory = $('#productcategory').val();
        let productstatus = $('#productstatus').val();

        $('.datatables-product').DataTable().destroy();
        $('.datatables-product').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url('product/smm/datatables') ?>',
                method: 'POST',
                data: {
                    kategori: productcategory,
                    status: productstatus
                }
            }
        });
    }
</script>