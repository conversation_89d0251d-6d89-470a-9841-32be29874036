<?php
defined('BASEPATH') or die('No direct script access allowed!');

class SMMIllusion
{
    private $_apikey;

    public function __construct($apikey)
    {
        $this->_apikey = $apikey;
    }

    public function services()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://smmillusion.com/api/v2");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_apikey&action=services");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function order($service, $target, $qty)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://smmillusion.com/api/v2");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_apikey&action=add&service=$service&link=$target&quantity=$qty");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function profile()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://smmillusion.com/api/v2");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_apikey&action=balance");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function status($orderid)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://smmillusion.com/api/v2");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_apikey&action=status&order=$orderid");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }
}
