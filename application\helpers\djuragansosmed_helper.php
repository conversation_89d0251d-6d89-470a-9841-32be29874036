<?php
defined('BASEPATH') or die('No direct script access allowed!');

class DjuraganSosmed
{
    private $_apikey;

    public function __construct($apikey)
    {
        $this->_apikey = $apikey;
    }

    public function services()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://djuragansosmed.com/api/v2");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_apikey&action=services");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function order($service, $link, $quantity, $runs = null, $interval = null)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://djuragansosmed.com/api/v2");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_apikey&action=add&service=$service&link=$link&quantity=$quantity&runs=$runs&interval=$interval");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function status($order)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://djuragansosmed.com/api/v2");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_apikey&action=status&order=$order");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function profile()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://djuragansosmed.com/api/v2");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "key=$this->_apikey&action=balance");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }
}
