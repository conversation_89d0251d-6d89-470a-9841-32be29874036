<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Tolak Pengajuan Playstore</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmUploadPlaystoreRejected" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body" style="max-height: 500px; overflow: auto;">
                <div class="row">
                    <div class="form-group mb-3">
                        <label for="rejectedreason" class="form-label">Alasan Penolakan</label>
                        <textarea class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" id="rejectedreason" name="rejectedreason" rows="5" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="rejectedimage" class="form-label">Bukti Penolakan</label>
                        <input type="file" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" id="rejectedimage" name="rejectedimage" required>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary waves-effect waves-light">Tolak</button>
            </div>
        </form>
    </div>
</div>

<script>
    $.AjaxRequest('#frmUploadPlaystoreRejected', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success'
                }).then(function(result2) {
                    return window.location.reload();
                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.MESSAGE,
                    icon: 'error'
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        }
    });
</script>