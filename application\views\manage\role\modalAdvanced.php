<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-xl">
    <div class="modal-content">
        <div class="modal-body">
            <div class="row">
                <div class="col-md-12">
                    <div class="mb-5">
                        <h3>Advanced</h3>


                    </div>
                </div>

                <table class="table table-striped table-row-bordered gy-5 datatables-advanced" style="width: 100%; max-height: 500px; overflow: auto;">
                    <thead>
                        <tr>
                            <th>Tipe Layanan</th>
                            <th>Range Harga</th>
                            <th>Tipe Diskon</th>
                            <th>Jumlah</th>
                        </tr>
                    </thead>

                    <tbody>
                        <?php foreach ($data as $value) : ?>
                            <tr>
                                <td><?= $value->servicetype ?></td>
                                <td><?= "Rp " . IDR($value->startrange) ?> - <?= "Rp " . IDR($value->endrange) ?></td>
                                <td><?= $value->discounttype ?></td>
                                <td>
                                    <?php if ($value->discounttype == 'Persentase') : ?>
                                        <?= $value->nominal ?>%
                                    <?php else : ?>
                                        <?= "Rp " . IDR($value->nominal) ?>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
        </div>
    </div>
</div>

<script>
    $('.datatables-advanced').DataTable();
</script>