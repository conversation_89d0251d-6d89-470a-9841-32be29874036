<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Ubah Member</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Management</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Member</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Ubah Member</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <a href="<?= base_url('manage/member') ?>" class="btn btn-danger fw-bold">Kembali</a>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <form id="frmEditMember" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="row align-items-center" id="rowparameter">
                            <div class="mb-7 col-md-12">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Nama</label>
                                <input class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" type="text" name="name" placeholder="Nama" value="<?= $member->name ?>" required>
                            </div>

                            <div class="mb-7 col-md-6">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Email</label>
                                <input class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" type="email" placeholder="Email" value="<?= $member->email ?>" readonly>
                            </div>

                            <div class="mb-7 col-md-6">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Password</label>
                                <input type="password" name="password" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Password">
                                <small>*Kosongkan jika tidak ingin diubah</small>
                            </div>

                            <div class="mb-7 col-md-12">
                                <label class="col-form-label fw-semibold fs-6 pt-0" for="googletag">Google Tag</label>
                                <textarea name="googletag" id="googletag" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" rows="5" placeholder="Google Tag"><?= $member->googletag ?></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $.AjaxRequest('#frmEditMember', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function(result) {
                        return window.location.href = '<?= base_url('manage/member') ?>';

                    })
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error'
                });
            }
        });
    };
</script>