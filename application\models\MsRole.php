<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Ms<PERSON>ole extends MY_Model
{
    protected $table = 'msrole';
    public $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.id, a.rolename, b.trxdiscount, a.discounttype, a.isdefault')
            ->from($this->table . ' a')
            ->join('msrolediscount b', 'b.roleid = a.id AND b.userid = ' . getCurrentIdUser(), 'LEFT');

        return $this->db;
    }
}
