<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ReportProfitMonthly extends MY_Model
{
    protected $table = 'trorder';
    public $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('MONTH(a.createddate) AS month, YEAR(a.createddate) AS year, SUM(a.price) AS omset, SUM(a.profit) AS profit')
            ->from($this->table . ' a')
            ->join('msusers b', 'b.id = a.userid')
            ->group_by('MONTH(a.createddate), YEAR(a.createddate)')
            ->order_by('a.createddate', 'DESC');

        return $this->db;
    }
}
