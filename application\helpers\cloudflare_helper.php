<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Cloudflare
{
    private $_account = null;
    private $_auth = null;

    public function __construct($account, $auth)
    {
        $this->_account = $account;
        $this->_auth = $auth;
    }

    public function zones($parameters = array())
    {
        $curl = curl_init();

        if (count($parameters) > 0) {
            curl_setopt($curl, CURLOPT_URL, 'https://api.cloudflare.com/client/v4/zones?' . http_build_query($parameters));
        } else {
            curl_setopt($curl, CURLOPT_URL, 'https://api.cloudflare.com/client/v4/zones');
        }

        curl_setopt_array($curl, array(
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Authorization: Bearer ' . $this->_auth,
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response);
    }

    public function create_zone($name)
    {
        $curl = curl_init();

        $params = array(
            'name' => $name,
            'account' => array(
                'id' => $this->_account,
            ),
        );

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.cloudflare.com/client/v4/zones',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($params),
            CURLOPT_HTTPHEADER => array(
                'Authorization: Bearer ' . $this->_auth,
                'Content-Type: application/json',
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response);
    }

    public function change_ssl_setting($zone, $value)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.cloudflare.com/client/v4/zones/' . $zone . '/settings/ssl',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'PATCH',
            CURLOPT_POSTFIELDS => json_encode(array(
                'value' => $value,
            )),
            CURLOPT_HTTPHEADER => array(
                'Authorization: Bearer ' . $this->_auth,
                'Content-Type: application/json',
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response);
    }

    public function create_dns_record($zone, $type, $name, $content, $ttl, $proxied = false)
    {
        $curl = curl_init();

        $params = array(
            'type' => $type,
            'name' => $name,
            'content' => $content,
            'ttl' => $ttl,
            'proxied' => $proxied,
        );

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.cloudflare.com/client/v4/zones/' . $zone . '/dns_records',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($params),
            CURLOPT_HTTPHEADER => array(
                'Authorization: Bearer ' . $this->_auth,
                'Content-Type: application/json',
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response);
    }

    public function create_firewall_rules($zone, $description, $action, $filter, $priority = 1)
    {
        $curl = curl_init();

        $param = array(
            'id' => md5(uniqid()),
            'action' => $action,
            'filter' => $filter,
            'description' => $description,
            'priority' => $priority,
        );

        $params = array();
        $params[] = $param;

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.cloudflare.com/client/v4/zones/' . $zone . '/firewall/rules',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($params),
            CURLOPT_HTTPHEADER => array(
                'X-Auth-Email: ' . String_Helper::CLOUDFLARE_EMAIL,
                'X-Auth-Key: ' . String_Helper::CLOUDFLARE_API_KEY,
                'Content-Type: application/json',
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response);
    }
}
