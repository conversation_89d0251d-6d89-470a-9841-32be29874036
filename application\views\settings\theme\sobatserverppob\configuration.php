<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Konfigurasi Tema: Sobat-ServerPPOB</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmConfiguration" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <input type="hidden" name="id" value="<?= $id ?>">
            <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

            <div class="modal-body">
                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">
                        Nama Singkatan Usaha (Depan)

                        <a href="javascript:;" data-bs-toggle="popover" data-bs-placement="right" title="Nama Singkatan Usaha (Depan)" data-bs-html="true" data-bs-content="<div>
<p class='m-0'>Teks ini ditampilkan pada saat ukuran layar mobile atau pada saat navbar ditutup.</p>
<p class='mb-2'>Contoh gambar:</p>
<img src='<?= base_url('assets/media/tutorial/sobat-serverppob/singkatan-depan.png') ?>' class='w-100'>
</div>">
                            <i class="far fa-question-circle"></i>
                        </a>
                    </label>

                    <input type="text" name="companyfirstabbr" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Singkatan Usaha (Depan)" value="<?= $config != null ? $config->companyfirstabbr : null ?>" required>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">
                        Nama Singkatan Usaha (Belakang)

                        <a href="javascript:;" data-bs-toggle="popover" data-bs-placement="right" title="Nama Singkatan Usaha (Belakang)" data-bs-html="true" data-bs-content="<div>
<p class='m-0'>Teks ini ditampilkan pada saat ukuran layar mobile atau pada saat navbar ditutup.</p>
<p class='mb-2'>Contoh gambar:</p>
<img src='<?= base_url('assets/media/tutorial/sobat-serverppob/singkatan-belakang.png') ?>' class='w-100'>
</div>">
                            <i class="far fa-question-circle"></i>
                        </a>
                    </label>

                    <input type="text" name="companylastabbr" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Singkatan Usaha (Belakang)" value="<?= $config != null ? $config->companylastabbr : null ?>" required>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">
                        Nama Usaha (Depan)

                        <a href="javascript:;" data-bs-toggle="popover" data-bs-placement="right" title="Nama Usaha (Depan)" data-bs-html="true" data-bs-content="<div>
<p class='m-0'>Teks ini ditampilkan pada saat halaman dashboard diakses menggunakan layar yang lebar atau pada saat navbar dibuka.</p>
<p class='mb-2'>Contoh gambar:</p>
<img src='<?= base_url('assets/media/tutorial/sobat-serverppob/usaha-depan.png') ?>' class='w-100'>
</div>">
                            <i class="far fa-question-circle"></i>
                        </a>
                    </label>

                    <input type="text" name="companyfirst" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Usaha (Depan)" value="<?= $config != null ? $config->companyfirst : null ?>" required>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">
                        Nama Usaha (Belakang)

                        <a href="javascript:;" data-bs-toggle="popover" data-bs-placement="right" title="Nama Usaha (Belakang)" data-bs-html="true" data-bs-content="<div>
<p class='m-0'>Teks ini ditampilkan pada saat halaman dashboard diakses menggunakan layar yang lebar atau pada saat navbar dibuka.</p>
<p class='mb-2'>Contoh gambar:</p>
<img src='<?= base_url('assets/media/tutorial/sobat-serverppob/usaha-belakang.png') ?>' class='w-100'>
</div>">
                            <i class="far fa-question-circle"></i>
                        </a>
                    </label>
                    <input type="text" name="companylast" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Usaha (Belakang)" value="<?= $config != null ? $config->companylast : null ?>" required>
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Link Facebook (Optional)</label>
                    <input type="text" name="facebook" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="https://facebook.com/" value="<?= $config != null ? ($config->facebook ?? '') : null ?>">
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Link Instagram (Optional)</label>
                    <input type="text" name="instagram" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="https://instagram.com/" value="<?= $config != null ? ($config->instagram ?? '') : null ?>">
                </div>

                <div class="mb-7">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Kontak WhatsApp (Optional)</label>
                    <input type="number" name="whatsapp" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="6285xxxxxxxxx" value="<?= $config != null ? ($config->whatsapp ?? '') : null ?>">
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
        </form>
    </div>
</div>

<script>
    KTApp.init();

    $.AjaxRequest('#frmConfiguration', {
        success: function(response) {
            if (response.RESULT == 'OK') {
                return Swal.fire({
                    title: 'Berhasil',
                    text: response.MESSAGE,
                    icon: 'success'
                }).then(function(result) {
                    return window.location.reload();

                });
            } else {
                return Swal.fire({
                    title: 'Gagal',
                    text: response.RESULT,
                    icon: 'error'
                });
            }
        },
        error: function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        }
    });
</script>