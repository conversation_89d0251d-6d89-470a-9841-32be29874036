<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Filter Informasi Layanan</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <div class="modal-body">
            <div class="row">
                <div class="mb-7 col-md-12">
                    <label class="col-form-label fw-semibold fs-6 pt-0">Kategori</label>
                    <select name="kategori" id="kategori" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                        <option value=""><PERSON>lih <PERSON></option>
                        <?php foreach ($category as $key => $value) : ?>
                            <option value="<?= $value->category ?>"><?= $value->category ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
            <button type="button" id="filter" class="btn btn-primary">Filter</button>
        </div>
    </div>
</div>

<script>
    $('#filter').click(function() {
        let kategori = $('#kategori').val();

        $('#ModalGlobal').modal('hide');
        $('.datatables-information').DataTable().destroy();
        $('.datatables-information').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url('product/information/datatables') ?>',
                method: 'POST',
                data: {
                    kategori: kategori
                }
            }
        });
    });
</script>