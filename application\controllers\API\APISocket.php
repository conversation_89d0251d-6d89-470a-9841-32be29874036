<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsUsers $msusers
 * @property TrOrder $trorder
 */
class APISocket extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsUsers', 'msusers');
        $this->load->model('TrOrder', 'trorder');
    }

    public function user()
    {
        $userid = getPost('userid');

        if ($userid == null) {
            return JSONResponse(array('RESULT' => 'FAILED'));
        }

        $userid = stringEncryption('decrypt', $userid);

        $get = $this->msusers->total(array(
            'id' => $userid
        ));

        if ($get == 0) {
            $get = $this->trorder->total(array(
                'clientip' => $userid
            ));

            if ($get == 0) {
                return JSONResponse(array('RESULT' => 'FAILED'));
            }
        }

        return JSONResponse(array('RESULT' => 'OK'));
    }
}
