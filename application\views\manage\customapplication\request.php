<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Pilihan Build Aplikasi</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <div class="modal-body">
            <div class="mb-7">
                <label class="col-form-label fw-semibold fs-6 pt-0">Tipe Aplikasi</label>
                <select name="applicationtype" id="applicationtype" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                    <option value="apk">.apk (Android Package Kit)</option>
                    <option value="aab">.aab (Android App Bundle)</option>
                </select>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
            <button type="button" class="btn btn-primary" onclick="buildApp()">Simpan</button>
        </div>
    </div>
</div>

<script>
    function buildApp() {
        return Swal.fire({
            title: 'Apakah anda yakin?',
            text: 'Permintaan anda akan masuk ke proses antrian?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/build') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        applicationtype: $('#applicationtype').val()
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }
</script>