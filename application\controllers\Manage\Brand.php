<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsCategory $mscategory
 * @property MsBrand $msbrand
 * @property CI_DB_mysqli_driver $db
 * @property MsProduct $msproduct
 */
class Brand extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsCategory', 'mscategory');
        $this->load->model('MsBrand', 'msbrand');
        $this->load->model('MsProduct', 'msproduct');
    }

    public function modal_add($category = null)
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            if ($category == null) {
                $servicetype = getPost('servicetype');

                if ($servicetype == null) {
                    throw new Exception('Tipe layanan tidak boleh kosong');
                } else if (!in_array($servicetype, array('PPOB', 'SMM'))) {
                    throw new Exception('Tipe layanan tidak valid');
                }

                if (getCurrentUser()->multivendor != 1) {
                    $currentkeys = getCurrentAPIKeys($servicetype);
                } else {
                    $currentkeys = null;
                }
            } else {
                $category = stringEncryption('decrypt', $category);

                if ($category == null) {
                    throw new Exception('Kategori produk tidak boleh kosong');
                }

                $servicetype = null;
                $currentkeys = null;
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/brand/add', array(
                    'category' => $this->mscategory->getCategory(getCurrentIdUser(), $servicetype, $currentkeys->vendor ?? null)->result(),
                    'currentcategory' => $category
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_add_brand($category = null)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $name = getPost('name');
            $productcategory = getPost('productcategory');

            if ($category == null) {
                $category = $productcategory;
            } else {
                $category = stringEncryption('decrypt', $category);
            }

            $servicesubtype = null;
            if (getCurrentUser()->multivendor != 1) {
                $currentvendor_ppob = getCurrentVendor('PPOB');
                $currentvendor_smm = getCurrentVendor('SMM');

                if ($currentvendor_ppob != null && $currentvendor_smm == null) {
                    $this->msproduct->where('vendor', $currentvendor_ppob);
                } else if ($currentvendor_ppob == null && $currentvendor_smm != null) {
                    $this->msproduct->where('vendor', $currentvendor_smm);
                } else if ($currentvendor_ppob != null && $currentvendor_smm != null) {
                    $this->msproduct->where(array(
                        "(vendor = '$currentvendor_ppob' OR vendor = '$currentvendor_smm') =" => true
                    ));
                } else {
                    $this->msproduct->where('vendor', null);
                }

                $this->msproduct->where('vendorid', null);
            } else {
                $this->msproduct->where('vendorid !=', null);
                $this->msproduct->where('vendorenabled', 1);
            }

            $get_category = $this->msproduct->get(array(
                'category' => $category,
                'userid' => getCurrentIdUser(),
            ));

            if ($get_category->num_rows() == 0) {
                $get_category = $this->mscategory->get(array(
                    'userid' => getCurrentIdUser(),
                    'name' => $category
                ));

                if ($get_category->num_rows() == 0) {
                    throw new Exception('Kategori produk tidak valid');
                } else {
                    $servicesubtype = $get_category->row()->servicesubtype;
                }
            } else {
                $servicesubtype = $get_category->row()->subcategory_apikey;
            }

            if ($name == null) {
                throw new Exception('Nama merek tidak boleh kosong');
            } else if ($productcategory == null) {
                throw new Exception('Kategori produk tidak boleh kosong');
            } else {
                $name = removeSymbol($name);
                $productcategory = removeSymbol($productcategory);
            }

            if (getCurrentUser()->licenseid == null) {
                $get = $this->msbrand->total(array(
                    'userid' => getCurrentIdUser()
                ));

                if ($get >= 5) {
                    throw new Exception('Anda telah mencapai batas maksimal 5 brand');
                }
            }

            $validate = $this->msbrand->total(array(
                'name' => $name,
                'category' => $category != null ? $category : $productcategory,
                'userid' => getCurrentIdUser()
            ));

            if ($validate > 0) {
                throw new Exception('Nama merek sudah ada');
            }

            $insert = array();
            $insert['name'] = $name;

            if ($category != null) {
                $insert['category'] = $category;
            } else {
                $insert['category'] = $productcategory;
            }

            $insert['userid'] = getCurrentIdUser();
            $insert['servicesubtype'] = $servicesubtype;

            $this->msbrand->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan brand');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Brand berhasil ditambahkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function select_brand()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $category = getPost('category');
            $selected = getPost('selected');

            if ($category == null) {
                throw new Exception('Kategori produk tidak boleh kosong');
            }

            $currentiduser = getCurrentIdUser();
            if (getCurrentUser()->multivendor != 1) {
                $brand = $this->db->query("SELECT a.brand AS name FROM ( SELECT brand FROM msproduct WHERE userid = $currentiduser AND vendor IS NOT NULL AND category = '$category' GROUP BY brand UNION SELECT NAME AS brand FROM msbrand WHERE userid = $currentiduser AND category = '$category' ) a WHERE a.brand IS NOT NULL ORDER BY a.brand ASC")->result();
            } else {
                $brand = $this->db->query("SELECT a.brand AS name FROM ( SELECT brand FROM msproduct WHERE userid = $currentiduser AND vendor IS NOT NULL AND vendorenabled = 1 AND category = '$category' GROUP BY brand UNION SELECT NAME AS brand FROM msbrand WHERE userid = $currentiduser AND category = '$category' ) a WHERE a.brand IS NOT NULL ORDER BY a.brand ASC")->result();
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/brand/select', array(
                    'brand' => $brand,
                    'selected' => $selected
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
