<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Tambah Produk Prabayar</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Produk</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Prabayar</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Tambah Produk Prabayar</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <a href="<?= base_url('product/prabayar') ?>" class="btn btn-danger fw-bold">Kembali</a>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <form id="frmAddProduct" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Kode Produk</label>
                                    <input type="text" class="form-control form-control-solid" name="kode_produk" placeholder="Kode Produk" disabled />
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Nama Produk</label>
                                    <input type="text" class="form-control form-control-solid" name="nama_produk" placeholder="Nama Produk" required />
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <div class="form-check form-check-custom form-check-solid mb-7">
                                            <input class="form-check-input" type="checkbox" name="isvendor" value="1" id="isvendor" onchange="changeIsvendor(this)" />

                                            <label class="form-check-label" for="isvendor">
                                                Dari Vendor
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-6 d-none" id="display_vendor">
                                        <div class="mb-7">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Vendor</label>
                                            <select class="form-select form-select-solid" name="vendor" id="vendor">
                                                <option value="" selected>- Pilih -</option>
                                                <?php foreach ($vendor as $key => $value) : ?>
                                                    <option value=" <?= $value->vendorid ?>"><?= $value->name ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0 d-flex justify-content-between">
                                        <span>
                                            Kategori Produk
                                        </span>

                                        <a href="javascript:;" onclick="addCategory()">
                                            <i class="fa fa-add text-primary"></i>
                                            Buat Kategori Baru
                                        </a>
                                    </label>

                                    <select class="form-select form-select-solid" name="kategori_produk" required>
                                        <option value="" selected>- Pilih -</option>
                                        <?php foreach ($category as $key => $value) : ?>
                                            <option value="<?= $value->category ?>"><?= $value->category ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0 d-flex justify-content-between">
                                        <span>
                                            Brand
                                        </span>

                                        <a href="javascript:;" onclick="addBrand()">
                                            <i class="fa fa-add text-primary"></i>
                                            Buat Brand Baru
                                        </a>
                                    </label>

                                    <select class="form-select form-select-solid" name="brand" required>
                                        <option value="" selected>- Pilih -</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Deskripsi</label>
                                    <textarea class="form-control form-control-solid" name="deskripsi" placeholder="Deskripsi"></textarea>
                                </div>
                            </div>

                            <div class="col-md-12" id="display_activate_stock">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <div class="form-check form-check-custom form-check-solid mb-7">
                                            <input class="form-check-input" type="checkbox" name="isstock" value="1" id="isstock" onchange="changeIsStock(this)" />

                                            <label class="form-check-label" for="isstock">
                                                Aktifkan Stok
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-6 d-none" id="display_stock">
                                        <div class="mb-7">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Stok</label>
                                            <input type="number" class="form-control form-control-solid" name="stock" placeholder="Stok">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12" id="display_activate_database">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <div class="form-check form-check-custom form-check-solid mb-7">
                                            <input class="form-check-input" type="checkbox" name="isdatabase" value="1" id="isdatabase" onchange="changeIsDatabase(this)" />

                                            <label class="form-check-label" for="isdatabase">
                                                Aktifkan Database
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-6 d-none" id="display_database">
                                        <div class="mb-7">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Database</label>
                                            <select class="form-select form-select-solid" name="database" id="database" onchange="changeDatabase(this)">
                                                <option value="" selected>- Pilih -</option>
                                                <option value="Stock Product">Stok Produk</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-12 d-none" id="display_database_stock">
                                        <div class="mb-7">
                                            <label class="col-form-label fw-semibold fs-6 pt-0">Stok Produk</label>
                                            <select class="form-select form-select-solid" name="stockproduct" id="stockproduct">
                                                <option value="" selected>- Pilih -</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6" id="display_harga_jual">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Harga Jual</label>
                                    <input type="text" class="form-control form-control-solid" name="harga_jual" placeholder="Harga Jual" required />
                                </div>
                            </div>

                            <div class="col-md-6" id="display_harga_vendor">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Harga Vendor</label>
                                    <input type="text" class="form-control form-control-solid" name="harga_vendor" placeholder="Harga Vendor" required />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function addCategory() {
        $.ajax({
            url: '<?= base_url('manage/category/product/add') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        });
    }

    function addBrand() {
        $.ajax({
            url: '<?= base_url('manage/brand/add') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                servicetype: 'PPOB'
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        });
    }

    window.onload = function() {
        $('select[name=kategori_produk]').change(function() {
            if ($(this).val() != '') {
                $.ajax({
                    url: '<?= base_url('manage/brand/select') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        category: $(this).val()
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            $('select[name=brand]').html(response.CONTENT);
                        }
                    }
                }).fail(function() {

                });
            }
        });

        $.AjaxRequest('#frmAddProduct', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success',
                    }).then(function(result) {
                        window.location.href = '<?= base_url('product/prabayar') ?>';

                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            }
        });
    };

    function changeIsvendor(element) {
        if (!element.checked) {
            $('input[name="kode_produk"]').prop('disabled', true);
            $('input[name="kode_produk"]').val('');
            $('input[name="kode_produk"]').removeAttr('required');
            $('#display_activate_stock').removeClass('d-none');
            $('#display_activate_database').removeClass('d-none');
            $('#display_vendor').addClass('d-none').find('select[name="vendor"]').removeAttr('required');

            $('select[name="vendor"]').find('option[value=""]').prop('selected', true);

            $('#display_harga_jual').removeClass('col-md-12').addClass('col-md-6');

            $('#display_harga_vendor').removeClass('d-none').find('input[name="harga_vendor"]').attr('required', true);
            if ($('#isstock').prop('checked')) {
                $('#display_stock').removeClass('d-none').find('input[name="stock"]').attr('required', true);
                $('#display_activate_database').addClass('d-none').find('select[name="database"]').removeAttr('required');
            }

            if ($('#isdatabase').prop('checked')) {
                $('#display_database').removeClass('d-none').find('select[name="database"]').attr('required', true);
                $('#display_activate_stock').addClass('d-none').find('input[name="stock"]').removeAttr('required');
            }
        } else {
            $('input[name="kode_produk"]').prop('disabled', false);
            $('input[name="kode_produk"]').attr('required', true);
            $('#display_activate_stock').addClass('d-none').find('input[name="stock"]').removeAttr('required');
            $('#display_activate_database').addClass('d-none').find('select[name="database"]').removeAttr('required');
            $('select[name="database"]').find('option[value=""]').prop('selected', true);
            $('select[name="stockproduct"]').html('<option value="">- Pilih -</option>');
            $('#display_harga_vendor').addClass('d-none').find('input[name="harga_vendor"]').removeAttr('required');
            $('#display_harga_jual').removeClass('col-md-6').addClass('col-md-12');
            $('#display_vendor').removeClass('d-none').find('select[name="vendor"]').attr('required', true);
        }
    }

    function changeIsStock(element) {
        if (element.checked) {
            $('#display_stock').removeClass('d-none');
            $('input[name="stock"]').attr('required', true);

            $('#display_activate_database').addClass('d-none').find('select[name="database"]').removeAttr('required');
        } else {
            $('#display_stock').addClass('d-none');
            $('input[name="stock"]').removeAttr('required');

            $('#display_activate_database').removeClass('d-none');
        }
    }

    function changeIsDatabase(element) {
        if (element.checked) {
            $('#display_database').removeClass('d-none');
            $('select[name="database"]').attr('required', true);

            $('#display_activate_stock').addClass('d-none').find('input[name="stock"]').removeAttr('required');
        } else {
            $('#display_database').addClass('d-none');
            $('#display_database_stock').addClass('d-none');
            $('select[name="database"]').removeAttr('required');
            $('select[name="stockproduct"]').removeAttr('required');

            $('select[name="database"]').find('option[value=""]').prop('selected', true);
            $('select[name="stockproduct"]').html('<option value="">- Pilih -</option>');

            $('#display_activate_stock').removeClass('d-none');
        }
    }

    function changeDatabase(these) {
        if (these.value == 'Stock Product') {
            $.ajax({
                url: '<?= base_url('select/database') ?>',
                method: 'POST',
                dataType: 'json',
                data: {
                    database: these.value
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {
                        $('#display_database_stock').removeClass('d-none');
                        $('select[name="stockproduct"]').attr('required', true);

                        $('#stockproduct').html(response.CONTENT);
                    } else {
                        return Swal.fire({
                            title: 'Gagal',
                            text: response.MESSAGE,
                            icon: 'error',
                        });
                    }
                }
            }).fail(function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            });
        }
    }
</script>