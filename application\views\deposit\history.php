<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Riwayat</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Deposit</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Riwayat</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="card mb-5">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-7">
                        <label for="depositdate" class="col-form-label fw-semibold fs-6 pt-0">Tanggal Deposit</label>
                        <input type="text" name="depositdate" id="depositdate" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Tanggal" value="<?= date('m/01/Y') ?> - <?= date('m/t/Y') ?>">
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="mb-7">
                        <label for="depositstatus" class="col-form-label fw-semibold fs-6 pt-0">Status Deposit</label>
                        <select name="depositstatus" id="depositstatus" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                            <option value="">Pilih Status</option>
                            <?php foreach ($status as $key => $value) : ?>
                                <option value="<?= $value->status ?>"><?= strtoupper($value->status) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="mb-7">
                        <label for="depositcode" class="col-form-label fw-semibold fs-6 pt-0">Kode Deposit</label>
                        <input type="text" name="depositcode" id="depositcode" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Kode Deposit">
                    </div>
                </div>

                <div class="col-md-12">
                    <button type="button" class="btn btn-dark w-100" onclick="filter()">Filter</button>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-5">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-row-bordered gy-5 datatables-history">
                    <thead>
                        <tr>
                            <th>Tanggal</th>
                            <th>Kode Topup</th>
                            <th>Pembayaran</th>
                            <th>Nominal</th>
                            <th>Catatan</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>

                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('input[name="transactiondate"]').daterangepicker();

        $('.datatables-history').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
            }
        });
    };

    function cancelTopup(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Apakah anda yakin ingin membatalkan permintaan deposit ini?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/cancel') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function filter() {
        let depositdate = $('input[name="depositdate"]').val();
        let depositstatus = $('#depositstatus').val();
        let depositcode = $('#depositcode').val();

        $('.datatables-history').DataTable().destroy();
        $('.datatables-history').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url('deposit/history/datatables') ?>',
                method: 'POST',
                data: {
                    date: depositdate,
                    status: depositstatus,
                    depositcode: depositcode,
                }
            }
        });
    }
</script>