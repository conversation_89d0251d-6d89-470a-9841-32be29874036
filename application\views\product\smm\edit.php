<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Ubah Produk Media Sosial</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Produk</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Media Sosial</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Ubah Produk Media Sosial</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <a href="<?= base_url('product/smm') ?>" class="btn btn-danger fw-bold">Kembali</a>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <form id="frmAddProduct" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Kode Produk</label>
                                    <input type="text" class="form-control form-control-solid" name="kode_produk" placeholder="Kode Produk" value="<?= $product->code ?>" required <?= $product->vendor != null ? 'disabled' : null ?> />
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Nama Produk</label>
                                    <input type="text" class="form-control form-control-solid" name="nama_produk" placeholder="Nama Produk" value="<?= $product->productname ?>" required />
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Deskripsi</label>
                                    <textarea class="form-control form-control-solid" name="deskripsi" placeholder="Deskripsi"><?= $product->description ?></textarea>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Status</label>
                                    <select class="form-select form-select-solid" name="status" required>
                                        <option value="" selected>- Pilih -</option>
                                        <option value="1" <?= $product->status == 1 ? 'selected' : null ?>>Aktif</option>
                                        <option value="0" <?= $product->status == 0 ? 'selected' : null ?>>Tidak Aktif</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="form-check form-check-custom form-check-solid mb-3">
                                    <input class="form-check-input" type="checkbox" name="dontupdate" value="1" id="dontupdate" <?= $product->dontupdate == 1 ? 'checked' : null ?> />

                                    <label class="form-check-label" for="dontupdate">
                                        Jangan update data (Nama Produk, Deskripsi dan Status) jika terjadi perubahan dari vendor
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $.AjaxRequest('#frmAddProduct', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success',
                    }).then(function(result) {
                        window.location.href = '<?= base_url('product/smm') ?>';

                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error',
                });
            }
        });
    };
</script>