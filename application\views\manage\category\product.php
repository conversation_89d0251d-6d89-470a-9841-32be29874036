<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Kategori Produk</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Management</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Kategori Produk</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <a href="javascript:;" onclick="add()" class="btn btn-dark fw-bold">Tambah Kategori Produk</a>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                    <div class="card-title">
                        <div class="d-flex align-items-center position-relative my-1">
                            <span class="svg-icon svg-icon-1 position-absolute ms-4"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1" transform="rotate(45 17.0365 15.1223)" fill="currentColor"></rect>
                                    <path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z" fill="currentColor"></path>
                                </svg>
                            </span>

                            <input type="text" placeholder="Pencarian" class="form-control form-control-solid w-300px ps-14" name="name" data-kt-ecommerce-product-filter="search" id="name" autocomplete="off">
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <table class="table table-striped table-row-bordered gy-5 datatables-category">
                        <thead>
                            <tr class="fw-semibold fs-6 text-muted">
                                <th>Icon</th>
                                <th>Nama Kategori</th>
                                <th>Kategori Usaha</th>
                                <th>Sub Kategori Usaha</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>

                        <tbody>
                            <?php foreach ($category as $key => $value) : ?>
                                <tr>
                                    <td>
                                        <?php if (!empty($value->asseturl)) : ?>
                                            <img src="<?= base_url('uploads/' . $value->asseturl) ?>" alt="<?= $value->category ?>" class="img-fluid" style="max-width: 40px; max-height: 40px;">
                                        <?php else : ?>
                                            <span class="badge badge-light-primary">No Icon</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= $value->category ?></td>
                                    <td><?= $value->category_apikey ?></td>
                                    <td><?= $value->subcategory_apikey ?? '- Tidak diketahui -' ?></td>
                                    <td>
                                        <a href="javascript:;" onclick="editDetail('<?= $value->category ?>', '<?= $value->category_apikey ?>')" class="btn btn-icon btn-warning btn-sm mb-1 me-1">
                                            <i class="fa fa-edit"></i>
                                        </a>

                                        <?php if ($value->dontupdatecategory == 1) : ?>
                                            <a href="javascript:;" class="btn btn-icon btn-danger btn-sm mb-1 me-1" onclick="restoreCategory('<?= $value->category ?>', '<?= $value->oldcategoryname ?>')">
                                                <i class="fa fa-undo"></i>
                                            </a>
                                        <?php endif; ?>

                                        <?php if ($value->isbrand || $value->category_apikey == 'PPOB') : ?>
                                            <a href="<?= base_url('manage/category/product/brand/' . stringEncryption('encrypt', $value->category)) ?>" class="btn btn-warning btn-sm mb-1 me-1">
                                                Brand
                                            </a>
                                        <?php endif; ?>

                                        <?php if ($value->disabledid == null && $value->tables == 'msproduct') : ?>
                                            <button type="button" class="btn btn-danger btn-sm mb-1 me-1" onclick="disableCategory('<?= $value->category ?>', '<?= $value->category_apikey ?>')">
                                                <i class="fa fa-times"></i>
                                                <span>Disable</span>
                                            </button>
                                        <?php elseif ($value->disabledid != null && $value->tables == 'msproduct') : ?>
                                            <button type="button" class="btn btn-primary btn-sm mb-1 me-1" onclick="enableCategory('<?= $value->disabledid ?>', '<?= $value->category ?>')">
                                                <i class="fa fa-check"></i>
                                                <span>Enable</span>
                                            </button>
                                        <?php endif; ?>

                                        <?php if ($value->tables == 'mscategory') : ?>
                                            <a href="javascript:;" class="btn btn-icon btn-danger btn-sm mb-1" onclick="deleteCategory('<?= stringEncryption('encrypt', $value->id) ?>')">
                                                <i class="fa fa-trash"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('.datatables-category').DataTable({
            responsive: true,
            stateSave: true,
            ordering: false,
        });

        $('#name').on('keyup', function() {
            $('.datatables-category').DataTable().search(this.value).draw();
        });
    };

    function disableCategory(category, api) {
        return Swal.fire({
            title: 'Pernyataan',
            text: `Apakah anda yakin ingin menonaktifkan kategori ${category}?`,
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/disable') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        category: category,
                        api: api
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function enableCategory(disabledid, category) {
        return Swal.fire({
            title: 'Pernyataan',
            text: `Apakah anda yakin ingin mengaktifkan kategori ${category}?`,
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/enable') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: disabledid
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function editDetail(category, apikey) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/edit') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                category: category,
                api: apikey
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    }

    function deleteCategory(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Data yang dihapus tidak dapat dikembalikan',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/delete') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function add() {
        $.ajax({
            url: '<?= base_url('manage/category/product/add') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error',
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error',
            });
        });
    }

    function restoreCategory(category, oldcategory) {
        return Swal.fire({
            title: 'Pernyataan',
            text: `Apakah anda yakin ingin mengembalikan kategori ${category} menjadi ${oldcategory}?`,
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/restore') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        category: category
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }
</script>