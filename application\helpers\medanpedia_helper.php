<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MedanPedia
{
    private $_apiid;
    private $_apikey;

    public function __construct($apiid, $apikey)
    {
        $this->_apiid = $apiid;
        $this->_apikey = $apikey;
    }

    public function services()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://api.medanpedia.co.id/services");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_id=$this->_apiid&api_key=$this->_apikey");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function profile()
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://api.medanpedia.co.id/profile");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_id=$this->_apiid&api_key=$this->_apikey");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function order($service, $target, $quantity)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://api.medanpedia.co.id/order");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_id=$this->_apiid&api_key=$this->_apikey&service=$service&target=$target&quantity=$quantity");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }

    public function status($id)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://api.medanpedia.co.id/status");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "api_id=$this->_apiid&api_key=$this->_apikey&id=$id");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    }
}
