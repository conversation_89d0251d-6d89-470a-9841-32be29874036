<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Deposits extends MY_Model
{
    protected $table = 'deposit';
    protected $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.*, b.name AS buyer_name')
            ->from($this->table . ' a')
            ->join('msusers b', 'b.id = a.userid')
            ->order_by('a.id', 'DESC');

        return $this;
    }
}
