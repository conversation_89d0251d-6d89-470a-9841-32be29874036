<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsPlatformSosmed extends MY_Model
{
    protected $table = 'msplatformsosmed';
    public $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.id, a.name, b.asseturl');
        $this->db->from($this->table . ' a');
        $this->db->join('msicons b', 'b.id = a.assetid');

        return $this->db;
    }
}
