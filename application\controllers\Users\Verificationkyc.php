<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property msuserskyc $msuserskyc
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver $db
 * @property MsUsers $msusers
 */

class Verificationkyc extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsUsersKyc', 'msuserskyc');
        $this->load->model('MsUsers', 'msusers');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Verifikasi KYC';
        $data['content'] = 'user/verificationkyc/index';

        return $this->load->view('master', $data);
    }

    public function datatables_verificationkyc()
    {
        try {
            if (!isLogin()) {
                return JSONResponseDefault('OK', 'Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                return JSONResponseDefault('OK', 'Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                return JSONResponseDefault('OK', 'Anda tidak dapat mengakses fitur ini');
            }

            $datatable = $this->datatables->make('MsUsersKyc', 'QueryDatatables', 'SearchDatatables');
            $data = array();

            foreach (
                $datatable->getData(array(
                    'b.merchantid' => getCurrentIdUser()
                )) as $key => $value
            ) {
                $detail = array();
                $actions = "<button type=\"button\" class=\"btn btn-info btn-sm mb-1\" onclick=\"detailKYC('" . stringEncryption('encrypt', $value->id) . "')\">
                    <i class=\"fa fa-eye\"></i>
                    <span>Detail</span>
                </button>";

                // Set status display
                if ($value->status == 0) {
                    $statusDisplay = "<span class=\"badge badge-warning\">Pending</span>";
                } else if ($value->status == 1) {
                    $statusDisplay = "<span class=\"badge badge-success\">Terverifikasi</span>";
                } else if ($value->status == 2) {
                    $statusDisplay = "<span class=\"badge badge-danger\">Ditolak</span>";
                }

                $detail[] = $value->name;
                $detail[] = $value->nik;
                $detail[] = $value->biologicalmother;
                $detail[] = $statusDisplay;
                $detail[] = $actions;

                $data[] = $detail;
            }

            return $datatable->json($data);
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_verificationkyc()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $action = getPost('action');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            // Dapatkan data KYC
            $kyc_data = $this->msuserskyc->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.id' => $id,
                    'a.status' => 0,
                    'b.merchantid' => getCurrentIdUser()
                ));

            if ($kyc_data->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $kyc_row = $kyc_data->row();
            $user_id = $kyc_row->userid; // ID pengguna yang mengajukan KYC

            $update = array();
            $user_update = array(); // Array untuk update tabel msusers

            if ($action == 'accept') {
                // Menyetujui verifikasi KYC
                $update['status'] = 1;
                $user_update['is_kyc'] = 1; // Set status KYC pengguna menjadi terverifikasi
                $message = 'Verifikasi KYC berhasil disetujui';
            } else if ($action == 'reject') {
                // Menolak verifikasi KYC
                $update['status'] = 2;
                $user_update['is_kyc'] = 2; // Set status KYC pengguna menjadi ditolak
                $message = 'Verifikasi KYC berhasil ditolak';
            } else {
                throw new Exception('Aksi tidak valid');
            }

            // Update status di tabel msuserskyc
            $this->msuserskyc->update(array(
                'id' => $id
            ), $update);

            // Update status is_kyc di tabel msusers
            if (!empty($user_id)) {
                $this->msusers->update(array(
                    'id' => $user_id
                ), $user_update);
            }

            if ($this->db->trans_status() === FALSE) {
                $this->db->trans_rollback();

                throw new Exception('Terjadi kesalahan saat memproses data');
            } else {
                $this->db->trans_commit();

                return JSONResponseDefault('OK', $message);
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function detail($id)
    {
        try {
            if (!isLogin()) {
                return redirect(base_url('auth/login'));
            } else if (!isUser()) {
                return redirect(base_url('dashboard'));
            } else if (getCurrentUser()->licenseid == null) {
                return redirect(base_url('dashboard'));
            }

            if ($id == null) {
                return redirect(base_url('users/verificationkyc'));
            }

            // Dekripsi ID jika dienkripsi
            $decrypted_id = stringEncryption('decrypt', $id);

            // Ambil data KYC
            $kyc_data = $this->msuserskyc->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.id' => $decrypted_id,
                    'b.merchantid' => getCurrentIdUser()
                ));

            if ($kyc_data->num_rows() == 0) {
                $this->session->set_flashdata('error', 'Data KYC tidak ditemukan');

                return redirect(base_url('users/verificationkyc'));
            }

            $kyc = $kyc_data->row();

            // Ambil data user
            $user_data = $this->msusers->get(array(
                'id' => $kyc->userid
            ));

            $user = $user_data->num_rows() > 0 ? $user_data->row() : null;

            $data = array();
            $data['title'] = 'Detail Verifikasi KYC';
            $data['content'] = 'user/verificationkyc/detail';
            $data['kyc'] = $kyc;
            $data['user'] = $user;
            $data['id_encrypted'] = $id; // ID terenkripsi untuk digunakan di form

            return $this->load->view('master', $data);
        } catch (Exception $ex) {
            $this->session->set_flashdata('error', $ex->getMessage());

            return redirect(base_url('users/verificationkyc'));
        }
    }
}
